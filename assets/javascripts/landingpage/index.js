
function openNav() {
    document.getElementById("ws-mySidenav").style.width = "100%";
    $('.ws-mob-menu').show();

}

function closeNav() {
    document.getElementById("ws-mySidenav").style.width = "0";
    $('.ws-mob-menu').hide();
}
$(document).ready(function() {
    console.log('first');
    var popularbooks = $('#popularBooks');
    var topBooks = $('#latestReleases');
    var videos = $('#videos-latest');
    var currentAffairs=$('#popularQuiz');
    popularbooks.slick({
        dots: false,
        infinite: false,
        speed: 300,
        slidesToShow: 5,
        slidesToScroll: 1,
        responsive: [
            {
                breakpoint: 1025,
                settings: {
                    slidesToShow:4,
                    slidesToScroll: 2,
                }
            },

            {
                breakpoint: 768,
                settings: {
                    slidesToShow: 4,
                    slidesToScroll: 1
                }
            },
            {
                breakpoint: 769,
                settings: {
                    slidesToShow: 4,
                    slidesToScroll: 1
                }
            },
            {
                breakpoint: 600,
                settings: {
                    slidesToShow: 1.5,
                    slidesToScroll: 1
                }
            },
            {
                breakpoint: 480,
                settings: {
                    slidesToShow: 1.2,
                    slidesToScroll: 1
                },

            },
            {
                breakpoint: 481,
                settings: {
                    slidesToShow: 1.2,
                    slidesToScroll: 1
                },

            }
        ]
    });
    videos.slick({
        dots: false,
        infinite: false,
        speed: 300,
        slidesToShow: 3,
        slidesToScroll: 1,
        responsive: [
            {
                breakpoint: 1025,
                settings: {
                    slidesToShow:3,
                    slidesToScroll: 2,
                }
            },

            {
                breakpoint: 768,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 1
                }
            },
            {
                breakpoint: 769,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 1
                }
            },
            {
                breakpoint: 600,
                settings: {
                    slidesToShow: 1.5,
                    slidesToScroll: 1
                }
            },
            {
                breakpoint: 480,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1
                },

            },
            {
                breakpoint: 481,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1
                },

            }
        ]
    });
    topBooks.slick({
        dots: false,
        infinite: false,
        speed: 300,
        slidesToShow: 5,
        slidesToScroll: 1,
        responsive: [
            {
                breakpoint: 1025,
                settings: {
                    slidesToShow:4,
                    slidesToScroll: 2,
                }
            },

            {
                breakpoint: 768,
                settings: {
                    slidesToShow: 4,
                    slidesToScroll: 1
                }
            },
            {
                breakpoint: 769,
                settings: {
                    slidesToShow: 4,
                    slidesToScroll: 1
                }
            },
            {
                breakpoint: 600,
                settings: {
                    slidesToShow: 1.5,
                    slidesToScroll: 1
                }
            },
            {
                breakpoint: 480,
                settings: {
                    slidesToShow: 1.2,
                    slidesToScroll: 1
                },

            },
            {
                breakpoint: 481,
                settings: {
                    slidesToShow: 1.2,
                    slidesToScroll: 1
                },

            }
        ]
    });

    currentAffairs.slick({
        dots: false,
        infinite: false,
        speed: 300,
        slidesToShow: 4,
        slidesToScroll: 1,
        responsive: [
            {
                breakpoint: 1025,
                settings: {
                    slidesToShow:4,
                    slidesToScroll: 2,
                }
            },

            {
                breakpoint: 768,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 1
                }
            },
            {
                breakpoint: 769,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 1
                }
            },
            {
                breakpoint: 600,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1
                }
            },
            {
                breakpoint: 480,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1
                },

            },
            {
                breakpoint: 481,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1
                },

            }
        ]
    });
    $("#filter-mode").on("show", function () {
        $("body").addClass("modal-open");
    }).on("hidden", function () {
        $("body").removeClass("modal-open")
    });
    $("#mobile-menu > li > a").click(function () {
        $('ul.sub-menu').not($(this).siblings()).slideUp().parent("li").removeClass('toggle-icon');
        $(this).siblings("ul.sub-menu").slideToggle(
            'slow',function(){
                $('#mobile-menu > li').toggleClass('default-icon',$(this).is(':visible'));
            }
        ).parent("li").addClass("toggle-icon");
    });
    $('#login,#back-login').click(function () {
        $('#loginSignup').attr('data-page', 'login');
        console.log('hello');
    });
    $('#sign-up').click(function () {
        $('#loginSignup').attr('data-page', 'signup');
    });
    $('#forgot-paswd').click(function () {
        $('#loginSignup').attr('data-page', 'reset');
    });
    $('#back-btn').click(function () {
        $('#loginSignup').attr('data-page', 'login-google');
    });

    $(window).scroll(function() {
        var y = $(this).scrollTop();
        if (y > 150) {
            $('html').find('.mobile-menu').fadeIn();
        } else {
            $('html').find('.mobile-menu').fadeOut();
        }
    });
    $('.mark-complete').click(function(){
        $(this).find('span').text($(this).find('span').text() == 'Mark as Complete' ? 'Completed' : 'Mark as Complete');
        $(this).find('i').text($(this).find('i').text() == 'check_circle_outline' ? 'check_circle' : 'check_circle_outline');
        $(this).toggleClass('complete');
    });

    $('.mobile-menu ul li a').each(function () {
        if (window.location.pathname.indexOf($(this).attr('href')) > -1) {
            $('.mobile-menu ul li:first-child > a').removeClass('active');
            $(this).closest('li > a').addClass('active');
            return false;
        }
        else{
            $('.mobile-menu ul li:first-child > a').addClass('active');
        }
    });

});


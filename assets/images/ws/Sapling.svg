<svg width="115" height="197" viewBox="0 0 115 197" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0)" filter="url(#filter0_d)">
<path d="M12.0801 161.34L58.8001 188.32V175.69L12.0801 148.72V161.34Z" fill="#5C2C1B"/>
<path d="M105.51 161.34L58.8 188.32V175.69L105.51 148.72V161.34Z" fill="#452116"/>
<path d="M105.51 148.72L58.8 175.69V172.19L105.51 145.22V148.72Z" fill="#4C6426"/>
<path d="M12.0801 148.72L58.8001 175.69V172.19L12.0801 145.22V148.72Z" fill="#71912C"/>
<path d="M12.0801 145.22L58.8001 172.19L105.51 145.22L58.8001 118.25L12.0801 145.22Z" fill="#92AE24"/>
<g style="mix-blend-mode:multiply" opacity="0.23">
<path d="M58.4899 154.72C70.6844 154.72 80.5699 150.019 80.5699 144.22C80.5699 138.421 70.6844 133.72 58.4899 133.72C46.2955 133.72 36.4099 138.421 36.4099 144.22C36.4099 150.019 46.2955 154.72 58.4899 154.72Z" fill="#828383"/>
</g>
<path d="M69.9401 144.39C69.9401 147.14 64.9401 149.39 58.7501 149.39C52.5601 149.39 47.5701 147.17 47.5701 144.39C47.5701 141.61 52.5701 139.39 58.7501 139.39C64.9301 139.39 69.9401 141.64 69.9401 144.39Z" fill="#312313"/>
<path d="M41.7101 95.6001L55.0002 113.71C55.0879 113.862 55.1341 114.035 55.1341 114.21C55.1341 114.386 55.0879 114.558 55.0002 114.71C54.8171 115.073 54.5637 115.396 54.2547 115.661C53.9457 115.925 53.5872 116.125 53.2002 116.25L39.9202 98.1101C40.307 97.9849 40.6652 97.7844 40.9741 97.5201C41.2831 97.2557 41.5366 96.9329 41.7202 96.5701C41.8079 96.418 41.8541 96.2456 41.8541 96.0701C41.8541 95.8945 41.8079 95.7221 41.7202 95.5701L41.7101 95.6001Z" fill="#312313"/>
<path d="M39.9201 98.1101L53.1901 116.21C53.0548 116.259 52.9135 116.289 52.7701 116.3C52.6023 116.334 52.4288 116.324 52.2659 116.271C52.1031 116.219 51.9565 116.126 51.8401 116L38.5701 97.8901C38.6874 98.0172 38.8357 98.1116 39.0005 98.1642C39.1652 98.2167 39.3408 98.2257 39.5101 98.1901C39.6493 98.1789 39.7868 98.1521 39.9201 98.1101Z" fill="#3E2B14"/>
<path d="M40.77 95.3001C41.65 95.2001 42.07 95.7701 41.77 96.5801C41.5567 97.0062 41.2451 97.3754 40.8608 97.6571C40.4765 97.9388 40.0305 98.125 39.56 98.2001C38.69 98.3001 38.26 97.7301 38.61 96.9301C38.8172 96.5082 39.1206 96.1408 39.4958 95.8577C39.871 95.5746 40.3074 95.3836 40.77 95.3001Z" fill="#372614"/>
<path d="M62.3401 68.1101L65.6001 143.46C65.5488 144.03 65.3467 144.576 65.0146 145.042C64.6824 145.507 64.2321 145.876 63.7101 146.11C61.5601 147.36 57.0701 147.55 54.5601 146.7L55.2401 71.4401C58.1154 72.2933 61.2039 72.0621 63.9201 70.7901C64.4421 70.5561 64.8924 70.1873 65.2245 69.7215C65.5567 69.2557 65.7588 68.7099 65.8101 68.1401L62.3401 68.1101Z" fill="#362316"/>
<path d="M56.0001 71.4001L54.5601 146.72C54.1094 146.564 53.6742 146.367 53.2601 146.13C52.7294 145.894 52.2715 145.519 51.934 145.046C51.5964 144.573 51.3913 144.019 51.3401 143.44L52.7801 68.1101C52.831 68.6878 53.036 69.2412 53.3737 69.7126C53.7115 70.184 54.1695 70.5561 54.7001 70.7901C55.1122 71.0306 55.5478 71.2282 56.0001 71.3801V71.4001Z" fill="#523928"/>
<path d="M63.89 65.4701C66.45 66.9501 66.45 69.3401 63.89 70.8101C62.4628 71.5395 60.8828 71.9199 59.28 71.9199C57.6772 71.9199 56.0972 71.5395 54.67 70.8101C52.12 69.3401 52.1 66.9501 54.67 65.4701C56.0987 64.7454 57.678 64.3677 59.28 64.3677C60.882 64.3677 62.4614 64.7454 63.89 65.4701Z" fill="#372614"/>
<path d="M58.25 105.07C87.0848 105.07 110.46 81.6948 110.46 52.86C110.46 24.0252 87.0848 0.650024 58.25 0.650024C29.4153 0.650024 6.04004 24.0252 6.04004 52.86C6.04004 81.6948 29.4153 105.07 58.25 105.07Z" fill="#7AA82B"/>
<g style="mix-blend-mode:multiply" opacity="0.49">
<path d="M97.4601 18.3201C99.3445 26.7928 99.2998 35.5807 97.3295 44.0338C95.3591 52.487 91.5135 60.3889 86.077 67.1552C80.6406 73.9215 73.7524 79.3789 65.9221 83.1238C58.0918 86.8687 49.5198 88.8052 40.8401 88.7901C32.1819 88.784 23.6366 86.8254 15.8401 83.0601C19.9373 88.8459 25.1709 93.7359 31.2212 97.4315C37.2714 101.127 44.0115 103.551 51.0297 104.555C58.048 105.558 65.1972 105.121 72.0408 103.269C78.8844 101.418 85.2788 98.1908 90.8335 93.7853C96.3883 89.3799 100.987 83.8885 104.348 77.6465C107.71 71.4044 109.764 64.5427 110.385 57.4803C111.006 50.4179 110.181 43.303 107.961 36.57C105.74 29.8371 102.171 23.6274 97.4701 18.3201H97.4601Z" fill="#7AA82B"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.33">
<path d="M77.6899 4.39003C89.0499 9.17003 70.35 33.9 53.32 47.1C36.2899 60.3 8.99994 74.32 6.69994 60.29C5.2663 51.2222 6.30201 41.9356 9.6982 33.4064C13.0944 24.8772 18.7256 17.4205 25.9999 11.82C42.2799 -0.749977 63.7599 -1.45997 77.6899 4.39003Z" fill="#7AA82B"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M20.44 16.27C20.61 16.38 20.53 17.02 19.92 16.88C19.31 16.74 20.07 16.04 20.44 16.27Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M31.51 22.19C31.69 22.3 31.6 22.9301 30.99 22.8001C30.38 22.6701 31.15 21.96 31.51 22.19Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M24.53 33.9901C24.71 34.1001 24.62 34.7401 24.01 34.6001C23.4 34.4601 24.17 33.7701 24.53 33.9901Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M9.00007 30.11C9.18007 30.22 9.09006 30.86 8.48006 30.72C7.87006 30.58 8.60007 29.86 9.00007 30.11Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M57.9999 0.0500351C58.1799 0.160035 58.0899 0.80002 57.4799 0.66002C56.8699 0.52002 57.6099 -0.179965 57.9999 0.0500351Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M66 10.92C66.17 11.03 66.09 11.67 65.48 11.53C64.87 11.39 65.62 10.7 66 10.92Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M60.0599 24.7501C60.2399 24.8601 60.16 25.5001 59.54 25.3601C58.92 25.2201 59.6999 24.5301 60.0599 24.7501Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M43.2599 29.6101C43.4299 29.7301 43.35 30.3601 42.73 30.2301C42.11 30.1001 42.8899 29.3901 43.2599 29.6101Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M40.3399 45.11C40.5199 45.22 40.4299 45.85 39.8199 45.72C39.2099 45.59 39.9999 44.9 40.3399 45.11Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M22.9199 47.7401C23.0999 47.8501 23.0099 48.4901 22.3999 48.3501C21.7899 48.2101 22.5599 47.5101 22.9199 47.7401Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M14.53 59.33C14.71 59.44 14.62 60.08 14.01 59.94C13.4 59.8 14.17 59.11 14.53 59.33Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M32.7 62.31C32.88 62.42 32.7899 63.06 32.1799 62.92C31.5699 62.78 32.34 62.11 32.7 62.31Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M43.48 71.39C43.66 71.5 43.57 72.13 42.96 72C42.35 71.87 43.12 71.16 43.48 71.39Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M56.3301 86.5901C56.5001 86.7001 56.4201 87.3301 55.8101 87.2001C55.2001 87.0701 56.0001 86.3601 56.3301 86.5901Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M70.6399 86.81C70.8199 86.92 70.7299 87.56 70.1199 87.42C69.5099 87.28 70.2799 86.58 70.6399 86.81Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M85.7099 76.5801C85.8899 76.6901 85.7999 77.33 85.1899 77.19C84.5799 77.05 85.3499 76.3501 85.7099 76.5801Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M73.78 66.33C73.96 66.44 73.87 67.08 73.26 66.94C72.65 66.8 73.42 66.11 73.78 66.33Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M59.53 70.11C59.71 70.22 59.62 70.86 59.01 70.72C58.4 70.58 59.17 69.9 59.53 70.11Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M49.54 56.76C49.72 56.87 49.63 57.51 49.02 57.37C48.41 57.23 49.18 56.54 49.54 56.76Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M67.7301 50.02C67.9001 50.13 67.8201 50.77 67.2101 50.64C66.6001 50.51 67.3601 49.8 67.7301 50.02Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M58.4 40.57C58.58 40.68 58.49 41.3201 57.88 41.1801C57.27 41.0401 58 40.35 58.4 40.57Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M76.6899 34.99C76.8699 35.1 76.7799 35.74 76.1699 35.61C75.5599 35.48 76.3299 34.77 76.6899 34.99Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M84.6401 51.52C84.8201 51.63 84.74 52.27 84.12 52.14C83.5 52.01 84.2801 51.3 84.6401 51.52Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M93.66 33.7301C93.83 33.8401 93.75 34.4801 93.14 34.3401C92.53 34.2001 93.29 33.5001 93.66 33.7301Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M79.8399 21.7401C80.0099 21.8501 79.9299 22.4901 79.3199 22.3501C78.7099 22.2101 79.4699 21.5101 79.8399 21.7401Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M84.4601 6.75011C84.6401 6.86011 84.5601 7.50009 83.9401 7.36009C83.3201 7.22009 84.1001 6.52011 84.4601 6.75011Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M96.4 19.9001C96.57 20.0101 96.49 20.6501 95.88 20.5101C95.27 20.3701 96 19.6801 96.4 19.9001Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M107.9 39.11C108.08 39.22 107.99 39.86 107.38 39.72C106.77 39.58 107.54 38.83 107.9 39.11Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M92.5901 63.8401C92.7701 63.9501 92.6801 64.5901 92.0701 64.4501C91.4601 64.3101 92.2301 63.6101 92.5901 63.8401Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M99.31 49.67C99.49 49.78 99.4 50.42 98.79 50.28C98.18 50.14 99 49.45 99.31 49.67Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M106.42 64.33C106.6 64.44 106.51 65.08 105.9 64.94C105.29 64.8 106.06 64.11 106.42 64.33Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M99.31 82.77C99.49 82.88 99.4 83.52 98.79 83.38C98.18 83.24 99 82.54 99.31 82.77Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M67.5 102.11C67.68 102.22 67.59 102.86 66.98 102.73C66.37 102.6 67.14 101.85 67.5 102.11Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M83.9999 95.45C84.1799 95.56 84.0899 96.2001 83.4799 96.0601C82.8699 95.9201 83.6099 95.23 83.9999 95.45Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M38.2599 86.1101C38.4399 86.2201 38.3599 86.8501 37.7399 86.7201C37.1199 86.5901 37.8999 85.9201 38.2599 86.1101Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M49.31 101.28C49.49 101.39 49.4 102.03 48.79 101.89C48.18 101.75 49 101.05 49.31 101.28Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M34.2999 98.62C34.4699 98.73 34.3899 99.37 33.7799 99.23C33.1699 99.09 33.9299 98.39 34.2999 98.62Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M25.45 74.56C25.63 74.67 25.5399 75.31 24.9299 75.18C24.3199 75.05 25.09 74.34 25.45 74.56Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M19.1899 87.52C19.3699 87.63 19.2799 88.27 18.6699 88.14C18.0599 88.01 18.8299 87.3 19.1899 87.52Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M10.0001 74.11C10.1701 74.22 10.0901 74.86 9.48005 74.72C8.87005 74.58 9.66006 73.9 10.0001 74.11Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M4.74999 47.2601C4.92999 47.3701 4.83999 48.0101 4.22999 47.8701C3.61999 47.7301 4.38999 47.0301 4.74999 47.2601Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M34.78 4.90001C34.96 5.01001 34.87 5.65 34.26 5.51C33.65 5.37 34.42 4.67001 34.78 4.90001Z" fill="white"/>
</g>
<g style="mix-blend-mode:screen" opacity="0.71">
<path d="M46.4 14.43C46.57 14.54 46.49 15.18 45.88 15.05C45.27 14.92 46 14.21 46.4 14.43Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_d" x="0" y="0" width="114.59" height="196.32" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<clipPath id="clip0">
<rect width="106.59" height="188.32" fill="white" transform="translate(4)"/>
</clipPath>
</defs>
</svg>

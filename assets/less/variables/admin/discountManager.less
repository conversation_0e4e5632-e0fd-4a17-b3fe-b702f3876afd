.discount-manager {
  .container-fluid {
    @media (min-width: 1920px) {
      width: 80%;
    }
  }
  input[type="radio"]:focus + label {
    border: none;
  }
  input[type="radio"]:checked + label {
    color: inherit;
    background-color: transparent;
  }

  .status-filter-toggle {
    position: relative;
    z-index: 1;
    width: 350px;
    margin: 0 auto -30px;
    .form-check {
      input, label {
        cursor: pointer;
      }
    }
  }

  #discountList {
    table {
      tr {
        th:first-child, th:nth-child(3), th:nth-child(4), th:nth-child(5), th:nth-child(6), th:nth-child(7) {
          width: 85px !important;
        }
        th:nth-child(2), th:nth-child(8) {
          width: 70px !important;
        }
        th:nth-child(9), th:nth-child(10) {
          width: 100px !important;
        }
        th:nth-child(11) {
          width: 50px !important;
        }
        th:nth-child(12), th:last-child {
          width: 60px !important;
        }
      }
    }
  }

  #couponCode::-webkit-input-placeholder {
    text-transform: initial;
  }
  #couponCode:-moz-placeholder {
    text-transform: initial;
  }
  #couponCode::-moz-placeholder {
    text-transform: initial;
  }
  #couponCode:-ms-input-placeholder {
    text-transform: initial;
  }
}
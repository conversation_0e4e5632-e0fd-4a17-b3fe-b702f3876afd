
@iPad-portrait: ~"only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait)";
@iPad-landscape: ~"only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : landscape)";
@iPhone: ~"only screen and (min-device-width : 320px) and (max-device-width : 480px)";
@iPhone5-landscape:~"only screen and (min-device-width: 320px) and (max-device-width: 568px) and (orientation : landscape)";
@iPhone5-portrait:~"only screen and (min-device-width: 320px) and (max-device-width: 568px) and (orientation : portrait)";
@iPhone6-landscape:~"only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape)";
@iPhone6-portrait:~"only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait)";
@iPhone7-landscape:~"only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : landscape)";
@iPhone7-portrait:~"only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : portrait)";
@iphoneX-landscape:~"only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape)";
@iphoneX-portrait:~"only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait)";
@reset-bootstrap:~"(min-width: 576px)";

@iPad-pro:~"only screen and (min-width: 1024px) and (max-height: 1366px) and (-webkit-min-device-pixel-ratio: 1.5) and (hover: none)";

@extraSmall: ~"(max-width: 575px)";
@small: ~"(min-width: 576px) and (max-width: 767px)";
@medium: ~"(min-width: 768px) and (max-width: 991px)";
@large: ~"(min-width: 992px) and (max-width: 1199px)";
@extraLarge: ~"(min-width: 1200px)";



/* App In App Template Styles */

// Components
@import "components/alerts.less";
@import "components/buttons.less";
@import "components/cards.less";
@import "components/data_tables.less";
@import "components/datepicker.less";
@import "components/dropdowns.less";
@import "components/form_elements.less";
@import "components/form_errors.less";
@import "components/modals.less";
@import "components/tables.less";
@import "components/typography.less";
@import "components/toggleswitch.less";
@import "components/chart.less";
@import "components/sweet_alerts.less";

// Admin
@import "admin/admin.less";

// Common
@import "common/preloader.less";
@import "common/page_common.less";

// Pages Styles
@import "pages/_books-list.less"; // Books list section (e.g. Store books)
@import "pages/_related-books.less"; // Related and best seller books section
@import "pages/_resources.less"; // Resources (e.g. Notes, MCQ, Flashcards)

@import "pages/ebooks.less"; // eBooks store page
@import "pages/ebook_detail.less"; // eBook detail page

@import "pages/mylibrary.less"; // My library page
@import "pages/access_code.less"; // Access code page

.app_in_app .mdl-layout__header{
  background-color: @theme-primary-color !important;
}
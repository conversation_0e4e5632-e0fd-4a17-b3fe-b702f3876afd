@import "../theme/colors.less";
@import "../theme/fonts.less";
@import "../theme/responsive.less";

.aboutus {
  p{
    font-size: 16px;
    @media @extraSmallDevices,@smallDevices {
      font-size: 15px;
    }
  }
  .sectionOne{
  margin-top: 3rem;
  @media @extraSmallDevices, @smallDevices {
    margin-top: 2rem;
  }
    .video-detail{
      position: relative;
      @media @extraSmallDevices, @smallDevices{
        position: inherit;
      }
    }
  .video-section {
    display: flex;
    align-items: center;
    @media @extraSmallDevices, @smallDevices {
      display: grid;
    }
 h2{
   @media @extraSmallDevices, @smallDevices {
     margin-top: 1rem;
   }
 }
    h4 {
      color: @orange;
    }

    button {
      margin-top: 1rem;
      border: none;
      color: @white;
      background-color: @orange;
      border-radius: 16px;
      height: 30px;
      width: 150px;
      font-size: 16px;
    }

    hr {
      width: 50px;
      height: 5px;
      background-color: @orange;
    }
  }

    .left-div {
      display: inline-block;
      position: relative;

      img {
        max-width: 100%;
        //margin-left: 30px;
        border-radius: 16px;
      }

    }

}
section{
  margin-top: 3rem;
  @media @extraSmallDevices, @smallDevices {
    margin-top: 0;
  }
}
  .sectionTwo{
    @media @mediumDevices{
      margin-top: 0px;
    }
    .card{
      background-color: transparent;
      border: none;
      @media @extraSmallDevices, @smallDevices{
        margin-bottom: 2rem;
      }
      .card-body{
        @media @extraSmallDevices, @smallDevices{
          align-self: center;
        }
      }
      img{
        height: 150px;
      }
      p{
        @media @extraSmallDevices, @smallDevices{
          text-align: center;
        }
      }
      hr{
        border: 2px solid @orange;
        width: 30px;
        @media @extraSmallDevices, @smallDevices{
          margin: 10px auto;
        }
      }
      .card-title{
        color: @orange;
        @media @extraSmallDevices, @smallDevices{
          text-align: center;
        }
      }
      .card-text{
        height: 60px;
        @media @extraSmallDevices, @smallDevices{
          height: auto;
        }
        @media @mediumDevices{
          height: 70px;
        }
      }
      a{
        background-color: @orange;
        border-radius: 16px;
        border: none;
        @media @extraSmallDevices, @smallDevices{
          margin-top: 10;
          margin-left: 35%;
        }
      }
    }
  }
  .sectionThree{
    .row{
      margin-right: 0;
      margin-left: 0;
    }
    .card{
      text-align: center;
      min-height: 150px;
      border-radius: 14px;
      @media @smallDevices, @extraSmallDevices{
        margin-bottom: 2rem;
      }
      @media @mediumDevices{
        min-height: 350px;
      }
      .card-body{
        display: grid;
      }
      .card-header{
        color: @white;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 100px;
        border-top-left-radius: 14px;
        border-top-right-radius: 14px;
      }
      .card-text{
        margin-bottom: 1rem;
        text-align: center;
      }
      a{
        font-size: 16px;
        color: @black;
        font-weight: @bold;
        @media @extraSmallDevices,@smallDevices {
          font-size: 15px;
        }
        &:hover{
          color: @orange;
        }
      }
    }

  }
  .sectionFour{
    .input-group{
      left: 25%;
      width: 50%;
      @media @extraSmallDevices, @smallDevices{
        left: 0%;
        width: 90%;
        display: block;
        text-align: center;
        margin: 0 auto;
      }
      input{
        background-color: transparent;
        border-bottom: 1px solid @gray;
        border-top: none;
        border-left: none;
        border-right: none;
        border-radius: 0;
        @media @smallDevices, @extraSmallDevices{
          width: 100%;
        }
      }
      .input-group-append{
        button{
          background-color: @white;
          color: @black;
          border: none;
          width: 300px;
          border-radius: 0;
          @media @mediumDevices{
            width: 200px;
          }
          @media @extraSmallDevices, @smallDevices{
            width: 100px;
            margin: 10 auto;
          }
        }
      }
    }
    background-color: @orange;
    border-radius: 16px;
    margin-left: 30px;
    margin-right: 30px;
    @media @extraSmallDevices, @smallDevices{
      margin-top: 2rem !important;
    }
    .subscribe-section{
padding-top: 30px;
      padding-bottom: 30px;
      img{
        height: 40px;
        position: relative;
        left: 50%;
        @media @extraSmallDevices, @smallDevices{
          left: 45%;
        }
      }
     h5{
       padding: 1rem;
       text-align: center;
       color: @white;
     }
      .input-subscribe{
        .form-control{
          color: black;
          background-color: transparent;
          border-bottom: 1px solid white;
          border-radius: 0;
          border-left: none;
          border-right: none;
          border-top: none;
        }
        .email-input{
          width: 80%;
          margin: 0 auto;
          @media @extraSmallDevices, @smallDevices{
            text-align: center;
          }
        }
        .subscribe{
          width: 50%;
          margin: 0 auto;
        }
      }
.subscribe-text{
  text-align: center;
  color: @white;
  padding: 1rem;
}
    }
  }
.sectionFive{
  .left-img{
    img{
      width: 350px;
      height: 100%;
      @media @mediumDevices{
        width: 300px;
        height: 100%;
      }
    }
  }
  .testimonial-container{
    border-radius: 14px;
    margin-left: 20px;
    border: 1px solid orange;
    min-height: 350px;
    width: 100%;
    align-self: center;
    @media @mediumDevices {
      min-height: 400px;
      margin-bottom: 2rem;
      margin-left: 0;
    }
    @media @extraSmallDevices,@smallDevices{
      min-height: 100%;
      margin-bottom: 2rem;
      margin-left: 0;
    }
    .rating{
      margin-bottom: 1rem;
      img {
        width: 25px;
      }
    }
    #carouselExampleCaptions {
      padding: 20px 20px 30px;
      min-height: 350px;
      @media @mediumDevices {
        min-height: 400px;
      }
      @media @extraSmallDevices,@smallDevices {
        min-height: 100%;
      }
    }
    .carousel-indicators {
      margin-bottom: 0;
    }
    .carousel-indicators [data-target]{
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: @gray;
      border: none;
      margin: 2px;
        &.active {
          background-color:@orange;
      }

    }
    .carousel-inner{
      .carousel-item{
        text-align: center;
        h3{
          color: @orange;
          font-size: 24px;
          margin-bottom: 15px;
          @media @extraSmallDevices,@smallDevices {
            font-size: 20px;
          }
        }
        h4 {
          @media @extraSmallDevices, @smallDevices {
            font-size: 18px;
          }
        }
        p{
          color: @black;
          margin-bottom: 20px;
          font-size: 15px;
          //min-height: 160px;
        }

      }
    }

  }
}
  .sectionSix{
    .card{
      border: 1px solid @orange !important;
      background-color: transparent;
      box-shadow: none;
      @media @extraSmallDevices, @smallDevices, @mediumDevices{
        margin-bottom: 1rem;
      }
      @media @extraSmallDevices, @smallDevices{
        min-height: 210px;
      }
      .card-content{
        display: flex; justify-content: center; padding: 15px;
        @media @extraSmallDevices, @smallDevices{
          display: grid !important;
        }
      }

    }
    #total-books{
      .circle{
        background: #F9B87C;
      }
    }
    #total-authors{
      .circle{
        background-color: #A5D6DE;
      }
    }
    #total-books-sold{
      .circle{
        background-color: #B2DCAB;
      }
    }
    #total-happy{
      .circle{
        background-color: #FACED6;
      }
    }
    .totals{
      align-self: center;
      @media @extraSmallDevices, @smallDevices{
        margin-top: 1rem;
        text-align: center;
      }
      p{
        color: @gray;
      }
    }
    .circle{
      border-radius: 50%;
      min-width: 80px;
      min-height: 80px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 15px;
      @media @extraSmallDevices, @smallDevices{
        margin: 0 auto;
      }
    }

  }

}


a{
  text-decoration: none !important;
}
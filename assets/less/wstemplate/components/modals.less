@import "../theme/colors.less";
@import "../theme/fonts.less";
@import "../theme/responsive.less";

// Modal Styles
.modal {
  //background: rgba(0, 0, 0, 0.85);
  z-index: 9992;
}
.modal-backdrop.show {
  opacity: .85;
  z-index: 9991;
}

.modal-modifier {
  .modal-header-modifier {
    border: none;
  }
  .modal-body-modifier {
    h1,h2,h3,h4,h5 {
      color: @dark-gray;
      font-weight: @medium;
      font-size: 18px;
      line-height: normal;
      @media @extraSmallDevices, @smallDevices {
        font-size: 16px;
      }
    }
    p {
      color: @light-gray;
      strong {
        font-weight: @semi-bold;
      }
      @media @extraSmallDevices, @smallDevices {
        font-size: 13px;
        br {
          display: none;
        }
      }
    }
  }
  .modal-header-modifier {
    .close {
      margin: 0;
    }
  }
  .close {
    position: absolute;
    right: 15px;
    top: -45px;
    padding: 0;
    font-weight: @thin;
    font-size: 30px;
    opacity: 1;
    &:focus, &:active {
      outline: 0;
      box-shadow: none;
    }
  }
  .btn, a.btn {
    font-size: 14px;
  }
  .modal-content-modifier {
    border-radius: 10px;
  }
  @media @extraSmallDevices, @smallDevices {
    .modal-dialog-modifier {
      align-items: flex-end;
      padding-bottom: 0;
      max-width: 100%;
      margin: 0;
      height: 100%;
    }
    .modal-content-modifier {
      border-radius: 20px 20px 0 0;
    }
  }
}
.modal-mobile-fullscreen {
  @media @extraSmallDevices, @smallDevices {
    .close {
      position: relative;
      right: 0;
      top: 0;
      font-weight: @extra-light;
      color: @dark-gray !important;
    }
    .modal-dialog-modifier {
      align-items: inherit;
    }
    .modal-content-modifier {
      border-radius: 0;
      border: none;
      height: 100vh;
    }
  }
}
.modal.fade {
  .modal-dialog.modal-dialog-zoom {
    -webkit-transform: translate(0,0)scale(.5);
    transform: translate(0,0)scale(.5);
  }
}
.modal.show {
  .modal-dialog.modal-dialog-zoom {
    -webkit-transform: translate(0,0)scale(1);
    transform: translate(0,0)scale(1);
  }
}
@import "../theme/colors.less";
@import "../theme/fonts.less";
@import "../theme/responsive.less";

// Bootstrap Button Styles
button, .btn {
  cursor: pointer;
  border-radius: 5px;
  text-transform: unset;
  &:hover, &:focus, &:active, &:active:focus {
    outline: 0;
    box-shadow: none;
  }
  &:not(:disabled):not(.disabled) {
    &:active, &:active:focus {
      box-shadow: none;
    }
  }
  &.disabled, &:disabled {
    cursor: unset;
  }
  &.btn-lg {
    font-size: 14px;
  }
}

.btn-shadow {
  box-shadow: 0 2px 4px @gray-light-shadow;
  -webkit-box-shadow: 0 2px 4px @gray-light-shadow;
  -moz-box-shadow: 0 2px 4px @gray-light-shadow;
}

// For Wonderslate Only - Button Primary Style
.btn-primary-modifier {
  background-color: @orange !important;
  border-color: @orange !important;
  color: @white;
  font-weight: @medium;
  &:hover {
    background-color: @orange;
    border-color: @orange;
    color: @white;
  }
  &:not(:disabled):not(.disabled) {
    &:active, &:active:focus {
      background-color: @orange;
      border-color: @orange;
      color: @white;
    }
  }
  &.disabled, &:disabled {
    background-color: @orange;
    border-color: @orange;
    color: @white;
  }
  &.dropdown-toggle {
    &:focus {
      box-shadow: none !important;
    }
  }
  &:focus:not(:active) {
    background-color: @orange;
    color: @white;
  }
  .material-icons {
    font-size: 18px;
  }
}
// End Button Primary Style

// For White Labels
//.btn-primary-modifier {
//  background-color: @theme-primary-color !important;
//  border-color: @theme-primary-color !important;
//  &:hover {
//    background-color: @theme-primary-color;
//    border-color: @theme-primary-color;
//  }
//  &:not(:disabled):not(.disabled) {
//    &:active, &:active:focus {
//      background-color: @theme-primary-color;
//      border-color: @theme-primary-color;
//    }
//  }
//  &.disabled, &:disabled {
//    background-color: @theme-primary-color;
//    border-color: @theme-primary-color;
//  }
//  &.dropdown-toggle {
//    &:focus {
//      box-shadow: none !important;
//    }
//  }
//  &:focus:not(:active) {
//    background-color: @theme-primary-color;
//  }
//  .material-icons {
//    font-size: 18px;
//  }
//}
// End here

.btn-outline-primary-modifier {
  color: @theme-primary-color;
  border-color: @theme-primary-color;
  &:hover {
    color: @theme-primary-color;
    background-color: transparent;
    border-color: @theme-primary-color;
  }
  &:not(:disabled):not(.disabled) {
    &:active, &:active:focus {
      color: @theme-primary-color;
      background-color: transparent;
      border-color: @theme-primary-color;
    }
  }
  &:focus:not(:active) {
    background-color: transparent;
  }
}

.btn-secondary-modifier {
  background-color: @secondary-btn;
  border-color: @secondary-btn;
  &:hover {
    background-color: @secondary-btn;
    border-color: @secondary-btn;
  }
  &:not(:disabled):not(.disabled) {
    &:active, &:active:focus {
      background-color: @secondary-btn;
      border-color: @secondary-btn;
    }
  }
  &.disabled, &:disabled {
    background-color: @secondary-btn;
    border-color: @secondary-btn;
  }
  &:focus:not(:active) {
    background-color: @secondary-btn;
  }
}

.btn-outline-secondary-modifier {
  color: @secondary-btn;
  border-color: @secondary-btn;
  &:hover {
    color: @secondary-btn;
    background-color: transparent;
    border-color: @secondary-btn;
  }
  &:not(:disabled):not(.disabled) {
    &:active, &:active:focus {
      color: @secondary-btn;
      background-color: transparent;
      border-color: @secondary-btn;
    }
  }
  &:focus:not(:active) {
    background-color: transparent;
  }
}

.btn-success-modifier {
  background-color: @success-btn;
  border-color: @success-btn;
  &:hover {
    background-color: @success-btn;
    border-color: @success-btn;
  }
  &:not(:disabled):not(.disabled) {
    &:active, &:active:focus {
      background-color: @success-btn;
      border-color: @success-btn;
    }
  }
  &.disabled, &:disabled {
    background-color: @success-btn !important;
    color: @white !important;
    border-color: @success-btn;
  }
  &:focus:not(:active) {
    background-color: @success-btn;
  }
}

.btn-outline-success-modifier {
  color: @success-btn;
  border-color: @success-btn;
  &:hover {
    color: @success-btn;
    background-color: transparent;
    border-color: @success-btn;
  }
  &:not(:disabled):not(.disabled) {
    &:active, &:active:focus {
      color: @success-btn;
      background-color: transparent;
      border-color: @success-btn;
    }
  }
  &:focus:not(:active) {
    background-color: transparent;
  }
}

.btn-danger-modifier {
  background-color: @danger-btn;
  border-color: @danger-btn;
  &:hover {
    background-color: @danger-btn;
    border-color: @danger-btn;
  }
  &:not(:disabled):not(.disabled) {
    &:active, &:active:focus {
      background-color: @danger-btn;
      border-color: @danger-btn;
    }
  }
  &.disabled, &:disabled {
    background-color: @danger-btn;
    border-color: @danger-btn;
  }
  &:focus:not(:active) {
    background-color: @danger-btn;
  }
}

.btn-warning-modifier {
  background-color: @orange;
  border-color: @orange;
  color: @white;
  &:hover {
    background-color: @orange;
    border-color: @orange;
    color: @white;
  }
  &:not(:disabled):not(.disabled) {
    &:active, &:active:focus {
      background-color: @orange;
      border-color: @orange;
      color: @white;
    }
  }
  &.disabled, &:disabled {
    background-color: @orange;
    border-color: @orange;
    color: @white;
  }
  &:focus:not(:active) {
    background-color: @orange;
    color: @white;
  }
}


// ===> MDL Button Styles
.mdl-button-modifier {
  height: auto;
  width: auto;
  min-width: auto;
}
@import "../variables/responsive.less";
@import "./color-lite.less";
//@import "button.less";
@ws-lightOrange:#F79420;
@ws-darkOrange:#CF6C00;
@ws-darkBlue:#2EBAC6;
:root {
  --primary-light: #8abdff;
  --primary: #6d5dfc;
  --primary-dark: #5b0eeb;
  --white: #ffffff;
  --greyLight-1: #e4ebf5;
  --greyLight-2: #c8d0e7;
  --greyLight-3: #bec8e4;
  --greyDark: #9baacf;
}

::-webkit-scrollbar {
  display: none;
}

html,body{
  margin: 0;
  padding: 0;
  height: 100vh;
  font-family: 'Poppins', sans-serif;
}
html { height: 100vh; }
body { height: 100%; }
h1,h2,h3,h4,label,input,p,pre,span{
  font-family: 'Poppins', sans-serif;
}
p{
  margin-bottom: 0;
}
body{
  //background:#e4ebf5;
  background: #fff!important;
  min-height: 100vh;
  overflow-y: auto;
}

.play-wrapper{
  height: 100vh;
}
.btn-play{
  padding:0.5rem 2rem;
  background: @dark-red-1;
  color:@practice-light;
}
.instruction p{
  color:@practice-light;
}
.instruction{
  min-height: 100vh;
}
.button-wrapper{
  position: absolute;
  bottom: 0;
  width: 100%;
  display: flex;
  background:@dark-bg-2;
  button{
    width: 50%;
    color:@practice-light;
    border-radius: 0;
    border:none;
    &:focus{
      outline: none;
    }
    &:first-child{
      border-right: 1px solid rgb(237 237 237 / 20%)
    }
  }
}

.button-main {
  padding: 16px 42px;
  box-shadow: 0px 0px 12px -2px rgba(0,0,0,0.5);
  line-height: 1.25;
  background: none;
  text-decoration: none;
  color: white;
  font-size: 16px;
  letter-spacing: .08em;
  text-transform: uppercase;
  position: relative;
  transition: background-color .6s ease;
  overflow: hidden;
  &:after {
    content: "";
    position: absolute;
    width: 0;
    height: 0;
    top: 50%;
    left: 50%;
    top: var(--mouse-y);
    left: var(--mouse-x);
    transform-style: flat;
    transform: translate3d(-50%,-50%,0);
    background: rgba(white,.1);
    border-radius: 100%;
    transition: width .3s ease, height .3s ease;
  }
  &:focus,
  &:hover {
    background: darken(@dark-bg-2,7%);
  }
  &:active {
    &:after {
      width: 300px;
      height: 300px;
    }
  }
}


.gamer-profile {
  min-height: 100vh;
  .media-body{
    p{
      color:@practice-light
    }
    h5{
      color:@practice-light
    }
  }
  .media {
    height: 50vh;
    img{
      border: 3px solid @practice-light
    }
    h5,p{
      padding: 0;
      margin: 0;
    }
    p{
      font-size: 10px;
    }
    &:first-child{
      padding: 6rem 2rem;
    }
    &.botProfile{
      &:last-child{
        border:none;
        padding: 6rem 2rem;
        h5,p{
          text-align: right;
        }
      }
    }
  }
}
.border-bot{
  border-top: 3px solid @practice-light;
}

.playerbg{
  background: url("../../images/prepjoy/playerbg.svg") center no-repeat;
  background-size: cover;
  background-color:@light-red-1;
}
.bot-anim-wrapper{
  display: flex;
}
.bot-anim{
  background: url("../../images/prepjoy/bot-anim.svg") center no-repeat;
  height: 50px;
  width: 50px;
  animation: mymove 4s infinite;
  position: relative;

}
@keyframes mymove {
  0%   { left:-100px; top:0px;}
  25%   { left:300px; top:0px;}
  50%  { left:-100px; top:0px;}
  75%   { left:300px; top:0px;}
  100%  { left:-100px; top:0px;}
}
.botbg{
  background: url("../../images/prepjoy/botbg.svg") center no-repeat;
  background-size: cover;
}
.roundbg{
  background: url("../../images/prepjoy/roundbg.svg") center no-repeat;
  background-size: contain;
  width: 80px;
  height: 100%;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  img{
    width: 45px;
  }
}
.quiz-details{
  height: 85vh;
}
.ready{
  font-size: 20px;
  color:@pale-white;
}
.title{
  font-size: 20px;
  color:@practice-light;
  font-weight: 400;
  margin-bottom: 0;
  margin-top: 10px;
}
.subject{
  font-weight: normal;
  color: rgb(221 221 221 / 80%);
  font-size: 14px;
  margin-top: 5px;
}
.que-no{
  font-size: 14px;
  color:@practice-light;
}
.quiz-profile{
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-bottom: 0.5rem;
  padding-top: 1rem;
  .media{
    img{
      border:2px solid @practice-light;
    }
  }
  .username{
    font-size: 12px;
    color:@practice-light;
    margin-bottom: 0;
  }
  .score{
    font-size: 12px;
    color:@light-orange;
  }
}
.main-timer{
  text-align: center;
  h4{
    font-size: 10px;
    text-transform: uppercase;
    color:@steal-color-1;
    margin-bottom: 0px;
  }
  p{
    font-size: 16px;
    color:@steal-color-2;
    font-weight: 500;
  }
}
.progress{
  position: absolute;
  top:0;
  width:100%;
  border-radius: 0;
  height: 0.25rem;
  .progress-bar{
    background-color:@steal-color-2;
  }
}
.quizes{
  //height: ~"calc(100vh - 70px)";
  position: relative;
  .container{
    height: ~"calc(100vh - 110px)";
    overflow-y: auto;
    overflow-x:hidden;
  }

}
.question-wrapper{
  margin-top: 1rem;
  text-align: center;
  &.test-series{
    @media @iPhone6-portrait,@iPhone{
      top: 0;
      padding-top: 15px;
      margin: 0 -15px;
      border-top: 1px solid rgb(221 221 221 / 20%);
      padding-bottom: 0;
      .que_text{
        padding: 10px;
        max-height: 200px;
        overflow-y: auto;
        margin-top: 1rem;
      }
    }
    border-bottom-left-radius: 30px;
    border-bottom-right-radius: 30px;
  }
  @media @iPhone{
    top:10px;
  }
  @media @iPhone6-portrait{
    top:25px;
  }
  z-index: 999;

  p{
    color:@dark-bg-1;
    text-align: center;
    font-size: 16px;
    display: flex;
    flex-direction: column;
  }
  img{
    border-radius: 4px;
    padding: 5px 20px;
    margin-top: 1rem;
    width: auto;
    max-height: 100px;
    object-fit: contain;
  }
}
.que-options-wrapper{
  padding: 0 10px;
  .que-options{
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    .option{
      padding: 5px 0.5rem;
      width: 100%;
      min-height: 65px;
      box-shadow: inset 0.2rem 0.2rem 0.5rem var(--greyLight-2), inset -0.2rem -0.2rem 0.5rem var(--white);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;
      margin-top:1rem;
      margin-bottom: 1rem;
      border-radius: 10px;
     background: none;
      span{
        display: flex;
        flex-direction: column;
        img{
          max-width: 100px;
          max-height: 100px;
        }
      }

      &.disabled{
        pointer-events: none;
      }
      &.correct{
        background: @green-color-ca;
        box-shadow: none;
        &.clicked{
          background: @steal-color-1;
        }

      }
      &.incorrect{
        background: @red-color-wa;
        box-shadow: none;
        &.clicked{
          background: @steal-color-1;
        }
      }
      &:last-child{
        margin-bottom: 1.5rem;
      }

    }
  }
}
.time_line-wrapper{
  background: @pale-white-1;
  box-shadow: inset 0px 2px 4px rgba(0, 0, 0, 0.25);
  border-radius: 10px;
  height: 7px;
  margin: 1rem 2rem;
  display: none;
}
.time_line{
  background: linear-gradient(90.34deg, @light-red-1 -2.11%, @yellow 110.36%);
  border-radius: 10px;
  height: 7px;
  width: 0;
}
.total_que{
  span{
    color:@practice-light;
    p{
      color: @practice-light;
    }
  }
}
.meridian{
  height: 8px;
  width: 100%;
  background: @practice-light;
  border-radius: 1px;
}

.circle-wrapper{
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 999;
}
.botselect{
  background: @dark-bg-1;
  height: 45vh;
  display: flex;
  align-items: center;
}
.img-bot{
  background: url("../../images/prepjoy/userstart.svg") center no-repeat;
  width:60px;
  height: 60px;
  position: absolute;
  left: 40%;
  //transform: translateX(-50%);
  top:0;
  border: 3px solid @practice-light;
  border-radius:50px;
}
.bot-wrapper{
  position: relative;
}
.progress-bar-vertical {
  width: 8px;
  height: 90%;
  display: flex;
  align-items: flex-end;
  position: absolute;
  bottom:0;
  top:initial;
  border-radius:4px ;
  background:#e4ebf5;
  &.player1{
    left: 0;
  }
  &.player2{
    right: 0;
  }
  &.progress-correct{
    background: rgb(66 181 56 / 50%);
  }
  &.progress-incorrect{
    background: rgb(218 1 1 / 50%);
  }
}

.progress-bar-vertical .progress-bar {
  width: 100%;
  height: 0;
  -webkit-transition: height 0.6s ease;
  -o-transition: height 0.6s ease;
  transition: height 0.6s ease;
}
.base-timer {
  position: relative;
  width: 40px;
  height: 40px;
}

.base-timer__svg {
  transform: scaleX(-1);
}

.base-timer__circle {
  fill: none;
  stroke: none;
}

.base-timer__path-elapsed {
  stroke-width: 7px;
  stroke: grey;
}

.base-timer__path-remaining {
  stroke-width: 7px;
  stroke-linecap: round;
  transform: rotate(90deg);
  transform-origin: center;
  transition: 1s linear all;
  fill-rule: nonzero;
  stroke: currentColor;
}

.base-timer__path-remaining.green {
  color: @light-green;
}

.base-timer__path-remaining.orange {
  color: orange;
}

.base-timer__path-remaining.red {
  color: red;
}

.base-timer__label{
  position: absolute;
  width: 40px;
  height: 40px;
  top: 0;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  display: none;
}
.time-wrapper{
  width: 40px;
  height: 40px;
  position: relative;
  margin: 5px auto;
}
#countdown {
  position: absolute;
  width: 40px;
  height: 40px;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  display: flex;
  top:0;
}
#app{

}
.resultWrapper,.practice-result {

  height: 100vh;
  overflow-y: auto;

  .result-header {
    h2, p {
      color: @practice-light;
      padding: 16px 0;
      display: flex;
      align-items: center;
    }

    h2 {
      font-family: 'Righteous', cursive;
      font-size: 21px;

      i {
        font-size: 24px;
      }
    }

    p {
      font-size: 14px;

      i {
        font-size: 18px;
        margin-right: 5px;
      }
    }
  }
}
.result{
  font-family: 'Righteous', cursive;
  font-size: 21px;
}
.prep-logo{
  width: 55px;
}
#user-status,.user-status{
  color:@light-red-1;
  font-family:'Righteous', cursive;
  font-size: 26px;
  text-align: center;
  text-transform: uppercase;
}
.score-media{
  border: 1px dashed @practice-light;
  border-radius: 10px;
  width: 125px;
  height: 80px;
  margin: 0 auto;
  .updateScore{
    font-size: 20px;
    color:@practice-light;
    text-align: center;
    font-family:'Righteous', cursive;
  }
  .score-text{
    font-size: 16px;
    color:@practice-light;
    text-align: center;
    font-family:'Righteous', cursive;
  }
  #noofque{
    font-size: 20px;
    color:@practice-light;
    text-align: center;
    font-family:'Righteous', cursive;
  }
  .score-wrap{
    color:@practice-light;
    font-size: 16px;
  }
  p{
    margin-bottom: 0!important;
  }
}


.slider{
  margin-top: 3.5rem;
}
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default, .ui-button, html .ui-button.ui-state-disabled:hover, html .ui-button.ui-state-disabled:active{
  background: @light-red-1;
  border-radius: 50px;
  border: 3px solid @practice-light;
}
.ui-widget-content{
  background: @pale-white-1;
  box-shadow: inset 0px 2px 4px rgba(0, 0, 0, 0.25);
  border-radius: 10px;
}
.ui-slider-horizontal{
  height: 8px;
}
.ui-slider-horizontal .ui-slider-handle{
  top:-0.4em;
}
.ui-slider-range {
  background: linear-gradient(90.34deg, @light-red-2 -2.11%, @yellow 110.36%);
}
.ui-widget.ui-widget-content{
  border:none;
}


.badges{
  div{
    margin-top: 10px;
  }
  p{
    color:@practice-light;
    font-size: 10px;
  }
}
.custom-handle{
  p{
    color:@practice-light;
    font-size: 10px;
    white-space: nowrap;
    text-align: center;
    position: absolute;
    top:-35px;
    left: -40px;
    width: 100px;
    &:nth-child(2){
      margin-top: 15px;
    }
  }
}
.balance-points{
  box-shadow: 0.3rem 0.3rem 0.6rem var(--greyLight-2), -0.2rem -0.2rem 0.5rem var(--white);
  border-radius: 10px;
  color:@practice-light;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 35px;
}
.btn-playagain{
  background: @light-red-1;
  border-radius: 10px;
  font-family: 'Righteous', cursive;
  font-size: 16px;
  color: #fff;
  width: 250px;
  height: 48px;
  margin: 10px auto;
}
.btn-play-reset{
  color: @ws-lightOrange;
  border-color: #fd693c;
  box-shadow: 0px 1px 5px #eb410e;
  border:none;
  background: #fff;
}
.btn-answer{
  background: #fff;
  color:@light-red-1;
  font-size: 14px;
  font-family: 'Righteous', cursive;
  margin-bottom: 1rem;
}
.box{
  width: 20px;
  height: 20px;
  background: @practice-light;
  &.red{
    background: @light-red-1;
  }
  &.green{
    background: @green-color-ca;
  }
}
.answer-indicator{
  display: flex;
  align-items: center;
  justify-content: space-around;
  border: 1px dashed @practice-light;
  width: 100%;
  height: 48px;
  margin-top: 2rem;
  border-radius: 4px;
  >div{
    display: flex;
    align-items: center;
    justify-content: center;
    p{
      color:@practice-light;
      margin-left: 10px;
      font-size: 10px;
    }
  }
}
.share-indicator{
  .answer-indicator;
  height: auto;
  padding: 10px;
  img{
    height: 40px;
    width: 40px;
    margin-right: 10px;
  }
  p{
    font-size: 14px;
  }
  div{
    display: block;
    p{
      font-size: 12px;
    }
  }
  button{
    background: @steal-color-1;
    font-size: 14px;
    display: flex;
    align-items: center;
    margin-left: 10px;
    margin-top: 4px;
    color:@practice-light;
    i{
      font-size: 16px;
      margin-right: 5px;
    }
  }
}

#qa-answer{
  margin: 2rem -14px;
}
.mcq-answers{
  margin: 0 -14px;
  padding: 2rem 10px;
  background: #FFFFFF;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  #que-no{
    font-weight: bold;
    font-size: 18px;
    color: @practice-light;
    text-align: center;
    margin-bottom: 1rem;
  }
  .question-wrapper{
    background: none;
    position: static;
    //background: @practice-light;
  }
  .que-options-wrapper .que-options .option:last-child{
    margin-bottom: 0;
  }
}
.answer-wrapper{
  display: none;
  .box{
    margin-right: 10px;
    width: 15px;
    height: 15px;
    margin-top: 5px;
  }
}
.share-wrapper{
  .answer-wrapper;
  display: block;
}
.option-qa{
  display: flex;
  align-items: center;

  .option{
    span{
      p{
        display: flex;
        flex-direction: column;
      }

    }
  }

}

.badge{
  height: 70px;
  width: 70px;
  margin: 15px auto;
  margin-bottom: 0;
  &.hunter{
    background: url("../../images/prepjoy/hunter.svg") center no-repeat;
    background-size: contain;
  }
  &.warrior{
    background: url("../../images/prepjoy/warrior.svg") center no-repeat;
    background-size: contain;
  }
  &.knight{
    background: url("../../images/prepjoy/knight.svg") center no-repeat;
    background-size: contain;
  }
  &.ninja{
    background: url("../../images/prepjoy/ninja.svg") center no-repeat;
    background-size: contain;
  }
  &.commander{
    background: url("../../images/prepjoy/gladiator.svg") center no-repeat;
    background-size: contain;
  }
  &.gladiator{
    background: url("../../images/prepjoy/gladiator.svg") center no-repeat;
    background-size: contain;
  }
  &.samurai{
    background: url("../../images/prepjoy/supreme.svg") center no-repeat;
    background-size: contain;
  }
  &.ultimate{
    background: url("../../images/prepjoy/ultimate.svg") center no-repeat;
    background-size: contain;
  }
}
.badge-screen {
  margin-bottom: 1rem;
  display: none;
  #congrats {
    color: @light-red-1;
    font-family: 'Righteous', cursive;
    font-size: 26px;
    text-align: center;
    text-transform: uppercase;
    text-align: center;
  }
  p{
    color:@practice-light;
    text-align: center;
    font-size: 14px;
  }
}
#lottie{
  position: absolute;
  top:0;
  left:0;
}
.modal.fade .modal-dialog.modal-dialog-zoom {-webkit-transform: translate(0,0)scale(.5);transform: translate(0,0)scale(.5);}
.modal.show .modal-dialog.modal-dialog-zoom {-webkit-transform: translate(0,0)scale(1);transform: translate(0,0)scale(1);}
#winnerModal {
  overflow-y: hidden;
  .modal-content {
    background: #000;

    .modal-header {
      border: none;
    }

    .modal-footer {
      border: none;
    }
  }
  .user-status{
    color:@green-color-ca;
    &.red{
      color:@light-red-1;
    }
  }
}
#winner{
  height: 200px;
}
.question-no{
  color: @steal-color-1;
  margin:0.5rem 0;
  font-family: 'Righteous', cursive;
  font-size: 16px;
  text-align: center;
  font-weight: normal;
}
.medal-user{
  width: 200px;
  height: 200px;
  margin: 20px auto;
  &.gold{
    background: url("../../images/prepjoy/goldmedal.svg") center no-repeat;
    background-size: contain;
  }
  &.silver{
    background: url("../../images/prepjoy/silvermedal.svg") center no-repeat;
    background-size: contain;
  }
  &.bronze{
    background: url("../../images/prepjoy/bronzemedal.svg") center no-repeat;
    background-size: contain;
  }
}
.medal-wrappers{
  text-align: center;
  background: #000;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  h1{
    color: @dark-red-2;
    font-size: 28px;
    font-family: 'Righteous', cursive;
    padding-top: 15px;
  }
  p{
    font-size: 18px;
    color:@practice-light;
  }
}
#medal-name{
  font-size: 18px;
  color:@practice-light;
}
#medalModal{
  .modal-footer{
    border: none;
  }
  .modal-dialog{
    height: 100%;
    margin: 0;
    border-radius: 0;
  }
  .modal-content{
    border-radius: 0;
    border:none;
    height: 100%;
  }
}
#rankModal{
  .modal-footer{
    border: none;
  }
  .modal-dialog{
    height: 100%;
    margin: 0;
    border-radius: 0;
  }
  .modal-content{
    border-radius: 0;
    border:none;
    height: 100%;
  }
}
#medal-upload{
  position: absolute;
  left: 0;
  top: 0;
}
@media only screen and (min-width:769px){
  .circle-wrapper{
    width: 83%;
  }
}
.locate{
  color:@practice-light;
  font-size: 18px;
  margin-bottom: 1rem;
}
.no-counter{
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
}
#no-counter{
  color:@practice-light;
}
#readyGo {
  position:relative;
}

@-webkit-keyframes count {
  0% {transform: scale(1.5);}
  100% {transform: scale(1);}
}

.nums {
  font-size:5rem;
  height:auto;
  top:0;
  right:0;
  bottom:0;
  left:0;
  margin:auto;
  text-align:center;
}

.three {
  -webkit-animation:count 0.1s cubic-bezier(0.1,0.1,1,1) 1;
  animation:count 0.1s cubic-bezier(0.1,0.1,1,1) 1;
}

.two {
  -webkit-animation:count 0.1s cubic-bezier(0.1,0.1,1,1) 1;
  animation:count 0.1s cubic-bezier(0.1,0.1,1,1) 1;
}

.one {
  -webkit-animation:count 0.1s cubic-bezier(0.1,0.1,1,1) 1;
  animation:count 0.1s cubic-bezier(0.1,0.1,1,1) 1;
}


.quiz-profile .playerName{
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 60px;
}
.audioChange{
  color:@practice-light;
  margin-right: 10px;
  position: relative;
  height: 25px;
  i{
    font-size: 15px;
    background: @light-red-1;
    border-radius: 50px;
    width: 25px;
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: right;
    position: absolute;
    right: 0;
  }
}
.button-results{
  text-align: center;
  margin-top: 1rem;
}

.app-header{

  padding: 0.5rem 1rem;

  >div{
    padding: 0.5rem;
  }
  i{
    color:@practice-light;
  }
  h4{
    color:@practice-light;;
    font-size: 16px;
  }
}
.app-footer{
  position: sticky;
  position: -webkit-sticky;
  bottom:0;
  padding: 0.5rem 2rem;
  width: 100%;
  box-shadow: 0px 0 2px #010102;
  background:white;
  >div{
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  button{
    background: @white-color;
    border:none;
    color:@practice-light;
    font-size: 16px;
    height: 40px;
    width: 95px;
    border: 1px solid #010102;
    border-radius: 8px;
    &.next-btn{
      background: #0AAEF9;
    }
  }
}
.clock-wrapper,.lang-wrapper{
  margin: 0 -1.5rem;
  width: auto !important;
}
.explanation-wrapper,.clock-wrapper,.lang-wrapper,.action-wrapper {
  display: none;
  background: @practice-light;
  min-height: 300px;
  position: sticky;
  bottom: 0;
  width: 100%;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  .clock-explanation,.lang-explanation{
    display: flex;
    flex-wrap: wrap;
    justify-content: space-evenly;
  }
  .answer-explanation,.clock-explanation,.lang-explanation,.action-explanation{
    padding: 0.5rem;
    max-height: 100vh;
    overflow-y: auto;
    padding-bottom: 6rem;
    h4{
      font-size: 14px;
      color:@practice-light;
    }
    #explanation{
      color:@practice-light;
      padding-bottom: 10px;
      font-size: 14px;
    }
  }
  .explanation-header,.clock-header,.lang-header,.action-header {
    background: @dark-bg-2;
    min-height: 80px;
    padding: 1rem;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    box-shadow: 0 -10px 10px -10px #201a40;
    div{

    }
    h4{
      color:@green-color-ca;
      font-size: 16px;
    }
    i{
      color: @practice-light;
      background: #201a40;
      width: 25px;
      height: 25px;
      display: flex;
      align-items: center;
      justify-content: CENTER;
      border-radius: 4px;
      font-size: 20px;
    }
    p{
      color:#fff;
    }
  }
}
.show-explanation{
  background:#e1b12c;
  color:@practice-light;
  font-size: 14px;
  display: none;
}
.preview-quiz{
  height: 100%;
  z-index: 0 !important;
  display: none;
  border-top-left-radius: 50px;
  border-top-right-radius: 50px;
  margin-top: 5px;
  h4{
    color:@practice-light;
    display: flex;
    align-items: center;
    font-size: 18px;
    i{
      font-size: 20px;
      margin-right: 10px;
    }
  }
  i{
    font-size: 20px;
    color: @practice-light;
  }
}
.num-wrapper{

  width: 100%;
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 10px;
  .num-text{
    background: #fff;
    border: 1px solid #5a4848;
    font-size: 14px;
    height: 30px;
    width: 30px;
    text-align: center;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    i{
      position: absolute;
      right: -10px;
      top: -5px;
      color:#e1b12c;
    }
  }
  div{
    color:@practice-light;
  }
}
#total-que-tab{
  padding: 1rem;
  height: calc(100vh - 78px);
  overflow-y: auto;
}
.practice-result{
  overflow-x:hidden;
  .nav-tabs{
    display: flex;
    justify-content: space-between;
    margin-top: 2rem;
    margin-left: -15px;
    margin-right: -15px;
    border-bottom: none;
    position: sticky;
    top:0;
    z-index: 999;
    box-shadow: 0 0 10px #999;
    li{
      width: 25%;
      &:last-child{
        a{
          border-right: 0.3px solid #000;
        }
      }
    }
    li a{
      background:@practice-light;
      color:@light-red-1;
      display: block;
      width: 100%;
      text-align: center;
      padding: 10px 0;
      text-decoration: none;
      border-right: 0.3px solid #000;
      &.active{
        background:@light-red-1;
        color:@practice-light;
      }
    }
  }
  .validate-answers{
    text-align: center;
    margin-top: 4rem;
    color:@light-red-1;
    font-size: 16px;
  }
  .tab-content{
    margin-left:-15px;
    margin-right:-15px;
  }
}
.test-summary{
  padding: 10px;
  i{
    color:@practice-light;
    font-size: 20px;
    margin-right: 12px;
  }
  h4{
    color:@practice-light;
    font-size: 15px;
    margin: 0;
  }
  p{
    color:#fff;
    font-size: 14px;
  }
}
.box-button{
  margin-top: 1.5rem;
  border-radius:4px;
  padding: 5px 8px;
  &.green{
    background:#05c46b;
    position: relative;
    &::after{
      content:'';
      color:#000;
      position:absolute;
      right: 0;
      top:0;
      width: 32px;
      height: 32px;
      border-radius: 50%;
    }
  }
  &.red{
    background:#cb170c;
  }
  &.yellow{
    background: #ffa801;
  }
}
.main-page{
  display: none;
  min-height: 50vh;
  overflow-y: auto;
  background:none;
  padding: 1.5rem;
  margin: 0 auto;
  margin-top:2%;
  margin-bottom:2%;
  h1,h2,h3,h4,p{
    color:@white-color;
    font-size: 16px;
    span{
      font-size: 14px;
    }
  }
  p.total-que{
    margin-top: 10px;
  }
  i{
    font-size: 24px;
    color:@white-color;
  }
  #header-top{
    box-shadow: 0.3rem 0.3rem 0.6rem var(--greyLight-2), -0.2rem -0.2rem 0.5rem var(--white);
    padding: 5px 10px;
    border-radius: 4px;
    p{
      color: @practice-light;
      margin:0 !important;
    }
    i{
      color:#111111;
    }
  }
  .mcq-name{
    font-size: 18px;
    color:@practice-light;
    font-weight: normal;
  }
  .set-box{
    width: 60px;
    height: 50px;
    background: rgba( 242, 223, 223, 0.25 );
    box-shadow: 0 8px 32px 0 rgba( 31, 38, 135, 0.37 );
    backdrop-filter: blur( 13px );
    -webkit-backdrop-filter: blur( 13px );
    border-radius: 10px;
    border: 1px solid rgba( 255, 255, 255, 0.18 );
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
  }
}
.instruction-wrapper{
  margin: 1.5rem -1.5rem;
  padding: 1.5rem;
  background-color: #2c2a3a;
  background-image: url("../../images/prepjoy/bg-practice.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  .h4{
    color: @practice-light;
    font-size: 15px;
  }
}
.custom-check{
  width: 100px;
  height: 75px;
  display: flex;
  align-items: center;
  justify-content: center;
  color:#fff;
  font-size: 16px;
  background: #201a40;
  padding: 5px 10px;
  border-radius: 4px;
  box-shadow: 0 0 0 2px #423e3e;
}

input[type=checkbox]:checked + label{
  border: 1px solid @steal-color-1;
}
input[type=radio]:checked + label{
  border: 1px solid @steal-color-1;
  background: @steal-color-1 ;
}
.btn-practices{
  box-shadow: 0.3rem 0.3rem 0.6rem var(--greyLight-2), -0.2rem -0.2rem 0.5rem var(--white);
  padding: 5px 10px;
  border-radius: 4px;

  color: @practice-light;
  font-size: 16px;
  text-transform: uppercase;
  width: 200px;
  border:none;
}
.customize-header{
  select{
    width: 150px;
  }
  label{
    color:@practice-light;
    font-size: 16px;
    margin-bottom: 0;
  }
}
.loader-submit{
  position: absolute;
  top: 50%;
  right: 50%;
  transform: translate(50%,-50%);
  display: none;
  width: 200px;
}
.btn-review{
  display: none;
  background: @practice-light;
  color: @practice-light;
  width: 100%;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
  border-bottom-left-radius: 30px;
  border-bottom-right-radius: 30px;
  font-size: 16px;
  &.marked{
    color: #e1b12c;
  }
  i{
    margin-right: 10px;
    font-size: 20px;
  }
  &:focus{
    outline: none !important;
    box-shadow: 0 0 0 0 rgba(0, 123, 255, 0) !important; // or none
  }
}
.reporticon{
  text-decoration:none;
  i{
    color:@practice-light;
  }
  &:focus{
    text-decoration:none;
  }
}

#prep-report-question{
  .prep-report-modal{
    .chkbox{
      display: flex;
      align-items: center;
    }
    label {
      display: flex;
      align-items: center;
      color: #000;
      cursor: pointer;
      position: relative;

      //checkbox object
      span {
        display: inline-block;
        position: relative;
        background-color: transparent;
        width: 15px;
        height: 15px;
        transform-origin: center;
        border: 2px solid @practice-light;
        border-radius: 50%;
        vertical-align: -6px;
        margin-right: 10px;
        transition: background-color 150ms 200ms, transform 350ms cubic-bezier(.78,-1.22,.17,1.89);

        &:before {
          content: "";
          width: 0px;
          height: 2px;
          border-radius: 2px;
          background: #000;
          position: absolute;
          transform: rotate(45deg);
          top: 6px;
          left: 2px;
          transition: width 50ms ease 50ms;
          transform-origin: 0% 0%;
        }

        &:after {
          content: "";
          width: 0px;
          height: 2px;
          border-radius: 2px;
          background: #fff;
          position: absolute;
          transform: rotate(305deg);
          top: 9px;
          left: 3px;
          transition: width 50ms ease;
          transform-origin: 0% 0%;
        }
      }
    }
    .form-check-label{
      width: 100%;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      color:#fff;
      font-size: 14px;
      background: #201a40;
      padding: 5px 10px;
      border-radius: 4px;
      box-shadow: 0 0 0 2px #423e3e;
      margin-top: 1rem;
    }
    input[type="checkbox"] {
      visibility:hidden;

      &:checked {
        + label {
          color:@dark-red-1;
          span {
            background-color: @dark-red-1;
            transform: scale(1.05);
            border: 2px solid @dark-red-1;

            &:after {
              width: 10px;
              background: #fff;
              transition: width 150ms ease 100ms;
            }

            &:before {
              width: 5px;
              background: #fff;
              transition: width 150ms ease 100ms;
            }
          }
        }
      }
    }


    label,input,p,textarea{
      font-family: 'Righteous', cursive !important;
      font-size: 14px;
      color: #000;
      font-weight: lighter !important;
    }
    .prep-report-icon{
      i{
        font-size: 18px;
        color: @light-red-1;
      }
    }
    .form-control{
      &:focus{
        border: 1px solid #999;
        box-shadow: none;

      }
    }
  }
}
.action-explanation{
  .btn-playagain{
    width: 80%;
    background: #334356;
  }
}
.close-buttons{
  text-align: right;
  color: #fff;
  float: right;
  width: 100%;
  padding: 10px;
}
.action-wrapper{
  z-index: 999;
}
@media (min-width: 1200px){
  .container {
    max-width: 1300px;
  }
}
@media (min-width: 992px) {
  .quizes{
    //height: ~"calc(100vh - 70px)";
    position: relative;
    .container{
      height: ~"calc(100vh - 150px)";
      overflow-y: auto;
      overflow-x:hidden;
    }

  }
  .mcq-answers{
    background: none;
  }
  .main-page{
    width: 768px;
    //box-shadow: 0.3rem 0.3rem 0.6rem var(--greyLight-2), -0.2rem -0.2rem 0.5rem var(--white);
    //box-shadow: 2px 2px 3px var(--greyLight-2), -2px -2px 3px var(--white);
    box-shadow: 0 1px 2px #21212136;
  }
  .title-header{
    position: absolute;
    top:2rem;
    font-size: 18px;
    color:#2b2355;
    text-transform: uppercase;
  }
  #total-que-tab,.preview-quiz{
    height: 800px;
    border-radius: 8px;
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
  }
  .que_text{
    //margin-top: 2rem;
  }

  .practice-quiz-wrapper{
    padding: 0 4rem;
  }
  .preview-quiz{
    border-radius: 11px;
    box-shadow: 0.3rem 0.3rem 0.6rem var(--greyLight-2), -0.2rem -0.2rem 0.5rem var(--white);
    position: relative;
    z-index: 999;
  }
  .app-header{
    border-radius: 11px;
    box-shadow: 0.3rem 0.3rem 0.6rem var(--greyLight-2), -0.2rem -0.2rem 0.5rem var(--white);
  }
  .app-footer{
    border-radius: 11px;
    box-shadow: 0.3rem 0.3rem 0.6rem var(--greyLight-2), -0.2rem -0.2rem 0.5rem var(--white);
  }
  .num-wrapper{
    box-shadow: inset 0.2rem 0.2rem 0.5rem var(--greyLight-2), inset -0.2rem -0.2rem 0.5rem var(--white);
  }
  .tab-content{
    .question-wrapper{
      margin-bottom: 0;
      min-height: 10px;
      border-top: none;
    }
  }
  .question-wrapper{
    min-height: 100px;
    padding: 5px;
    box-shadow: inset 0.2rem 0.2rem 0.5rem var(--greyLight-2), inset -0.2rem -0.2rem 0.5rem var(--white);
    border-radius: 4px;
    margin-bottom: 4rem;
    border-top: 1px solid #2980b9;
  }
  .question-wrapper .question-no{
    text-align: left;
    padding: 0 10px;
  }
  .question-wrapper .reporticon{
    margin-right: 10px;
    margin-top: 10px;
    text-decoration: none;
    i{
      font-size: 20px;
    }
  }
  .que-options-wrapper .que-options .option{

    color:#201a40;
    span{
      color: #201a40;
    }
  }
}
.sidebar {
  width: 85px;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  box-shadow: 0.3rem 0.3rem 0.6rem var(--greyLight-2), -0.2rem -0.2rem 0.5rem var(--white);
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  i{
    color:#201a40;
  }
}

.main-menu {
  margin-block-start: 0;
  margin-block-end: 0;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  padding-inline-start: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.menu-item {
  list-style: none;
  position: relative;
  width: 85px;
  height: 65px;
}

.menu-txt {
  opacity: 0;
  width: 0px;
  min-width: 0px;
  overflow: hidden;
  transition: 300ms linear;
  transition-delay: 250ms;
  display: flex;
  align-items: center;
  position: relative;
  top: 2px;
  white-space: nowrap;
  overflow: hidden;
}

.menu-a {
  padding: 35px 33.5px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.menu-icon {
  display: block;
  font-size: 1.5rem;
  color: #bdbdbd;
  position: relative;
  z-index: 100;
  transition: 400ms;
}

.menu-item:hover .menu-txt {
  opacity: 1;
  width: 100%;
  min-width: 40px;
  padding: 0px 20px;
  transition-delay: 0s;
  color: #4c4c4c;
}

.menu-txt-hld {
  position: absolute;
  z-index: 99;
  background: rgba(255, 255, 255, 0);
  border: 1px solid rgba(0, 0, 0, 0);
  border-radius: 38px;
  font-size: .94rem;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0);
  padding: 9.5px 8px;
  transition: 250ms linear;
  display: flex;
  align-items: center;
  left: 20px;
  transition-delay: 300ms;

}

.menu-item:hover .menu-txt-hld {
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.07);
  background: rgb(255, 255, 255);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition-delay: 0ms;
  i{
    color:#201a40;
  }
}
.active .material-icon {
  color: #03A9F4;
}
.prep-start{
  width: 35px;
  border-radius: 4px;
}


.show-hide-text {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}
.show-hide-text a {
  order: 2;
  text-transform: uppercase;
  order: 2;
  font-size: 14px;
}
.show-hide-text p {
  position: relative;
  overflow: hidden;
  max-height: 60px;
  line-height: 20px;
}
.show-hide-text p:after {
  content: "";
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100px;
  height: 20px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, white 100%);
}
@supports (-webkit-line-clamp: 3) {
  .show-hide-text p {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }
  .show-hide-text p:after {
    display: none;
  }
}

.show-less {
  display: none;
}
.show-less:target {
  display: block;
}
.show-less:target ~ p {
  display: block;
  max-height: 100%;
}
.show-less:target + a {
  display: none;
}

.wrapper {
  //max-width: 300px;
  //margin: 50px auto;
}
.wrapper p {
  font-size: 16px;
  line-height: 20px;
}

.toggle-group{
  .btn-primary{
    background-color: #201a40;
    border-color: #201a40;
  }
  .toggle-handle{
    border:1px solid #201a40;
  }
}
.h-full{
  height: 100vh;
}
.toggle{
  margin-top: 2.5rem;
}
.morecontent span {
  display: none;
}
.morelink {
  display: block;
}
@prep-secondary:#04001D;
@prep-white:#fff;
@prep-red:#E83500;

*{
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}
html,body{
  overflow-x: inherit !important;
  position: relative !important;
}

.hiddenDiv{
  @media screen and (max-width: 767px){
    display: none !important;
  }
}

.header{
  background: @theme-primary-color;
  padding: 2rem;
  position: sticky;
  top: 0;
  z-index:999999;

  width: 100%;
  @media screen and (max-width: 767px){
    padding: 1rem;
  }
  &-wrapper{
    display: flex;
    justify-content: space-between;
    align-items: center;

    @media screen and (max-width: 767px){
      flex-direction: column;
      align-items: flex-start;
      & :nth-child(3){
        order: 4;
      }
    }
    @media screen and (max-width: 860px){
      flex-direction: column;
      align-items: flex-start;
      & :nth-child(3){
        order: 4;
      }
    }

    &__logo{
      display: flex;
      justify-content: space-between;
      a{
        img{
          @media screen and (max-width: 767px){
            width:85px !important;
          }
          @media screen and (max-width: 860px){
            width: 100px;
          }
        }
        span{
          @media screen and (max-width: 767px){
           font-size: 17px !important;
          }
          @media screen and (max-width: 860px){
            font-size: 17px !important;
          }
        }
      }
      button{
        display: none;
        margin-left: auto;
        @media screen and (max-width: 767px){
          display: block;
        }
        @media screen and (max-width: 860px){
          display: block;
        }
      }
      @media screen and (max-width: 767px){
        width: 100%;
      }
      @media screen and (max-width: 860px){
        width: 100%;
      }
    }
    &__navbar{
      display: flex;
      align-items: center;
      margin-left: auto;
      .navbar-list{
        display: flex;
        margin-bottom: 0;
        flex-wrap: wrap;
        @media screen and (max-width: 767px){
          flex-direction: column;
        }
        @media screen and (max-width: 860px){
          flex-direction: column;
        }
        li{
          list-style: none;
          margin-right: 20px;
          @media screen and (max-width: 767px){
            margin-bottom: 12px;
          }
          @media screen and (max-width: 860px){
            margin-bottom: 12px;
          }
          a{
            color: @prep-white !important;
            transition: all 0.3s ease;
            position: relative;
            &::before{
              content: "";
              position: absolute;
              height: 2px;
              background-color: @prep-red;
              width: 0;
              left: 50%;
              bottom: -8px;
              transform: translateX(-50%);
              transition: 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55) all;
            }
            &:hover{
              color:@prep-red !important;
              &::before{
                width: 100%;
                border-bottom-color: @prep-red;
              }
            }
          }
        }
      }
      @media screen and (max-width: 767px){
        margin-left: 0;
        margin-top: 14px;
      }
      @media screen and (max-width: 860px){
        margin-left: 0;
        margin-top: 14px;
      }
    }
    &__searchbar{
      display: flex;
      align-items: center;
      margin-bottom: 0!important;
      //margin-right: 20px;
      position: relative;

      .search-bar__wrapper{
        width: 100%;
        margin-right: 30px;
        input{
          width: 300px;
          outline: none;
          padding: 6px;
          border-radius: 5px;
          transition: all 0.4s ease;
          font-size: small;
          border: none;
          @media screen and (max-width: 767px){
            width: 100%;
            &:focus{
              width: 100% !important;
            }
          }
          @media screen and (max-width: 860px){
            width: 100%;
            &:focus{
              width:100% !important;
            }
          }
          &:focus{
            width: 350px;
          }
        }
        button{
          background: @prep-white;
          border: none;
          outline: none;
          cursor: auto;
          color: @prep-red;
          margin-left: -35px;
          border-radius: 50%;
          padding: 5px;
          width: 30px;
          height: 30px;
        }
        ul{
          width:80%;
          position: absolute;
          min-height: 100px;
          max-height: 500px;
          overflow-y: scroll;
          @media screen and (max-width: 860px){
            width: 75%;
          }
          @media screen and (max-width: 767px){
            width: 88%;
          }
          @media screen and (max-width: 550px){
            width: 82% !important;
          }
          li{
            a{
              white-space: pre-line;
            }
          }
        }
        @media screen and (max-width: 767px){
          margin-right: 10px;
        }
      }
      @media screen and (max-width: 767px){
        width: 100%;
        margin-top: 20px !important;
      }
      @media screen and (max-width: 860px){
        width: 100%;
        margin-top: 30px;
      }
    }
    &__userProfile{
      .profile-wrapper{
        border: 2px solid #fff;
        padding: 1rem;
        border-radius: 50%;
        height: 50px;
        width: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        span{
          border: 1px solid @prep-white;
          border-radius: 50%;
          img{
            transform: scale(0.9);
            object-fit: cover;
          }

        }
      }
    }
  }
}

#prepjoyRegBtn{
    background: @prep-red;
    color: @prep-white;
}

#prepjoyLoginModal{
  font-family: Righteous;
  #prepjoyRegModalContent{
    h4{
      color:@prep-red;
    }

    #regLoginForm{
      padding: 1rem;
      .prepjoyLoginBtn{
        background: @prep-red;
        color:@prep-white;
        margin-top: 10px;
      }
    }
  }
}

.prepjoy-profile{
  background: @prep-secondary;
  text-decoration: none;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 0 8px -3px @prep-white;
  transition: all 0.3s ease;
  font-family: Righteous;
  width: 30%;
  height: 100vh;
  z-index: 9999999;
  @media screen and (max-width: 767px){
    width: 100% !important;

  }
  @media screen and (max-width: 800px){
    width: 50%;
  }
  &__content{
    flex-direction: column;
    height: 85%;

    img{
      border: 2px solid @prep-white;
      object-fit: cover;
    }
    .logoutDiv{
      margin-top: auto;
    }
    @media screen and (max-width: 767px){
      flex-direction: column;
    }

  }
}

.logout{
  text-decoration: none;
  color: @prep-red;
  font-size: 1.2rem;
}
#userCurBadge{
  color: @prep-red !important;
}

.ebooks .ebooks_filter select.background-bg{
  background: @prep-red !important;
  color: @prep-white !important;
  border-color: @prep-red !important;
}


//HAMBURGER
.prepjoy-header__hamburger {
  width: 30px;
  height: 0;
  position: relative;
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -o-transform: rotate(0deg);
  transform: rotate(0deg);
  -webkit-transition: .5s ease-in-out;
  -moz-transition: .5s ease-in-out;
  -o-transition: .5s ease-in-out;
  transition: .5s ease-in-out;
  cursor: pointer;
  display: none;

  @media screen and (max-width: 767px){
    display: block;
  }
  @media screen and (max-width: 860px){
    display: block;
  }

  span {
    display: block;
    position: absolute;
    height: 3px;
    width: 100%;
    background: @prep-white;
    border-radius: 9px;
    opacity: 1;
    left: 0;
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
    -webkit-transition: .25s ease-in-out;
    -moz-transition: .25s ease-in-out;
    -o-transition: .25s ease-in-out;
    transition: .25s ease-in-out;
    &:nth-child(1) {
      top: 0px;
    }
    &:nth-child(2) {
      top: 10px;
    }
    &:nth-child(3) {
      top: 20px;
    }
  }
}
.prepjoy-header__hamburger.open {
  span {
    &:nth-child(1) {
      top: 18px;
      -webkit-transform: rotate(135deg);
      -moz-transform: rotate(135deg);
      -o-transform: rotate(135deg);
      transform: rotate(135deg);
    }
    &:nth-child(2) {
      opacity: 0;
      left: -60px;
    }
    &:nth-child(3) {
      top: 18px;
      -webkit-transform: rotate(-135deg);
      -moz-transform: rotate(-135deg);
      -o-transform: rotate(-135deg);
      transform: rotate(-135deg);
    }
  }
}


//sigup
.modal-content{
  border: none;
}
#prepjoySignupModalBody{
  background: @theme-primary-color;
  //background: @prep-red;

}
.form {
  .input {
    margin-top: 20px;
    text-align: left;
    .inputBox {
      //margin-top: 20px;
      label {
        display: block;
        color: #868686;
        margin-bottom: 5px;
        font-size: 18px;
      }
      input {
        width: 100%;
        height: 50px;
        color: lightgrey;
        background: @prep-secondary !important;
        //background: @prep-primary !important;
        border: none;
        outline: none;
        border-radius: 40px;
        padding: 7px 15px;
        font-size: 14px;
        font-weight: lighter !important;
        box-shadow: inset -2px -2px 6px rgba(255, 255, 255, 0.1), inset 2px 2px 6px rgba(0, 0, 0, 0.8);
        &::placeholder {
          color: #555;
          font-size: 14px;
        }
      }
      input[type=number] {
        -moz-appearance: textfield;
      }
      .editPencil{
        color: #555;
        font-size: 18px;
        margin-left: -30px;
        margin-top: 5px;
      }
      input[type="button"] {
        //margin-top: 20px;
        height: 40px!important;
        background: @prep-red !important;
        color: @prep-white!important;
        box-shadow: -2px -2px 6px rgba(255, 255, 255, 0.1), 2px 2px 6px rgba(0, 0, 0, 0.8);
        &:active {
          color: #006c9c;
          margin-top: 20px;
          box-shadow: inset -2px -2px 6px rgba(255, 255, 255, 0.1), inset 2px 2px 6px rgba(0, 0, 0, 0.8);
        }
      }

      input[type=number]::-webkit-inner-spin-button,
      input[type=number]::-webkit-outer-spin-button {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        margin: 0;
      }

    }
  }
}

#linkTestandLibraryPrepjoy{
  background: @prep-red !important;
  color: @prep-white !important;
  border: none;
}
.userProfile > div{
  padding: 15px !important;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper{
  background-color: transparent !important;
}

.linkWrapper {
  display: flex;

  #playstr, #appstr {
    width: 200px;
    color: #fff;
    border-radius: 5px;
    border: 1px solid @prep-red;
    padding:5px;
    transition: all 0.3s ease;

    @media screen and (max-width: 900px) {
      padding: 0;
    }
    &:hover {
      color: @prep-red;
      border-color: #9999;
    }

    .dwnp {
      @media screen and (max-width: 900px) {
        font-size: 12px !important;
      }
    }
    .dwnh{
      @media screen and (max-width: 900px) {
        font-size: 14px !important;
      }
    }
  }
}

.browse-wrapper{
  @media screen and (max-width: 900px) {
    padding: 0!important;
  }
}
#bookNewDescription{
  margin-top: 100px;
}
#contPurchase{
  background: @prep-red !important;
  border: 1px solid @prep-red!important;
  color: @prep-white;
}
.ebooks{
  min-height: 650px;
}

@media screen and (max-width: 990px){
  .header-wrapper{
    flex-direction: column;
  }
  .header-wrapper__navbar{
    margin-left: 0;
  }

}

@media screen and (max-width: 900px){
  .navbar{
    padding: 0.5rem 0!important;
  }
}

.mic{
  cursor: pointer!important;
}

.ldbar {
  /*width:500px;*/
  margin:0 auto;
  border-radius:10px;
  border:4px solid transparent;
  position:relative;
  padding:1px;
  top: -30px;
  &:before {
    content:'';
    /*border:1px solid #fff;*/
    border-radius:10px;
    position:absolute;
    top:-4px;
    right:-4px;
    bottom:-4px;
    left:-4px;
  }
  .ldbardiv {
    position:absolute;
    border-radius:10px;
    top:0;
    right:100%;
    bottom:0;
    left:0;
    background:red;
    width:0;
    animation:borealisBar 1s linear infinite;
  }
}

@keyframes borealisBar {
  0% {
    left:0%;
    right:100%;
    width:0%;
  }
  10% {
    left:0%;
    right:75%;
    width:25%;
  }
  90% {
    right:0%;
    left:75%;
    width:25%;
  }
  100% {
    left:100%;
    right:0%;
    width:0%;
  }
}

#salesData{
  color: #fff !important;
}
.content-wrapper{
  h6{
    color: #fff;
  }
}
.btn-primary-modifier{
  background: @prep-red !important;
  color: #fff !important;
}

.badge-overlay {
  position: absolute;
  left: -15px;
  top: -15px;
  width: 40px;
  height: 40px;
  z-index: 100;
  -webkit-transition: width 1s ease, height 1s ease;
  -moz-transition: width 1s ease, height 1s ease;
  -o-transition: width 1s ease, height 1s ease;
  transition: width 0.4s ease, height 0.4s ease;
  @media @extraSmallDevices, @smallDevices {
    left: -10px;
    top: -10px;
  }
}
.top-left {
  position: absolute;
  top: 0;
  left: 0;
  -ms-transform: translateX(-30%) translateY(0%) rotate(-45deg);
  -webkit-transform: translateX(-30%) translateY(0%) rotate(-45deg);
  transform: translateX(-30%) translateY(0%) rotate(-45deg);
  -ms-transform-origin: top right;
  -webkit-transform-origin: top right;
  transform-origin: top right;
}
.badge {
  margin: 0;
  padding: 5px;
  color: white;
  line-height: 1;
  background: #01b901;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: pre-wrap;
}

//Percentage,Rating option
.percentageOff{
  background: #01b901;
  padding: 2px;
  width: 100px;
  text-align: center;
  color: #fff;
  border-radius: 2px;
}
.star-rating i{
  color: #F79420;
}
.shimmer {
  background-repeat: no-repeat;
  animation: shimmer 2.5s infinite;
}

@keyframes shimmer {
  100% {-webkit-mask-position:left}
}

//Additional styles
.notificationContainer{
  padding-bottom: 2rem;
  #notificationForm{
    label{
      color: #fff !important;
    }
    input{
      height: auto !important;
    }
    #btnSend{
      width: 200px;
      background: @prep-red;
      color: #fff;
      border: 1px solid @prep-red;
      display: block;
      @media @extraSmallDevices, @smallDevices {
        width: 100%;
      }
    }
  }
}
.list_price{
  color: #FF4B33;
}
.offer_price{
  color: #212121;
}
#buyNow{
  width: 100%;
}
.ebook_detail .book_info .book_variants a.card .card-body{
  min-height: 130px;
}
.ebooks .global-search button {
  position: relative;
  z-index: 10;
  width: 48px;
  height: 48px;
  margin-left: -48px;
  color: #000000b3 !important;
}
.global-search {
  display: none !important;
  width: 96%;
  margin: 0 auto;
}

#search-book-store{
  height: 40px !important;
}
.ebooks .global-search button{
  background: #E83500 !important;
  opacity: 1;
  height: 40px;
}

.affiliationPrices{
  margin-top: 10px;
  width: fit-content;
  background: #fff;
  padding: 15px;
  border-radius: 10px;
  &_title{
    p{
      //font-size: 18px;
      font-weight: 500;
      color: #000 !important;
      span{
        //color: #F79420;
      }
    }
  }
  .affiliationLinks{
    display: flex;
    gap: 1rem;
    margin-top: 10px;
    .fieldSet{
      position: relative;
      //border: 1px solid #0005;
      border: 1px solid orange;
      padding: 8px;
      width: 140px;
      border-radius: 5px;
      //background: #FFF0CC;
      &:hover{
        border: 1px solid orange;
      }
      span{
        img{
          width: 60px;
          &.flipkartLogo{
            width: 70px;
            position: relative;
            z-index: 2;
          }
        }
      }
      .fieldSet_legend{
        position: absolute;
        top: 0;
        margin: -9px 0 0 -0.5rem;
        background: #fff;
        //background: linear-gradient(to bottom,#fff,#FFF0CC);
        margin-left: 65px;
        width: 55px;
        text-align: center;
        z-index: 1;
        font-size: 18px;
        font-weight: 500;
        color: #000 !important;
        span{
          margin-left: -4px;
        }
      }
    }
  }
}

.affiliationLinks .fieldSet:hover{
  background: #FFF0CC;
}
.affiliationLinks .fieldSet:hover .fieldSet_legend{
  background: linear-gradient(to bottom,#fff,#FFF0CC);
}

.affiliationLinksLoader{
  border: 1px solid #0003;
  width: 140px;
  height: 50px;
  border-radius: 5px;
  background: linear-gradient(to right, #F6F6F6 8%, #F0F0F0 18%, #F6F6F6 33%);
  display: flex;
  align-items: center;
  justify-content: center;
  p{
    font-size: 12px;
    font-weight: 500;
    color: #0008 !important;
    text-align: center;
    line-height: initial;
  }
}


.aa {
  &:after {
    position: absolute;
    margin-left: .1rem;
    content: ' ...';
    animation: loading steps(4) 2s infinite;
    clip: rect(auto, 0px, auto, auto);
  }
}
.badge-info{
  display: initial;
  border-radius: 50px;
  font-size: 10px;
  padding: 3px 5px;
  font-weight: normal;
  background: #17a2b8;
  position: absolute;
  right: 1px;
  top: 1px;
  height: auto;
  width: 60px;
}
@keyframes loading {
  to {
    clip: rect(auto, 20px, auto, auto);
  }
}

#addedIconAnimation {
  position: relative;
  width: 60px;
  height: 60px;
  margin: 20px auto;
  &:before {
    -webkit-animation: pulseWarning 2s linear infinite;
    animation: pulseWarning 2s linear infinite;
    background-color: lighten(@success-btn,40%);
    border-radius: 50%;
    content: "";
    display: inline-block;
    height: 100%;
    opacity: 0;
    position: absolute;
    width: 100%;
    left: 0;
    right: 0;
  }
  &:after {
    background-color: @white;
    border-radius: 50%;
    content: "";
    display: block;
    height: 100%;
    position: absolute;
    width: 100%;
    z-index: 1;
    left: 0;
    right: 0;
  }
}

.checkmark__circle {
  stroke-dasharray: 166;
  stroke-dashoffset: 166;
  stroke-width: 2;
  stroke-miterlimit: 10;
  stroke: @success-btn;
  fill: none;
  animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
}

.checkmark {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: block;
  stroke-width: 2;
  stroke: @white;
  stroke-miterlimit: 10;
  margin: 0;
  box-shadow: inset 0 0 0 @success-btn;
  animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
  position: absolute;
  z-index: 2;
  right: 0;
  left: 0;
}

.checkmark__check {
  transform-origin: 50% 50%;
  stroke-dasharray: 48;
  stroke-dashoffset: 48;
  animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
}

.f-modal-alert .f-modal-icon {
  border-radius: 50%;
  border: 3px solid transparent;
  box-sizing: content-box;
  height: 56px;
  margin: 20px auto;
  padding: 0;
  position: relative;
  width: 56px;

  &.f-modal-warning {
    border-color: #F8BB86;

    &:before {
      -webkit-animation: pulseWarning 2s linear infinite;
      animation: pulseWarning 2s linear infinite;
      background-color: #ffd9b9;
      border-radius: 50%;
      content: "";
      display: inline-block;
      height: 100%;
      opacity: 0;
      position: absolute;
      width: 100%;
      left: 0;
      right: 0;
    }

    &:after {
      background-color: @white;
      border-radius: 50%;
      content: "";
      display: block;
      height: 100%;
      position: absolute;
      width: 100%;
      z-index: 1;
      left: 0;
      right: 0;
    }
  }

  &.f-modal-warning .f-modal-body {
    background-color: #F8BB86;
    border-radius: 2px;
    height: 26px;
    left: 50%;
    margin-left: -1px;
    position: absolute;
    top: 10px;
    width: 4px;
    z-index: 2;
  }

  &.f-modal-warning .f-modal-dot {
    background-color: #F8BB86;
    border-radius: 50%;
    bottom: 10px;
    height: 6px;
    left: 50%;
    margin-left: -2px;
    position: absolute;
    width: 6px;
    z-index: 2;
  }

  + .f-modal-icon {
    margin-top: 50px;
  }
}

.scaleAnimation {
  -webkit-animation: scaleAnimation 1s infinite alternate;
  animation: scaleAnimation 1s infinite alternate;
}

.pulseAnimationIns {
  -webkit-animation: pulseAnimationIns 0.75s infinite alternate;
  animation: pulseAnimationIns 0.75s infinite alternate;
}
.header-wrapper__navbar .mobile_cart_icon{
  position: relative;
}

.header-wrapper__navbar .cart_count {
  position: absolute;
  top: -10px;
  left: 10px;
  right: -10px;
  width: 17px;
  height: 18px;
  text-align: center;
  background: @white;
  color: #000;
  border-radius: 50px;
  font-size: 11px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  line-height: normal;
  font-weight: 500;
}

@keyframes stroke {
  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes scale {
  0%,
  100% {
    transform: none;
  }
  50% {
    transform: scale3d(1.1, 1.1, 1);
  }
}

@keyframes fill {
  100% {
    box-shadow: inset 0 0 0 30px @success-btn;
  }
}

@keyframes scaleAnimation {
  0% {
    transform: scale(1);
  }
  30% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes pulseWarning {
  0% {
    //background-color: #fff;
    transform: scale(1);
    opacity: 0.5;
  }
  30% {
    //background-color: #fff;
    transform: scale(1);
    opacity: 0.5;
  }
  100% {
    //background-color: #F8BB86;
    transform: scale(2);
    opacity: 0;
  }
}
@keyframes pulseAnimationIns {
  0% {
    background-color: #F8D486;
  }
  100% {
    background-color: #F8BB86;
  }
}
#cartModalBtns .btn-primary{
  background: @prep-red !important;
}
@import "variables/color.less";
@import "variables/fonts.less";
.about-banner{
  img{
    width: 100%;
    border-bottom: 1px solid #DDDDDD;
  }
}
.aboutus{
  padding-bottom: 4rem;
  p{
    color:rgba(119,119,119,1);
    font-size: 20px;
    a{
      color:rgba(56,63,139,1);
      text-decoration: underline;
      //font-weight: bold;
      font-family: @robotoBold;
      font-size: 20px;
    }
  }
}
.contactus{
  h1{
    color:#000000;
    font-size: 30px;
    font-family: @robotoBold;
  }
  p{
    color:#777777;
    font-size: 15px;
    font-family: @sageFont;
  }
  a{
    font-size: 15px;
    color:rgba(56,63,139,1);
    text-decoration: underline;
  }
  .contact_form {
    .form-check {
      label {
        font-family: @sageFont;
      }
    }
    label {
      font-family: @sageFont;
    }
    textarea {
      min-height: 120px;
    }
  }
}
.privacy,.terms{
  h2{
    color:#333;
    font-size: 20px;
    font-family: @sageFont;
    margin-top: 2rem;

  }
  p{
    color:#777777;
    font-size: 15px;
    font-family: @sageFont;
    margin-bottom: 0;
    a{
      color:rgba(56,63,139,1);
      text-decoration: underline;
      font-size: 15px;
    }
  }
  ul{
    li{
      color:#777777;
      font-size: 15px;
      font-family: @sageFont;
    }
  }
  h3{
    font-size: 18px;
    font-family: @sageFont;
    color:#333333;
    margin-top: 1rem;
  }
  h1{
    //margin-top: 1rem;
    font-size: 30px;
    font-family: @sageFont;
    color:#000000;
  }
}
.feedback_form .form-row {
  /*p {
    margin-bottom: 0;
  }*/
}
.custom-control-label::before {
  background-color: #fff;
  border: #adb5bd solid 1px;
  box-shadow: 0 0 3px;
}
.custom-control input[type="radio"]:focus + label {
  border: none !important;
}
.custom-control input[type="radio"]:checked + label {
   background-color: transparent !important;
  color: #777777 !important;
}
.custom-control {
  display: inline-flex;
}
.custom-control-label::after, .custom-control-label::before {
  top: 0;
}
.custom-control-label {
  font-size: 14px;
  color: #777777 !important;
}


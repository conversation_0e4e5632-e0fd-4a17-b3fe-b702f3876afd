@import "../variables/responsive.less";
@import "color.less";
@import "common.less";

@theme_font: 'Poppins', sans-serif;

#ws_landing {

  //Ask Doubts
  .ask_doubts {
    position: fixed;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    z-index: 9992;
    @media @iPhone{
      right: 0;
    }
    a {
      background-color: @theme1;
      box-sizing: border-box;
      border-radius: 10px;
      padding: 12px 25px;
      height: 45px;
      color: @white;
      font-weight: 300;
      transition: all 0.2s linear;
      -webkit-transition: all 0.2s linear;
      -moz-transition: all 0.2s linear;
      -ms-transition: all 0.2s linear;
      &:hover {
        text-decoration: none;
        box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
      }
      &:focus {
        background-color: @theme1 !important;
      }
      p {
        padding-left: 10px;
        width: 100%;
      }
    }
  }

  //Hero Section
  .hero_section {
    h2 {
      font-weight: bold;
      padding-bottom: 0.5rem;
      font-size: 36px;
    }
    .hero_subtitle{
      h6{
        font-size: 18px;
      }
    }
    h6 {
      font-family: @theme_font;
    }
    a {
      .card {
        border-radius: 10px;
        color: #fff;
        border: none;
        height: 160px;
        transition: all 0.2s linear;
        -webkit-transition: all 0.2s linear;
        -moz-transition: all 0.2s linear;
        -ms-transition: all 0.2s linear;
        img {
          position: absolute;
          width: 38px;
          height: 50px;
          right: 0px;
          top:0px;
        }
        .card-body {
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
          h5.card-title {
            font-family: @theme_font;
            font-size: 18px;
            margin-bottom: 7px;
          }
          p.card-text {
            font-size: 13px;
            line-height: 1.2;
          }
        }
        &.ebooks_bg {
          background: @white;
          h5{
            color:#AA15EB;
            font-weight: bold;
          }
          p{
            color:#444444;
          }
        }
        &.live_classes_bg {
          background: @white;
          h5{
            color:#E9AC00;
            font-weight: bold;
          }
          p{
            color:#444444;
          }
        }
        &.mymaterials_bg {
          background: @white;
          h5{
            color:#05B717;
            font-weight: bold;
          }
          p{
            color:#444444;
          }
        }
        &.current_affairs_bg {
          background: @white;
          h5{
            color:#8CBD00;
            font-weight: bold;
          }
          p{
            color:#444444;
          }
        }
      }
      &:hover {
        text-decoration: none;
        .card {
          margin-top: -5px;
        }
      }
    }
    .icon_modules {
      div {
        //width: 100px;
        //display: block;
        a:hover {
          text-decoration: none;
          //font-weight: 500;
        }
      }
      img{
        height: 110px;
      }
    }
  }

  //Partners Section
  .partners_section {
    .partner_title {
      p {
        color: @text-darkcolor;
        margin-bottom: 15px;
      }
      h2 {
        font-weight: bold;
        img {
          width: 220px;
        }
      }
    }
    a {
      .card {
        box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
        border-radius: 10px;
        //width: 160px;
        height: 170px;
        margin: 0 auto;
        transition: all 0.2s linear;
        -webkit-transition: all 0.2s linear;
        -moz-transition: all 0.2s linear;
        -ms-transition: all 0.2s linear;
        img {
          position: absolute;
          left: 0;
          right: 0;
          top: 40px;
          width: 60px;
          margin: 0 auto;
        }
        .card-body {
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
          p {
            color: @text-color;
            //font-weight: 300;
            transition: all 0.2s linear;
            -webkit-transition: all 0.2s linear;
            -moz-transition: all 0.2s linear;
            -ms-transition: all 0.2s linear;
          }
        }
      }
      &:hover {
        text-decoration: none;
        .card {
          margin-top: -5px;
          p {
            color: @purple;
            font-weight: 500;
          }
        }
      }
    }
    .partners_message {
      p {
        color: @text-darkcolor;
      }
    }
    .know_more_btn {
      a {
        background-color: @btn-color;
        padding: 15px 40px;
        border-radius: 50px;
        color: #FFFFFF;
        transition: all 0.2s linear;
        -webkit-transition: all 0.2s linear;
        -moz-transition: all 0.2s linear;
        -ms-transition: all 0.2s linear;
        &:hover {
          box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
        }
        &:focus {
          background-color: @btn-color;
        }
      }
    }
  }

  //Partners logo section
  .partners_logos_section {
    .partner_logos {
      div {
        padding: 0 30px;
        margin-bottom: 20px;
        img {
          width: 180px;
        }
      }
    }
  }

.ws-cards-0{
   .card{
     box-shadow: 0px 4px 10px rgba(170,21,235,0.30);
   }
}
  .ws-cards-1{
    .card{
      box-shadow: 0px 4px 10px rgba(5,183,23,0.30);
    }
  }
  .ws-cards-2{
    .card{
      box-shadow: 0px 4px 10px rgba(140,189,0,0.30);
    }
  }
  .ws-cards-3{
    .card{
      box-shadow: 0px 4px 10px rgba(233,172,0,0.30);
    }
  }
}
.home-search {
  position: absolute;
  right: 0px;
  top: 1.5px;
  background-color: transparent !important;
}

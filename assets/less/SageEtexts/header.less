@import "variables/color.less";
@import "variables/fonts.less";
html{
  p,a,h1,h2,h3,h4,span,td,th{
    font-family: @sageFont;
  }
}
header{
  background:linear-gradient(to bottom, white 80%, #e0e0e0);
  height:auto;
  display: flex;
  align-items: center;
  >div{
    height: 100%;
    display: flex;
    align-items: center;
  }
  .logo{
    /*background-color: #ffffff;*/
    padding: 0;
    /*border-top-right-radius: 50px;
    border-bottom-right-radius: 50px;*/
    display: flex;
    align-items: center;
    /*height: 60px;*/
    img{
      width:300px;
      //border-top-right-radius: 50px;
      //border-bottom-right-radius: 50px;
    }
  }
  .right-menu{
    /*background: #ffffff;*/
    padding: 1rem;
    /*border-top-left-radius: 50px;
    border-bottom-left-radius: 50px;*/
    display: flex;
    align-items: center;
    height: 60px;

  }
  .ws-header{
    .navbar-nav{
      .nav-link{
        color:@black;
      }
      &.right-menu{
        li{
          .nav-link{
            color:@black;
            padding: 0 1.2rem;
            font-size: 15px;
          }
        }
      }
    }
  }
}
.login-btn{
  background: @sageTheme;
  color:#fff !important;
  border-radius: 4px;
  font-family: @sageFont !important;
  text-transform: capitalize !important;
  padding: 6px 40px !important;
}
.evidyaLogin{
  display: none;
  &::before {
    content: "";
    width: 0px;
    height: 0px;
    border: 0.8em solid transparent;
    position: absolute;
    right: 30px;
    top: -20px;
    border-bottom: 10px solid #ffffff;

  }
  background: #fff;
  box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
  width: 350px;
  min-height: 400px;
  position: absolute;
  top:4rem;
  right: 20px;
  z-index: 9;
  padding: 1rem;
  padding-bottom: 2rem;
  form{

  }
  button{
    width: 100%;
    margin-top: 1rem;
    outline:0;
    height: 40px;
  }
  .btn-login{
    background: @sageTheme;
    color:#fff;
    width: 100%;
    margin-top: 1rem;
  }
  .btn-create{
    border:1px solid @sageTheme;
    background: none;
    color:@sageTheme;
  }
  .googleLogin,.facebookLogin{
    border-radius: 50px;
    color:#fff;
    background: none;
    border:none;
    outline:0;
  }
  .googleLogin{
    background-color:rgb(217,89,64);
  }
  .facebookLogin{
      background-color:rgb(76,124,207);
  }
  .frgtPassword{
    display: block;
    margin-top: 10px;
    color:@sageTheme;
  }
}
.evidyaSignup{
h3{
  font-size: 20px;
}
  .loginContinue{
    font-size: 16px;
    display: block;
    color:@sageTheme !important;
    cursor: pointer;
    &:hover{
      /*color:rgb(217,89,64) !important;*/
    }
  }
  #signup{
    width: 100%;
    background: @sageTheme;
    color:#ffffff;
  }

}
.sageEvidya{
  position: relative;
  .order-pr{
    display: none;
  }
}
.evidya .user_profile{
  min-height: calc(100vh - 175px);
}
.evidyaloginWrapper{
  label{
    font-weight: normal;
  }
}
.forgotPassword{
  label{
    font-weight: normal;
  }
}
.evidyaSignup{
  label{
    font-weight: normal;
  }
}
.evidya{
  .ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .edit-btn {
    width: 20px;
    height: 20px;
    display: inline-block;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.94);
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    position: absolute;
    left: 57px;
    bottom: 9px;
    text-align: center;
  }
  .input-login label > span {
    position: absolute;
    font-weight: normal;
  }
.bookTemplate{
  .tab-header > .navbar{
    position: sticky;
    box-shadow: none;
  }
  .content-wrapper{
    margin-top: 0;
  }
  .shadowHeader{

  }
}
  &.custom-fix{
    .bookTemplate{
      .shadowHeader{
         position: fixed;
        top:0;
      }
    }
  }
  .bookTemplate .content-wrapper #book-sidebar .side-content > h2{
    margin-top: 100px;
  }
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .media .media-body p {
  font-family: @sageFont;
  font-size: 15px;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .dropdown-item {
  padding: 0.4rem 1rem;
  font-size: 15px;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .dropdown-item:active {
  background-color: transparent;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu {
  min-width: 180px;
  margin-top: 10px;
  padding-top: 0;
  box-shadow: 0 5px 10px 0 #ccc;
}

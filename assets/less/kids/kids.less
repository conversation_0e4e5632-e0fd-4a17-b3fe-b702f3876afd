@import "../kids/kids-theme.less";
@import "../reboot.less";
@import "../variables/ws_fonts.less";
@import "../variables/responsive.less";

body{
  background: #C6FFDD;  /* fallback for old browsers */
  background: -webkit-linear-gradient(to right, #f7797d, #FBD786, #C6FFDD);  /* Chrome 10-25, Safari 5.1-6 */
  background: linear-gradient(to right, #f7797d, #FBD786, #C6FFDD); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
  height: 100vh;
}
.language-wrapper {
  background: url('../images/kids/kidsBg.svg') no-repeat center;
  background-size: cover;
  width: 100%;
  height: 100vh;

  .jumbotron {
    background: transparent;
    padding-bottom: 10px;
    @media @iPhone{
      padding-bottom: 2rem;
    }
    &#age-content {
      @media @iPhone {
        padding-bottom: 2rem;
      }
    }
  }
}
.languages,.age-container{
  div{
    div{
      margin-right: 3rem;
      @media @iPhone{
        margin-right: 15px;
      }
    }
  }
}
.container{
  @media @iPhone{
    padding: 0;
  }
}
input[type='radio'],input[type='checkbox']{
  display: none;
}
input[type=radio]:checked ~ label{
  border:4px solid @kids-theme;
  color:@kids-theme;
  font-weight: @ws-header-fontWeight;
}
input[type=checkbox]:checked ~ label{
  border:4px solid @kids-theme;
  color:@kids-theme;
  font-weight: @ws-header-fontWeight;
}
.language{
  //background: @white;
  height: 130px;
  width: 130px;
  border-radius: 8px;
  box-shadow: @btn-shadow;
  display: flex;
  align-items: center;
  justify-content: center;
  text-transform: capitalize;
  font-family: @ws-header-font;
  background: url('../images/kids/language.svg') no-repeat 0% 10%;
  background-size: contain;
  background-color: @white;
  @media @iPhone{
    width: 100px;
    height: 100px;
  }
}
h4.header-text{
  font-family: 'Quicksand', sans-serif;
  font-weight: bold;
  font-size: 32px;
  margin-top: 3rem;
  color:@white;
  @media @iPhone{
    margin-top: 0rem;
    font-size: 26px;
  }
}
.next{
 // border: 3px solid rgba(189, 197, 129, 0.5);
  border-radius: 8px;
  background: #2EBAC6;
  box-shadow: @btn-shadow;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  color:@white;
  padding: 1rem 2rem;
  margin-top: 2rem;
  border: 1px solid;
  @media @iPhone{
    min-height: 50px;
    padding: 0.5rem 2rem;
  }
  i{
    font-size: 16px;
    margin-left: 10px;
  }
  &:focus{
    outline: 0;
  }
}
.gender-container{
  height: 70%;
  input[type=radio]:checked + label{
    font-weight: bold;
  }
  label{
    height:300px;
    width: 300px;
    border-radius: 8px;
    box-shadow: @btn-shadow;
    display: flex;
    align-items: center;
    justify-content: center;
    text-transform: capitalize;
    font-family: 'Quicksand', sans-serif;
    font-size: 24px;
    background-color: @white;
    @media @iPhone{
      width: 100px;
      height: 100px;
      margin-right: 10px;
      font-size: 16px;
    }
     &.boy  {
        background-image: url('../images/kids/boy.svg');
       background-size: contain;
       background-position: center;
       background-repeat: no-repeat;
     }
    &.girl{
      background-image: url('../images/kids/girl.svg');
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;
    }
  }
}

.videos {
  .nav-tabs {
    border: none;
    position: sticky;
    top: 7rem;
    z-index: 999;

    li {
      a {
        background: @white;
        box-shadow: @btn-shadow;
        border-radius: 8px;
        min-width: 150px;
        min-height: 55px;
        margin-right: 1rem;
        text-align: center;
        font-family: @ws-header-font;
        color: @grey;
        display: flex;
        align-items: center;
        justify-content: center;
        @media @iPhone {
          margin-right: 5px;
          min-width: 90px;
          min-height: 45px;
          font-size: 12px;
        }

        &.active {
          border: 4px solid @kids-theme;
          font-weight: bold;
          color: @kids-theme;
        }
      }
    }
  }

  .tab-content {
    min-height: 250px;

    .card {
      border-radius: 8px;

      img {
        position: relative;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
      }
    }

    .card-body {
      background: url('../images/kids/videoBg.svg') no-repeat 0% 10%;
      background-size: contain;
      padding: 5px 10px;

      .text-content {
        align-items: center;
        min-height: 60px;

        p {
          //width: 90%;
        }

        i {
          display: none;
          cursor: pointer;
        }
      }

      .card-text {
        margin: 0;
      }
    }

    > div {
      > div {
        > div {
          &:last-child {
            padding-bottom: 2rem;
          }
        }
      }
    }
  }
}
.play{
  background: none;
  border:none;
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 250px;
  i{
    color:@kids-theme;
    background: @white;
    border-radius: 50px;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &:hover{
    text-decoration: none;
  }
}
#languageDropdown{
  div.mt-4{
    margin-top: 0 !important;
  }
  .language{
    border: none;
    border-radius: 0;
    height: 40px;
    width: auto;
    div{
      margin: 0 !important;
      div{
        margin: 0 !important;
      }
    }
  }
}
#modifyLanguage,#modifyAge,#modifyGender{
  text-transform: capitalize;
  max-width:180px;
  overflow:hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  min-width: 80px;
  margin-left: 1rem;
  @media @iPhone{
    max-width: 85px;
  }
}

.btn-wrapper{
  padding-top: 2rem;
  position: sticky;
  top:0;
  z-index: 999;
  @media @iPhone{
      padding-top: 3rem;
  }
  .nav-tabs{
    border:none;
    @media @iPhone{
      margin-top: 2rem;
    }
  }
  .nav-link{
    display: none;
     padding: 0.5rem;
    background: @white;
    border:2px solid @kids-theme;
    color:@grey;
    margin-right: 0.5rem;
    outline: 0;
    border-radius: 0px;
    text-align: center;
    @media @iPhone{
      font-size: 14px;
    }
    &.active{
      background: @kids-theme;
      color:@white;
    }
  }
}
#videoModal{
  .modal-content {
    background: none;
    border: none;
  }
  .modal-body {
    iframe {
      @media @iPhone {

      }
    }
  }
  .modal-header{
    border: none;
    .close{
      opacity: 1;
      text-shadow: none;
      color:#F79420;
    }
  }
  .modal-footer{
    border:none;
  }
}
#rhymesContent{
  .col-12{
    margin-bottom: 1rem;
  }
}

#background-wrap {
  bottom: 0;
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  z-index: -1;
}
.back-btn{
  background: #fff;
  border-radius: 50px;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top:1rem;
  left: 1rem;
  z-index: 9999;
  &:hover{
    text-decoration: none;
  }
}
.footer{
  position: fixed;
 top: 0;
  right:5px;
  z-index: 999;
  @media @iPhone{
    top:8px;
  }
  a:first-child{
    img{
      width: 150px;
      height: 50px;
      margin-top: 7px;
      @media @iPhone{
        width: 120px;
        height:44px;
      }
    }
  }
  a:last-child{
    img{
      width: 150px;
      height: 46px;
      @media @iPhone{
        width: 120px;
        height: 41px;
      }
    }
  }

}
/* KEYFRAMES */

//@-webkit-keyframes animateBubble {
//  0% {
//    margin-top: 1000px;
//  }
//  100% {
//    margin-top: -100%;
//  }
//}
//
//@-moz-keyframes animateBubble {
//  0% {
//    margin-top: 1000px;
//  }
//  100% {
//    margin-top: -100%;
//  }
//}
//
//@keyframes animateBubble {
//  0% {
//    margin-top: 1000px;
//  }
//  100% {
//    margin-top: -100%;
//  }
//}
//
//@-webkit-keyframes sideWays {
//  0% {
//    margin-left:0px;
//  }
//  100% {
//    margin-left:50px;
//  }
//}
//
//@-moz-keyframes sideWays {
//  0% {
//    margin-left:0px;
//  }
//  100% {
//    margin-left:50px;
//  }
//}
//
//@keyframes sideWays {
//  0% {
//    margin-left:0px;
//  }
//  100% {
//    margin-left:50px;
//  }
//}
//
///* ANIMATIONS */
//
//.x1 {
//  -webkit-animation: animateBubble 25s linear infinite, sideWays 2s ease-in-out infinite alternate;
//  -moz-animation: animateBubble 25s linear infinite, sideWays 2s ease-in-out infinite alternate;
//  animation: animateBubble 25s linear infinite, sideWays 2s ease-in-out infinite alternate;
//
//  left: -5%;
//  top: 5%;
//
//  -webkit-transform: scale(0.6);
//  -moz-transform: scale(0.6);
//  transform: scale(0.6);
//}
//
//.x2 {
//  -webkit-animation: animateBubble 20s linear infinite, sideWays 4s ease-in-out infinite alternate;
//  -moz-animation: animateBubble 20s linear infinite, sideWays 4s ease-in-out infinite alternate;
//  animation: animateBubble 20s linear infinite, sideWays 4s ease-in-out infinite alternate;
//
//  left: 5%;
//  top: 80%;
//
//  -webkit-transform: scale(0.4);
//  -moz-transform: scale(0.4);
//  transform: scale(0.4);
//}
//
//.x3 {
//  -webkit-animation: animateBubble 28s linear infinite, sideWays 2s ease-in-out infinite alternate;
//  -moz-animation: animateBubble 28s linear infinite, sideWays 2s ease-in-out infinite alternate;
//  animation: animateBubble 28s linear infinite, sideWays 2s ease-in-out infinite alternate;
//
//  left: 10%;
//  top: 40%;
//
//  -webkit-transform: scale(0.7);
//  -moz-transform: scale(0.7);
//  transform: scale(0.7);
//}
//
//.x4 {
//  -webkit-animation: animateBubble 22s linear infinite, sideWays 3s ease-in-out infinite alternate;
//  -moz-animation: animateBubble 22s linear infinite, sideWays 3s ease-in-out infinite alternate;
//  animation: animateBubble 22s linear infinite, sideWays 3s ease-in-out infinite alternate;
//
//  left: 20%;
//  top: 0;
//
//  -webkit-transform: scale(0.3);
//  -moz-transform: scale(0.3);
//  transform: scale(0.3);
//}
//
//.x5 {
//  -webkit-animation: animateBubble 29s linear infinite, sideWays 4s ease-in-out infinite alternate;
//  -moz-animation: animateBubble 29s linear infinite, sideWays 4s ease-in-out infinite alternate;
//  animation: animateBubble 29s linear infinite, sideWays 4s ease-in-out infinite alternate;
//
//  left: 30%;
//  top: 50%;
//
//  -webkit-transform: scale(0.5);
//  -moz-transform: scale(0.5);
//  transform: scale(0.5);
//}
//
//.x6 {
//  -webkit-animation: animateBubble 21s linear infinite, sideWays 2s ease-in-out infinite alternate;
//  -moz-animation: animateBubble 21s linear infinite, sideWays 2s ease-in-out infinite alternate;
//  animation: animateBubble 21s linear infinite, sideWays 2s ease-in-out infinite alternate;
//
//  left: 50%;
//  top: 0;
//
//  -webkit-transform: scale(0.8);
//  -moz-transform: scale(0.8);
//  transform: scale(0.8);
//}
//
//.x7 {
//  -webkit-animation: animateBubble 20s linear infinite, sideWays 2s ease-in-out infinite alternate;
//  -moz-animation: animateBubble 20s linear infinite, sideWays 2s ease-in-out infinite alternate;
//  animation: animateBubble 20s linear infinite, sideWays 2s ease-in-out infinite alternate;
//
//  left: 65%;
//  top: 70%;
//
//  -webkit-transform: scale(0.4);
//  -moz-transform: scale(0.4);
//  transform: scale(0.4);
//}
//
//.x8 {
//  -webkit-animation: animateBubble 22s linear infinite, sideWays 3s ease-in-out infinite alternate;
//  -moz-animation: animateBubble 22s linear infinite, sideWays 3s ease-in-out infinite alternate;
//  animation: animateBubble 22s linear infinite, sideWays 3s ease-in-out infinite alternate;
//
//  left: 80%;
//  top: 10%;
//
//  -webkit-transform: scale(0.3);
//  -moz-transform: scale(0.3);
//  transform: scale(0.3);
//}
//
//.x9 {
//  -webkit-animation: animateBubble 29s linear infinite, sideWays 4s ease-in-out infinite alternate;
//  -moz-animation: animateBubble 29s linear infinite, sideWays 4s ease-in-out infinite alternate;
//  animation: animateBubble 29s linear infinite, sideWays 4s ease-in-out infinite alternate;
//
//  left: 90%;
//  top: 50%;
//
//  -webkit-transform: scale(0.6);
//  -moz-transform: scale(0.6);
//  transform: scale(0.6);
//}
//
//.x10 {
//  -webkit-animation: animateBubble 26s linear infinite, sideWays 2s ease-in-out infinite alternate;
//  -moz-animation: animateBubble 26s linear infinite, sideWays 2s ease-in-out infinite alternate;
//  animation: animateBubble 26s linear infinite, sideWays 2s ease-in-out infinite alternate;
//
//  left: 80%;
//  top: 80%;
//
//  -webkit-transform: scale(0.3);
//  -moz-transform: scale(0.3);
//  transform: scale(0.3);
//}
//
///* OBJECTS */
//
//.bubble {
//  -webkit-border-radius: 50%;
//  -moz-border-radius: 50%;
//  border-radius: 50%;
//
//  -webkit-box-shadow: 0 20px 30px rgba(0, 0, 0, 0.2), inset 0px 10px 30px 5px rgba(255, 255, 255, 1);
//  -moz-box-shadow: 0 20px 30px rgba(0, 0, 0, 0.2), inset 0px 10px 30px 5px rgba(255, 255, 255, 1);
//  box-shadow: 0 20px 30px rgba(0, 0, 0, 0.2), inset 0px 10px 30px 5px rgba(255, 255, 255, 1);
//
//  height: 200px;
//  position: absolute;
//  width: 200px;
//}
//
//.bubble:after {
//  background: -moz-radial-gradient(center, ellipse cover,  rgba(255,255,255,0.5) 0%, rgba(255,255,255,0) 70%); /* FF3.6+ */
//  background: -webkit-gradient(radial, center center, 0px, center center, 100%, color-stop(0%,rgba(255,255,255,0.5)), color-stop(70%,rgba(255,255,255,0))); /* Chrome,Safari4+ */
//  background: -webkit-radial-gradient(center, ellipse cover,  rgba(255,255,255,0.5) 0%,rgba(255,255,255,0) 70%); /* Chrome10+,Safari5.1+ */
//  background: -o-radial-gradient(center, ellipse cover,  rgba(255,255,255,0.5) 0%,rgba(255,255,255,0) 70%); /* Opera 12+ */
//  background: -ms-radial-gradient(center, ellipse cover,  rgba(255,255,255,0.5) 0%,rgba(255,255,255,0) 70%); /* IE10+ */
//  background: radial-gradient(ellipse at center,  rgba(255,255,255,0.5) 0%,rgba(255,255,255,0) 70%); /* W3C */
//  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#80ffffff', endColorstr='#00ffffff',GradientType=1 ); /* IE6-9 fallback on horizontal gradient */
//
//  -webkit-border-radius: 50%;
//  -moz-border-radius: 50%;
//  border-radius: 50%;
//
//  -webkit-box-shadow: inset 0 20px 30px rgba(255, 255, 255, 0.3);
//  -moz-box-shadow: inset 0 20px 30px rgba(255, 255, 255, 0.3);
//  box-shadow: inset 0 20px 30px rgba(255, 255, 255, 0.3);
//
//  content: "";
//  height: 180px;
//  left: 10px;
//  position: absolute;
//  width: 180px;
//}
#errorModal{
  .modal-header{
    border:none;
  }
  .modal-footer{
    border:none;
  }
  .modal-body{
    text-align: center;
    img{
      width: 100px;
      height: 80px;
    }
    p{
      margin-top: 1rem;
      font-family: 'Quicksand', sans-serif;
      color:#c23616;
    }
  }
}
#openApps{
  .modal-body{
    padding: 0;
    text-align: center;
    h4{
      font-size: 16px;
      font-family: @ws-header-font;
      color:@ws-darkBlack;
    }
    p{
      font-size: 12px;
      font-family: @ws-banner-font;
      color:fade(@ws-darkBlack,80%);

    }
  }
  .get-app{
    background: linear-gradient(270deg, #2EBAC6 0%, #4DDCE8 100%);
    color:@ws-white;
  }
  .modal-footer{
    border: none;
    a{
      color:fade(@ws-darkBlack,80%);
      font-size: 12px;
    }
  }
  .logoWs{
    background:@ws-white;
    width: 55px;
    height: 55px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    position: relative;
    top: -22px;
    border-radius: 4px;
    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.25);
  }
}
@media @iPhone {
  .next-wrapper {
    position: fixed;
    bottom: 0;
    background: #FBD786;
    height: 60px;
    display: flex;
    align-items: center;

    .next {
      margin-top: 0;
      background: #fff;
      color: @kids-theme;
    }
  }
}
.next-wrapper{
  position: static;
}
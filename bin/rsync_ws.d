#!/bin/sh

# - Copyright (c) 2018 WonderSlate, Inc. All Rights Reserved. -
#
#  --- Setting local Environment --
UPLOAD_HOME=/u01/ws/exports; export UPLOAD_HOME;
HOME=/u01/ws; export HOME;

CLASSPATH=$HOME/lib/javax.mail.jar:$HOME/lib/activation.jar:$HOME/bin; export CLASSPATH;

LOG_DATE=`TZ='Asia/Kolkata' date '+%d %h %Y %H:%M'`; export LOG_DATE;
TIMESTAMP=$UPLOAD_HOME/logs/ws_rsync.log;

AWS_CONFIG_FILE="/root/.aws/config"; export AWS_CONFIG_FILE;
AWS_CREDENTIALS="/root/.aws/credentials"; export AWS_CREDENTIALS;

PATH=$PATH:$AWS_CONFIG_FILE:$AWS_CREDENTIALS; export PATH;

cleartime() {
 echo "" > $TIMESTAMP;
}

logtime() {
	echo "$1" >> $TIMESTAMP;
}

cleartime;

# -- rsyncing efs --
logtime "Syncing EFS /efs to S3 /efsbackup1 from staging server"
logtime "";
logtime "Starting sync at $LOG_DATE";
logtime "";
logtime "";

#rsync -avzh --stats /efs /efs_backup/ >> $TIMESTAMP;
#added delete at destination if not at source option 08-10-2018
#rsync -avzh --delete --stats /efs /efs_backup/ >> $TIMESTAMP;
aws s3 sync /efs s3://efsbackup1/ --quiet;
LOG_DATE=`TZ='Asia/Kolkata' date '+%d %h %Y %H:%M'`; export LOG_DATE;

logtime "";
logtime "";
logtime "Done with sync at $LOG_DATE";
logtime "";

logtime "";
#logtime "Syncing is scheduled to run every day at 4am, 8am, 10am, 12pm, 2pm, 4pm, 6pm, 8pm, 12am Indian Time.";
logtime "Syncing is scheduled to run every day at 7am, 1pm, 7pm, 1am Indian Time.";
logtime "";
logtime "Emailing out the notification to following recipients: ";
logtime "<EMAIL>";
#logtime "<EMAIL>";
java -classpath $CLASSPATH email "<EMAIL>" "WonderSlate EFS sync to S3 bucket notification" "`cat $TIMESTAMP`";
#java -classpath $CLASSPATH email "<EMAIL>" "WonderSlate EFS sync notification" "`cat $TIMESTAMP`";
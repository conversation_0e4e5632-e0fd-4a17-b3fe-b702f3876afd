#!/bin/bash

explorefolder() {
for d in $1/*; do
  if [ -d "$d" ]; then
    #echo exploring "$d" ....
    explorefolder $d
  else
    #strlow=`echo $d | tr '[:upper:]' '[:lower:]'`; export strlow;
    #echo $strlow
    if [[ `echo $d | tr '[:upper:]' '[:lower:]'` == *png  && `echo $d | tr '[:upper:]' '[:lower:]'` == */chapters/* ]]
    then
        echo converting "$d" "to" jpg
        #default quality is 85. less the value more the compression
        convert "$d" -quality 75 "${d%.*}".jpg
        #convert "$d" "${d%.*}".jpg
        echo moving "$d" "to" "$d".donotuse
        mv "$d" "$d".donotuse
    elif [[ `echo $d | tr '[:upper:]' '[:lower:]'` == *ws && `echo $d | tr '[:upper:]' '[:lower:]'` == */chapters/* ]]
    then
        echo replacing png with jpg in "$d"
        sed -i -e 's/.png/.jpg/gi' "$d"
    elif [[ `echo $d | tr '[:upper:]' '[:lower:]'` == *svg && `echo $d | tr '[:upper:]' '[:lower:]'` == */chapters/* ]]
    then
        echo replacing png with jpg in "$d"
        sed -i -e 's/.png/.jpg/gi' "$d"
    fi
  fi
done
}

if [ -z "$1" ]
  then
    echo "Syntax ./optimiseRes.sh <path>"
    exit
fi

echo `date`
explorefolder $1
echo `date`
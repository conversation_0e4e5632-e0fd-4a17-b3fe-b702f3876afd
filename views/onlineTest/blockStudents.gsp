<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
.form-group a {
    color: white;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <div class="container">
                        <h2 class="text-center">Block/Unblock Students for : ${testName}</h2>
                        <hr/>
                        <%if(!"ongoing".equals(params.mode)){%>
                        <form action="${createLink(controller:'onlineTest', action:'doBlockStudents')}" method="post">
                            <input type="hidden" name="testId" value="${params.testId}"/>
                            <div class="form-group">
                                <label>Reason (Optional):</label>
                                <input type="text" name="reason" class="form-control"/>
                            </div>

                            <table class="table table-striped">
                                <thead>
                                <tr>
                                    <th>Select</th>
                                    <th>Name</th>
                                    <th>Status</th>
                                </tr>
                                </thead>
                                <tbody>
                                <g:each in="${batchUsers}" var="u">
                                    <tr>
                                        <td>
                                            <g:if test="${!blockedMap[u.username]&&!"ongoing".equals(params.mode)}">
                                                <!-- If not blocked, show checkbox to block them -->
                                                <input type="checkbox" name="usernames" value="${u.username}"/>
                                            </g:if>
                                        </td>
                                        <td>${u.name}</td>
                                        <td>
                                            <g:if test="${blockedMap[u.username]}">Blocked</g:if><g:else>Active</g:else>
                                        </td>
                                    </tr>
                                </g:each>
                                </tbody>
                            </table>

                            <button type="submit" class="btn btn-warning">Block Selected</button>

                        </form>
                        <hr/>
                        <%}%>
                        <%if(blockedMap.size()>0){%>
                        <!-- Unblock Form -->
                        <form action="${createLink(controller:'onlineTest', action:'doUnblockStudents')}" method="post">
                            <input type="hidden" name="testId" value="${params.testId}"/>

                            <h3>Currently Blocked Students</h3>
                            <table class="table table-striped">
                                <thead>
                                <tr>
                                    <th>Select</th>
                                    <th>Name</th>
                                </tr>
                                </thead>
                                <tbody>
                                <g:each in="${batchUsers}" var="u">
                                    <g:if test="${blockedMap[u.username]}">
                                        <tr>
                                            <td>
                                                <input type="checkbox" name="blockedUsernames" value="${u.username}"/>
                                            </td>
                                            <td>${u.name}</td>
                                        </tr>
                                    </g:if>
                                </g:each>
                                </tbody>
                            </table>
                            <button type="submit" class="btn btn-success">Unblock Selected</button>
                        </form>
                        <%}else{%>
                        <h4>No students currently blocked from this test</h4>
                        <%}%>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>


</body>
</html>

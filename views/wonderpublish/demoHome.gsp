<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Wonderpublish</title>
    <meta name="description" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1 maximum-scale=1, minimum-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <link rel="icon" sizes="192x192" href="${assetPath(src: 'app-icon-4x.png')}">
    <link rel="manifest" href="manifest.json">
    <link rel="icon" href="${assetPath(src: 'favicon.ico')}" type="image/x-icon" />
    <link href='https://fonts.googleapis.com/css?family=Roboto:300,700,900' rel='stylesheet' type='text/css'>
    <link href='https://fonts.googleapis.com/css?family=Exo' rel='stylesheet' type='text/css'>
    <link href='https://fonts.googleapis.com/css?family=Arima Madurai' rel='stylesheet' type='text/css'>
    <asset:stylesheet href="bootstrap.css"/>
    <asset:stylesheet href="style.css"/>
    <asset:stylesheet href="flip.css"/>
    <asset:stylesheet href="font-awesome.min.css"/>
</head>
<%
    String url = (request.getRequestURL()).toString();
    String serverUrl = url.substring(0,url.indexOf('/',9));
    String requestURL = request.getRequestURL().toString();
    String servletPath = request.getServletPath();
    String appURL = requestURL.substring(0, requestURL.indexOf(servletPath));

    session.setAttribute("servername", appURL);

%>
<style>
.indieflower{
    font-family: 'Arima Madurai';
    font-size: 20px;
    letter-spacing: 0.6px;
}

</style>

<body class="wplandingimage">

<nav class="navbar navbar-default wplandingimage" ><br>
    <div class="container-fluid navbarfirstline ">
        <div class="navbar-header">
            <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#myNavbar">
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>

                <span class="brand">&nbsp;&nbsp;&nbsp;&nbsp;<a style="padding-top:5px;" class="navbar-brand" href="/wonderpublish/demoHome"><img alt="brand" src="${assetPath(src: 'logo-ws.png')}" id="addingcontent2">PUBLISHER NAME <span class="smallerText greytext"></span></a></span>



        </div>
        <div class="collapse navbar-collapse" id="myNavbar">
            <div class="row row-right">


            </div>
        </div>
    </div>


    <!--<a href='#' id='example2' rel='popover' data-placement='left' data-content='Discover your subject by choosing these options' data-original-title='Find your subject'></a>-->

<br>
</nav>
<div class="container-fluid wplandingimage ">

        <div class="row text-center">
            <div class="col-md-10 col-md-offset-1  homemessage darkgrey" >Welcome to the world of <span class="light10text">digital smart books </span> !
            </div>
        </div>


            <div class="row text-center">
                <div class="col-md-12" ><img  src="${assetPath(src: 'ipads.png')}"></div></div>
    <div class="row text-center">
        <div class="col-md-8 col-md-offset-2 bigtext darkgrey" ><span class="light10text">Digital smart book</span> contains
        </div>
    </div><BR>
    <div class="row text-center ">
        <div class="col-md-12 bigtext darkgrey" ><i class="fa fa-hand-o-down fa-2x "></i>
        </div>
    </div><br>



</div>


            <div id="work" class="feature-shadow" style="display: block;">


                <a class="trigger" href="/books/index">
                    <div class="feature two show">
                        <div class="face cover" style="border-radius: 0px;"> </div>
                        <div class="face hover"><p class="info indieflower">Read anytime, anywhere!!</strong>.</p></div>
                    </div>
                </a>
                <a class="trigger" href="/books/index">
                    <div class="feature three show">
                        <div class="face cover" style="border-radius: 0px;"></div>
                        <div class="face hover"><p class="info indieflower">Make learning interesting, easy and effective</strong>.</p></div>
                    </div>
                </a>
                <a class="trigger" href="/books/index">
                    <div class="feature one show">
                        <div class="face cover" style="border-radius: 0px;"></div>
                        <div class="face hover"><p class="info indieflower">Learn on the go with iOS and Android apps</p></div>
                    </div>
                </a>
                <a class="trigger" href="/books/index">
                    <div class="feature four show">
                        <div class="face cover" style="border-radius: 0px;"></div>
                        <div class="face hover"><p class="info indieflower">Assess and improve learning with easy analytics tools</p></div>
                    </div>
                </a>
            </div>

<BR><br>
<a href="/books/index" class="whitetext"> <div class="container-fluid wplandingblueimage">

    <div class="row text-center">
        <div class="col-md-10 col-md-offset-1  homemessage whitetext" >Enter here
        </div>
    </div>


</div></a>

















<% if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1 && (user==null||!"Yes".equals(""+user.wonderSlateEmployee))){ %>
<asset:javascript src="analytics.js"/>
<% } %>
<script>
        $("#card").flip();
    $(function() {
        $('a[href*="#"]:not([href="#"])').click(function() {
            if (location.pathname.replace(/^\//,'') == this.pathname.replace(/^\//,'') && location.hostname == this.hostname) {
                var target = $(this.hash);
                target = target.length ? target : $('[name=' + this.hash.slice(1) +']');
                if (target.length) {
                    $('html, body').animate({
                        scrollTop: target.offset().top
                    }, 1000);
                    return false;
                }
            }
        });
    });


</script>

</body>
</html>
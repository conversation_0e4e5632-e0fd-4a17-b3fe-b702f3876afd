<g:render template="/wonderpublish/loginChecker"></g:render>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>
<script>
    var defaultSiteName="${session['entryController']}";
    var mybookName="${params.bookName}";
    var mybookId="${params.bookId}";

    var siteName = "";

    <%if("true".equals(session["commonWhiteLabel"])){%>
    siteName = "${session['siteName']}";
    <%}else{%>
    siteName = "${session['entryController']}";
    <%}%>

</script>
<asset:stylesheet href="imageoverlay.css"/>
<asset:stylesheet href="bootstrap-select.css"/>
<asset:stylesheet href="jquery.simple-dtpicker.css"/>

<link href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/jquery-datetimepicker/2.5.20/jquery.datetimepicker.min.css" rel="stylesheet" type="text/css" />

<asset:stylesheet href="wonderslate/adminCommonStyle.css"/>

<div class="new_book_create">
    <div class="container lightblue_bg border rounded my-5">

        <!-- Book Tabs -->
        <ul class="nav nav-pills mb-3 bg-white rounded px-0 px-md-4" id="pills-tab" role="tablist">
            <li class="nav-item px-3">
                <a class="nav-link mr-4 py-3 px-2 rounded-0 active" id="bookDetailsTab" data-toggle="pill" href="#bookDetails" role="tab" aria-controls="bookDetails" aria-selected="true">eBook Details</a>
            </li>
            <li class="nav-item px-3">
                <a class="nav-link mr-4 py-3 px-2 rounded-0" id="bookChaptersTab" data-toggle="pill" href="#bookChapters" role="tab" aria-controls="bookChapters" aria-selected="false">Chapters</a>
            </li>
        <% if(gptManager){%>
        <li class="nav-item px-3">
            <a class="nav-link mr-4 py-3 px-2 rounded-0" id="bookGPTTab" data-toggle="pill" href="#bookGPT" role="tab" aria-controls="bookGPT" aria-selected="false">GPT Manager</a>
        </li>
        <%}%>
            <div id="bookSaving" class="d-none">
                <p class="saving-text">Auto Saving<span>.</span><span>.</span><span>.</span></p>
            </div>
        </ul>

        <!-- Book Tab Content -->
        <div class="tab-content p-2 p-md-4" id="pills-tabContent">

            <!-- Book Details Tab Content -->
            <div class="tab-pane px-3 fade show active" id="bookDetails" role="tabpanel" aria-labelledby="bookDetailsTab">
                <g:render template="/wonderpublish/bookdetailsTab"></g:render>
            </div>

            %{--Book Chapters Tab Content--}%
            <div class="tab-pane fade" id="bookChapters" role="tabpanel" aria-labelledby="bookChaptersTab">
                <g:render template="/wonderpublish/bookChaptersTab"></g:render>
            </div>
            <%if(gptManager){%>
            <div class="tab-pane fade" id="bookGPT" role="tabpanel" aria-labelledby="bookGPTTab">
                <g:render template="/wonderpublish/bookGPTTab"></g:render>
            </div>
            <%}%>

        </div>
    </div>

    <g:render template="/wonderpublish/uploadResourcesModal"></g:render>

</div>

<div class="container-fluid wplandingblueimage topnavblue"></div>

<div class="modal fade new_book_create modal-modifier" id="videoModal">
    <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-modifier">
        <div class="modal-content modal-content-modifier">
            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>
            <div class="modal-body modal-body-modifier bg-white rounded">
                <iframe width="100%" height="500px" src="" frameborder="0" allowfullscreen></iframe>
            </div>
        </div>
    </div>
</div>
<div class="modal fade new_book_create modal-modifier" id="removePhone">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <!-- Modal body -->
            <div class="modal-body modal-body-modifier">
                <div class="form-group col-12">
                    <h5 class="mt-3 mb-4 pb-2 border-bottom">Reference Video Link</h5>
                </div>
                <div class="form-group col-md-12 px-0 d-flex align-items-center">
                    <label class="col-md-3 mb-0 text-right">Name: </label>
                    <div class="col-md-9">
                        <input class="form-control" type='text' name='number' id='restitle'>
                        <div class="invalid-feedback" id="restitleError">Please enter a name.</div>
                     </div>
                </div>
                <div class="form-group col-md-12 px-0 d-flex align-items-center">
                    <label class="col-md-3 mb-0 text-right">link: </label>
                    <div class="col-md-9">
                        <input class="form-control" type='text' name='number' id='resname'>
                        <div class="invalid-feedback" id="resnameError">Please enter the video link.</div>
                    </div>
                    <input type="hidden" name='number' id='resid'>
                </div>
                <div class="form-group col-md-12 px-0 d-flex align-items-center">
                    <label class="col-md-3 mb-0 text-right">Start Date: </label>
                    <div class="col-md-9">
                        <div class="input-group">
                            <input class="form-control" type='text' name='modalTestStartDate' id='modalTestStartDate' autocomplete="off">
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary readonly-datepicker" type="button" id="modalTestStartDatePicker"><asset:image src="ws/icon-calendar.svg"/></button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group col-md-12 px-0 d-flex align-items-center">
                    <label class="col-md-3 mb-0 text-right">End Date: </label>
                    <div class="col-md-9">
                        <div class="input-group">
                            <input class="form-control" type='text' name='modalTestEndDate' id='modalTestEndDate' autocomplete="off">
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary readonly-datepicker" type="button" id="modalTestEndDatePicker"><asset:image src="ws/icon-calendar.svg"/></button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group col-md-12 d-flex mb-2">
                    <p class="mb-0">Download video links</p>
                </div>
                <div class="form-group col-md-12 px-0 d-flex align-items-center">
                    <label class="col-md-3 mb-0 text-right">360p: </label>
                    <div class="col-md-9">
                        <input class="form-control" type='text' name='downloadlink1edit' id='downloadlink1edit'>
                    </div>
                </div>
                <div class="form-group col-md-12 px-0 d-flex align-items-center">
                    <label class="col-md-3 mb-0 text-right">540p: </label>
                    <div class="col-md-9">
                        <input class="form-control" type='text' name='downloadlink2edit' id='downloadlink2edit'>
                    </div>
                </div>
                <div class="form-group col-md-12 px-0 d-flex align-items-center">
                    <label class="col-md-3 mb-0 text-right">720p: </label>
                    <div class="col-md-9">
                        <input class="form-control" type='text' name='downloadlink3edit' id='downloadlink3edit'>
                    </div>
                </div>
                <div class="form-group col-md-12 d-flex mb-2">
                    <p class="mb-0">Video Player</p>
                </div>
                <div class="form-group col-md-12 d-flex" style="display: none" id="videoPlayer">
                    <label class="clickable-label">
                        <input type="radio" name="videoPlayer" value="youtube" id="modalYoutube" onchange="videoPlayerChangedModel(this)"> Youtube
                    </label>
                    <label class="clickable-label">
                        <input type="radio" name="videoPlayer" value="custom" id="modalCustom" onchange="videoPlayerChangedModel(this)"> Custom player
                    </label>
                </div>
                <div class="form-group col-md-12" style="display: none;" id="modalCommentSection">
                    <label class="clickable-label d-flex mb-2">
                        <input type="checkbox" name="allowComments" id="modalAllowComments"> Allow comments for live video
                    </label>
                    <label class="clickable-label d-flex">
                        <input type="checkbox" name="displayComments" id="modalDisplayComments"> Display comments after live video
                    </label>
                </div>

                <div class="d-flex justify-content-center py-3 col-12">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-shadow col-5 col-md-4 mr-2" data-dismiss="modal" aria-label="Close">Cancel</button>
                    <button type="button" class="btn btn-lg btn-success btn-shadow border-0 col-5 col-md-4 ml-2" onclick="submitSearch()">Submit</button>
                </div>
            </div>

        </div>
    </div>
</div>

<div class="modal fade new_book_create modal-modifier" id="removePhone1">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <!-- Modal body -->
            <div class="modal-body modal-body-modifier">
                <div class="form-group col-md-12">
                    <h5 class="mt-3 mb-4 pb-2 border-bottom">Reference Web Link</h5>
                </div>
                <div class="form-group col-md-12 px-0 d-flex align-items-center">
                    <label class="col-md-3 mb-0 text-right">Name: </label>
                    <div class="col-md-9">
                        <input class="form-control" type='text' name='number' id='weblinktitleedit'>
                        <div class="invalid-feedback" id="weblinktitleeditError">Please enter a name.</div>
                    </div>
                </div>
                <div class="form-group col-md-12 px-0 d-flex align-items-center">
                    <label class="col-md-3 mb-0 text-right">Web link: </label>
                    <div class="col-md-9">
                        <input class="form-control" type='text' name='number' id='weblinkurledit'>
                        <div class="invalid-feedback" id="weblinkurleditError">Please enter the web link.</div>
                    </div>
                    <input type="hidden" name='number' id='weblinkresid'>
                </div>

                <div class="d-flex justify-content-center py-3 col-12">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-shadow col-5 col-md-4 mr-2" data-dismiss="modal" aria-label="Close">Cancel</button>
                    <button type="button" class="btn btn-lg btn-success btn-shadow border-0 col-5 col-md-4 ml-2" onclick="submitSearch1()">Submit</button>
                </div>
            </div>

        </div>
    </div>
</div>

<div class="modal fade" data-backdrop="static" data-keyboard="false" id="addResource" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">
                <button type="button" class="close" data-dismiss="modal"  aria-label="Close"><span aria-hidden="true">&times;</span></button>

                <b><p class='resourcename'>Add Chapter/Topic name:</p></b>
                <div id="samplelink" class="samplelink"><p><span >1. Download the sample file <a href="#" id="sampleButton" onclick="">here <i class="fa fa-download fa-x"></i> </a> </span> </p>
                    <p>2. Enter your quiz questions in the sample file (remove the questions which are already present).</p>
                    <p>3. Upload your file below. This will create the quiz questions on Wonderslate  </p>

                </div>
                <div id="videolinkinfo" class="videolinkinfo" style="display: none"><p><span>Copy the YouTube video link and paste it in the box given below. </a> </span> </p></div>
                <div id="filelinkinfo" class="filelinkinfo" style="display: none"><p><span>Select the file from your system and upload. </a> </span> </p></div>


                <g:uploadForm name="resourceForm" url="[action:'addResource',controller:'creation']"  method="post">
                    <div class="form-group col-sm-6 col-xs-12 videolink"><g:textField id="link" class="form-control" name="link"  placeholder="Video link"  /></div>
                    <div class="form-group col-sm-3 col-xs-12"><g:textField id="resourceName" class="form-control" name="resourceName"  placeholder="Name" /></div>
                    <div class="form-group col-sm-3 col-xs-12 filelink"><input id="file" type="file" class="form-control" name="file"/></div>
                    <div class="form-group col-sm-3 col-xs-12 samplelink"></div>
                    <div class="form-group">
                        <div class="col-sm-9 text-center">
                            <button type="button" onclick="javascript:submitResource()" class="btn btn-primary">Add</button>
                        </div>
                    </div>
                    <div class='col-md-12'>
                        <p  class='text-danger topicexists'></p>
                    </div>
                    <div class="alert-thin alert alert-warning col-sm-12 text-left" style="display: none;" id="genericAlert">
                        ** Please complete required fields marked in red.
                    </div>
                    <div class="alert-thin alert alert-warning col-sm-12 text-left" style="display: none;" id="youTubeLinkAlert">
                        ** Please enter a valid you tube link.
                    </div>
                    <div class="alert-thin alert alert-info col-sm-12 text-left" style="display: none;" id="youTubeAlert">
                        ** Please note only <i class="fa fa-youtube fa-2x"></i> videos will be accepted.
                    </div>
                    <input type="hidden" name="resourceType">
                    <input type="hidden" name="useType">
                    <input type="hidden" name="chapterId" value="">
                </g:uploadForm>
                <br><br><br><br><br><br><br>
            </div>
        </div>
    </div>
</div>


<div class="container-fluid whitebackground"></div>
<form name="updateDescriptionForm" id="updateDescriptionForm" action="/wonderpublish/updateDescription" method="post">
    <input type="hidden" name="bookId" id="bookId">
    <input type="hidden" name="description" >
</form>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/footer"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<%}%>
<g:render template="/funlearn/topicscripts"></g:render>
<asset:javascript src="bootstrap-select.js"/>
<asset:javascript src="generic.js"/>
<asset:javascript src="topic.js"/>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="clock.js"/>
<asset:javascript src="moment.min.js"/>
<asset:javascript src="jquery.simple-dtpicker.js"/>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-datetimepicker/2.5.20/jquery.datetimepicker.full.min.js"></script>
<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>
<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    $(function() {
        $( "#chaptersList" ).sortable();
        $( "#chaptersList" ).disableSelection();
    });
    function updateSortOrder(){
        $('.loading-icon').removeClass('hidden');
        var itemOrder = $('#chaptersList').sortable("toArray");
        var chapterSortId="";
        for (var i = 0; i < itemOrder.length; i++) {
            chapterSortId += itemOrder[i]+",";
        }
        var chaptersSortId=chapterSortId.substring(0,(chapterSortId.length-1));
        <g:remoteFunction controller="wonderpublish" action="updateChaptersSortId" params="'chaptersSortId='+chaptersSortId+'&bookId='+bookId" onSuccess="uploadChaptersSort(data)"></g:remoteFunction>
    }

    function uploadChaptersSort(data){
        if(data.status=="success") window.location.reload();
    }
</script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full" async></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/ckeditor/4.18.0/ckeditor.js" ></script>

<script type="text/x-mathjax-config">
  MathJax.Hub.Config({tex2jax: {inlineMath: [['$','$'], ['\\(','\\)']]}});
</script>
<script>
    var dateToday = moment().format('YYYY-MM-DD');
    var onChangeDate = "";
    var dateChangeLogic = function(ct){
        onChangeDate = moment(ct).format('YYYY-MM-DD');
        if (onChangeDate == dateToday) {
            this.setOptions({
                minTime: 0
            });
        } else {
            this.setOptions({
                minTime: false
            });
        }
    }
    $('*[name=resourceStartDate],*[name=resourceEndDate],*[name=modalTestStartDate],*[name=modalTestEndDate]').datetimepicker({
        lang:'en',
        format:'Y-m-d H:i',
        step:5,
        minDate:0,
        scrollMonth: false,
        onSelectDate:dateChangeLogic,
        onShow:dateChangeLogic
    });

    $(function(){
        $('*[name=testStartDate]').appendDtpicker({
           "futureOnly": true,
            "autodateOnStart": false,
            "minuteInterval": 15,
            "onHide": function(handler){
                 bookDtlUpdate(document.getElementById("testStartDate"))
            }
        });

        $('*[name=testEndDate]').appendDtpicker({
            "futureOnly": true,
            "autodateOnStart": false,
            "minuteInterval": 5,
            "onHide": function(handler){
                bookDtlUpdate(document.getElementById("testEndDate"))
            }

        });
        $('*[name=testResultDate]').appendDtpicker({
            "futureOnly": true,
            "autodateOnStart": false,
            "minuteInterval": 5,
            "onHide": function(handler){
                bookDtlUpdate(document.getElementById("testResultDate"))
            }

        });

        //here
<%  if(booksMst!=null && booksMst.testStartDate!=null && booksMst.testStartDate!="") { %>
        $("#testStartDate").val(moment.utc('<%=booksMst.testStartDate%>').local().format("YYYY-MM-DD HH:mm"));
<%  } %>
<%  if(booksMst!=null && booksMst.testEndDate!=null && booksMst.testEndDate!="") { %>
        $("#testEndDate").val(moment.utc('<%=booksMst.testEndDate%>').local().format("YYYY-MM-DD HH:mm"));
<%  } %>
    });

    CKEDITOR.replace( 'description', {
        height: 50,
        customConfig: '/assets/ckeditor/customConfig.js',
        extraPlugins: 'mathjax,uploadimage,image2,font,colorbutton,colordialog',
        mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML',
    });

    CKEDITOR.instances.description.on('blur', function() {
        bookDtlUpdate(document.getElementById("description"));
    });

<%if(booksMst!=null&&booksMst.description!=null&&!"".equals(booksMst.description)){%>
    document.getElementById("description").innerHTML="${booksMst.description.replace("\n", "").replace("\r", "")}";
<%}%>
    var loggedInUser = false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedInUser = true;
    </script>
</sec:ifLoggedIn>
<script>
    <%if(booksMst!=null){%>
    var isbn= "${booksMst.isbn}";
    <%}%>
    var bookId="${bookId}";
    var titleAlertShown=false;
    var isbnShown=false;
    var priceErrorShown=false;
    var pubDtErrorShown=false;
    var chapterId=-1;
    var previousChapterId=-1;
    var noOfChapters = ${chaptersMst!=null?chaptersMst.size():0};
    var syllabusList;
    var pageType='';
    var loggedIn='';
    var noOfTags=0;
    var published=false;
    var receivedResId=null;
    <%if(booksTagDtl!=null){%>
    noOfTags = ${booksTagDtl.size()};
    <%}%>
    <%if(booksMst!=null&&("published".equals(booksMst.status)||"institutePublished".equals(booksMst.status))){%>
     $("#notpublished").hide();
     $("#published").show();
     published=true;
    <%}else{%>
    $("#notpublished").show();
    $("#published").hide();
    <%}%>
    $( "input[type='text']" ).keyup(function(e){
        if(e.keyCode == 13) {
           $(this).blur();
        }
    });

    $('#authors').on('changed.bs.select', function (e, clickedIndex, isSelected, previousValue) {
        newAuthorUpdate(clickedIndex,isSelected);
    });

  function videoPlayerChanged(field){
      if("custom"==field.value){
          $("#allowCommentsSection").show();
      }else{
          $("#allowCommentsSection").hide();
      }
  }
    function newAuthorUpdate(clickedIndex,isSelected){
      var authorMode = isSelected?"add":"remove";
       var authorId = document.getElementById("authors").options[clickedIndex].value;
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="wonderpublish" action="updateAuthorNew" onSuccess='updateBookId(data);'
            params="'bookId='+bookId+'&authorId='+authorId+'&authorMode='+authorMode"></g:remoteFunction>
    }

    function publish(){
        document.getElementById("publisherrors").innerHTML="";
        $("#publisherrors").hide();
        var errorStr="";

        if(document.getElementById('title').value=="") {
            errorStr+="- eBook Title<br>";
        }

        if(document.getElementById('publisherId').selectedIndex==0){
            errorStr+="- Publisher<br>";
            $('#publisher-error').show();
            $('#publisherId').removeClass('input-success').addClass('input-error');
        }

        if(document.getElementById('authors').selectedIndex==-1){
            errorStr+="- Author/s<br>";
            $('#authors-error').show();
            $('.bootstrap-select button').removeClass('input-success').addClass('input-error');
        }
        <%if(!institutePublisher){
           if(smartEbook){%>


        <%}else{%>
        if(document.getElementById('buylink1').value==""&&document.getElementById('buylink2').value==""){
            errorStr+="- Amazon or Flipkart buy link<br> ";
        }
        <%}%>

        var isbn="";
        <%   if("evidya".equals(session["entryController"])|| "etexts".equals(session["entryController"])||"ebouquet".equals(session["entryController"])) { %>
            if(document.getElementById('isbn').value==""){
                errorStr+="- Enter ISBN<br> ";
                document.getElementById('isbnerror').innerText = 'Please enter ISBN number.';
                $('#isbnerror').show();
                $('#isbn').removeClass('input-success').addClass('input-error');
            }
        <%}%>

        if(document.getElementById("addedtags").rows.length==0){
            errorStr+="- Category tags<br>";
            $('#categoryTagsError').show();
            $('#createLevel').removeClass('input-success').addClass('input-error');
        }
       <%}%>
        if(errorStr.length>0){
            errorStr = "\n<strong>Please enter the following information to proceed with publishing, </strong><br>"+errorStr;
            document.getElementById("publisherrors").innerHTML=errorStr;
            $("#publisherrors").show();
        }else{
            <%if(smartEbook){%>
            <g:remoteFunction controller="wonderpublish" action="getChaptersListWithResourceCount" onSuccess='continueValidating(data);'
                params="'bookId='+bookId"></g:remoteFunction>
            <%}else{%>
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="wonderpublish" action="publishBook" onSuccess='publishSucess(data)' params="'bookId='+bookId"></g:remoteFunction>
            <%}%>
        }
    }

    function unPublishBook(){
        if(confirm("Unpublishing this eBook will remove it from store. It will not remove from user's library. Are you sure to continue?")) {
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="wonderpublish" action="unpublishBook" onSuccess='unpublishSucess(data)' params="'bookId='+bookId"></g:remoteFunction>
        }
    }
    function unpublishSucess(data){
        $('.loading-icon').addClass('hidden');
        $("#notpublished").show();
        $("#published").hide();
        <%if(institutePublisher&&!"-1".equals(instituteId)){%>
        <g:remoteFunction controller="institute" action="removeBookFromInstitute" params="'instituteId=${instituteId}&bookId='+bookId"/>
        <%}%>
    }
    function publishSucess(data){
       $('.loading-icon').addClass('hidden');
       $("#notpublished").hide();
       $("#published").show();
       $('#publishSuccess').modal('show');
       <%if(institutePublisher&&!"-1".equals(instituteId)){%>
       <g:remoteFunction controller="institute" action="addBooks" params="'instituteId=${instituteId}&bookIds='+bookId+'&bookIdMethod=true' "/>
       <%}%>
    }
    function continueValidating(data){
        var chapters = data.results;
        var errorStr="";
        var priceList = data.priceList;

        if(priceList.length==0) errorStr += "Please add price<br>";

  /**      if("Nothing present"==data.status){
            errorStr+="- Add atleast one chapter<br>";
        }else{
            <%if(!institutePublisher){%>
            for(i=0;i<chapters.length;i++){
                if(chapters[i].resourceCount=="0"){
                    errorStr+="- Chapter "+chapters[i].chapterName+" does not have any content.<br>";
                }
            }
            if(document.getElementById('bookTypeId')[document.getElementById('bookTypeId').selectedIndex].value=='test'){
                if(chapters.length>"1"){
                    errorStr+="- You should have only one chapter for the test series.<br>";
                }
                if(document.getElementById('testStartDate').value==""){
                    errorStr += "- Test start date<br>";
                }
                if(document.getElementById('testEndDate').value==""){
                    errorStr += "- Test end date<br>";
                }
                if(testSeries.length==0){
                    errorStr += "- Please select the exam for the quiz<br>";
                }
            }else {
                if (document.getElementById('previewChapter').selectedIndex == 0) {
                    errorStr += "- Preview Chapter<br>";
                    $('#previewChapter').removeClass('input-success').addClass('input-error');
                }
            }

            <%}%>
        }*/

        if(errorStr.length>0){
            errorStr = "\n<strong>Please enter the following information to proceed with publishing, </strong><br>"+errorStr;
            document.getElementById("publisherrors").innerHTML=errorStr;
            $("#publisherrors").show();
        }else{
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="wonderpublish" action="publishBook" onSuccess='publishSucess(data)' params="'bookId='+bookId"></g:remoteFunction>
        }

    }
    function getCreateSyllabus(level){
        if(document.getElementById('title').value=="") {
            $('.booktitlealert').show();
            $('#title').removeClass('input-success').addClass('input-error').focus();
            titleAlertShown=true;

        }else {
            if(document.getElementById('createLevel').selectedIndex==0) {
                $('#createSyllabus').hide();
                $('#createGrade').hide();
                $('#createSubject').hide();
            } else {
                $('.loading-icon').removeClass('hidden');
                level = encodeURIComponent(level);
                <g:remoteFunction controller="wonderpublish" action="getSyllabus"  onSuccess='initializeCreateSyllabus(data);'
        params="'level='+level"/>
            }
        }
    }

    function initializeCreateSyllabus(data){
        $('.loading-icon').addClass('hidden');
         syllabusList = data.results;
        var select = document.getElementById("createSyllabus");
        select.options.length = 1;
        for(var i=0;i< syllabusList.length; i++) {

            el = document.createElement("option");
            el.textContent = syllabusList[i].syllabus;
            el.value = syllabusList[i].syllabus;
            select.appendChild(el);
        }

        select.focus();
        $('#createSyllabus').show().removeClass('input-success').addClass('input-error');
    }

    function getCreateGrade(syllabus){
        if(document.getElementById("createSyllabus").selectedIndex == 0) {
            $('#createGrade').hide();
            $('#createSubject').hide();
        } else {
            $('.loading-icon').removeClass('hidden');
            var length=13;
            var level = document.getElementById("createLevel");
            var select = document.getElementById("createGrade");
            if("School"==level[level.selectedIndex].value&&syllabus!='NIOS'){
                select.options.length = 1;
                <% if(session['siteId'].equals(5)){%>
                length=9;
                el = document.createElement("option");
                el.textContent = 'Pre Primary';
                el.value = 'Pre Primary';
                select.appendChild(el);
                <%}%>
                for(var i=1;i< length; i++) {
                    el = document.createElement("option");
                    el.textContent = i;
                    el.value = i;
                    select.appendChild(el);
                }

                select.focus();
                $('#createGrade').show().removeClass('input-success').addClass('input-error');
                $('.loading-icon').addClass('hidden');
            }
            else{
                var seperate=true;
                for(var i=0;i< syllabusList.length; i++) {
                    if(syllabus==syllabusList[i].syllabus){
                        if(syllabusList[i].gradeType=="Semester"){
                            seperate=false;
                            select.options.length = 1;
                            var startSemester=1;
                            var endSemester=8;
                            if(syllabusList[i].startSemester!=null) startSemester = syllabusList[i].startSemester;
                            if(syllabusList[i].endSemester!=null) endSemester = syllabusList[i].endSemester;
                            el = document.createElement("option");
                            el.textContent = 'All Semesters';
                            el.value = 'All Semesters';
                            select.appendChild(el);
                            for(var j=startSemester;j< (1+endSemester); j++) {

                                el = document.createElement("option");
                                el.textContent = 'Semester '+j;
                                el.value = 'Semester '+j;
                                select.appendChild(el);
                            }

                            select.focus();
                            $('#createGrade').show().removeClass('input-success').addClass('input-error');
                            $('.loading-icon').addClass('hidden');
                        }
                        break;
                    }
                }
                if(seperate){
                    if(syllabus==='NIOS'){
                        $('#createGrade').html(" <option>Select</option>" +
                            "<option value=\"Open Basic Education\">Open Basic Education</option>\n" +
                            "    <option value=\"Secondary courses\">Secondary courses</option>\n" +
                            "    <option value=\"Open Basic Education\">Senior Secondary courses</option>\n" +
                            "    <option value=\"Vocational Courses\">Vocational Courses</option>\n" +
                            "    <option value=\"Diploma in Elementary Education (D.El.Ed)\">Diploma in Elementary Education (D.El.Ed)</option>"+
                            "    <option value=\"5\">5</option>"+
                            "    <option value=\6\">6</option>"+
                            "    <option value=\"7\">7</option>"+
                            "    <option value=\"8\">8</option>"+
                            "    <option value=\"9\">9</option>"+
                            "    <option value=\"10\">10</option>"+
                            "    <option value=\"11\">11</option>"+
                            "    <option value=\"12\">12</option>");
                        $('#createGrade').show().removeClass('input-success').addClass('input-error');
                        select.focus();
                        $('.loading-icon').addClass('hidden');
                    }
                    else {
                        syllabus = encodeURIComponent(syllabus);
                        <g:remoteFunction controller="wonderpublish" action="getGrades"  onSuccess='initializeCreateGrades(data);'
                params="'syllabus='+syllabus"/>
                    }
                }
            }
        }
    }

    function initializeCreateGrades(data){
        $('.loading-icon').addClass('hidden');
        var  grades = data.results;
        var select = document.getElementById("createGrade");
        select.options.length = 1;
        for(var i=0;i< grades.length; i++) {

            el = document.createElement("option");

            el.textContent = grades[i].grade+(grades[i].state!=null?" ( "+grades[i].state+" )":"");
            el.value = grades[i].grade;
            select.appendChild(el);
        }

        select.focus();
        $('#createGrade').show().removeClass('input-success').addClass('input-error');
    }

    function getCreateSubject(value){
        if(document.getElementById("createGrade").selectedIndex == 0) {
            $('#createSubject').hide();
        } else {
            var level = document.getElementById("createLevel");
            var syllabusList = document.getElementById("createSyllabus");
            var syllabus=syllabusList[syllabusList.selectedIndex].value;
            if("School"==level[level.selectedIndex].value) syllabus="School";
            $('.loading-icon').removeClass('hidden');
            syllabus = encodeURIComponent(syllabus);
            <g:remoteFunction controller="wonderpublish" action="getSubjects"  onSuccess='initializeCreateSubjects(data);'
        params="'syllabus='+syllabus"/>
        }
    }
    function initializeCreateSubjects(data){
        $('.loading-icon').addClass('hidden');
        var subjects = data.results;
        var select = document.getElementById("createSubject");
        select.options.length = 1;
        for(var i=0;i<  subjects.length; i++) {

            el = document.createElement("option");
            el.textContent =  subjects[i].subject;
            el.value =  subjects[i].subject;
            select.appendChild(el);
        }

        select.focus();
        $('#createSubject').show().removeClass('input-success').addClass('input-error');
    }

    function addTag(subject){
        var level = document.getElementById("createLevel");
        var syllabus = document.getElementById("createSyllabus");
        var grade = document.getElementById("createGrade");
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="wonderpublish" action="addBookTags" onSuccess='updateBookTagTable(data);'
        params="'bookId='+bookId+'&level='+encodeURIComponent(level[level.selectedIndex].value)+'&syllabus='+encodeURIComponent(syllabus[syllabus.selectedIndex].value)+'&grade='+encodeURIComponent(grade[grade.selectedIndex].value)+'&subject='+encodeURIComponent(subject)"/>
        $('#createSyllabus').hide();
        $('#createGrade').hide();
        $('#createSubject').hide();
    }

    function updateBookTagTable(data){
        $('.loading-icon').addClass('hidden');
        $('#createLevel').removeClass('input-error input-success');
        $('#createLevel option:first').attr('selected','selected');
        var tags =data.results;
        var str = "<tr>"+
                        "<td>"+tags.level+"</td>"+
                        "<td>"+tags.syllabus+"</td>"+
                        "<td>"+tags.grade+"</td>"+
                        "<td>"+tags.subject+"</td>";
        %{--<sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">--}%
                        str +="<td width='5%'><a href='javascript:deleteBookTag("+tags.id+")'><i class='material-icons delete'>delete_outline</i> </a></td>";
        %{--</sec:ifAllGranted>--}%

                  str +=  "</tr>";
        document.getElementById("addedtags").innerHTML = document.getElementById("addedtags").innerHTML +str;
        $("#addedtags").show();
        noOfTags +=1;
    }

    function updateChapterPdf(){
        document.resourceForm.bookId.value=bookId;
        document.resourceForm.chapterId.value=chapterId;
        document.resourceForm.submit();
    }

    function updatetoc(){
        document.resource2Form.bookId.value=bookId;
        document.resource2Form.chapterId.value=chapterId;
        document.resource2Form.submit();
    }

    function updateChapter(){
        document.resource1Form.bookId.value=bookId;
        document.resource1Form.chapterId.value=chapterId;
        document.resource1Form.submit();
    }

    function showNotesUpload(){
        $("#notesupload").show(500);
    }

    function showQuizUpload(resourceType){
        $("#quizupload").show(500);
        document.resource6Form.resourceType.value=resourceType;

    }

    function showQuizCopy(resourceType){
        $('#copyQuiz').modal('show');
        document.resource8Form.resourceType.value=resourceType;
    }

    function showReadCopy(resourceType){
        $('#readQuiz').modal('show');
        document.resource9Form.resourceType.value=resourceType;
    }
    var validFileSize = true;
    $('#file3').change(function(event) {
        var _size = this.files[0].size;
        if(_size >= 50000000){
            validFileSize = false;
        }else{
            validFileSize = true;
        }
    });
    function uploadNotes(){
        if(document.resource3Form.resourceName.value==""){
            $("#notesUploadAlert").show();
            $('#resourceName').focus().addClass('input-error');
        }else if(document.getElementById("file3").value == "") {
            $("#UploadSizeAlert").show();
            $('#file3').addClass('input-error');
            document.getElementById('UploadSizeAlert').innerText = 'Please select file to upload.';
        } else if(!validFileSize) {
            $("#UploadSizeAlert").show();
            document.getElementById('UploadSizeAlert').innerText = 'File size exceeds 50mb.';
            $('#file3').addClass('input-error');
        } else {
            document.resource3Form.bookId.value = bookId;
            document.resource3Form.chapterId.value = chapterId;
            document.resource3Form.submit();
            $('.loading-icon').removeClass('hidden');
        }
    }
    function showImagesZipUpload(){
        $("#imagezipupload").show(500);
    }

    function uploadImageZip(){
        $("#zipUploadAlert").hide(500);

        if(document.getElementById("file5").value == "") {
            $("#zipUploadAlert").show(500);
        } else {
            document.resource7Form.bookId.value = bookId;
            document.resource7Form.chapterId.value = chapterId;
            document.resource7Form.submit();
            $('.loading-icon').removeClass('hidden');
            $('#imagesZip').hide();
        }
    }

    function showVideosUpload(){
        $("#paidvideoupload").show(500);
    }

    function uploadPaidVideo(){
        if(document.getElementById("mediaresourceName").value == "") {
            $("#paidVideoNameAlert").show();
            $('#mediaresourceName').focus().addClass('input-error');
        } else if(document.getElementById("file10").value == "") {
            $("#paidVideoAlert").show();
            $('#file10').addClass('input-error');
        } else {
            document.resource10Form.bookId.value = bookId;
            document.resource10Form.chapterId.value = chapterId;
            document.resource10Form.submit();
            $('.loading-icon').removeClass('hidden');
        }
    }

    function uploadQuiz(){
        $("#quizUploadAlert").hide(500);
        if(document.getElementById("quizresourceName").value==""){
            $("#quizUploadAlert").show(500);
        } else {
            document.resource6Form.bookId.value = bookId;
            document.resource6Form.chapterId.value = chapterId;
            document.resource6Form.submit();
            $('.loading-icon').removeClass('hidden');
            $('#fillBlanks').hide();
        }
    }

    function copyQuiz(){
        if(document.getElementById("resId").value==""){
            $("#quizCopyAlert").show().html('Please enter the quiz number.');
            $('#resId').addClass('input-error');
        } else {
            document.resource8Form.reAttempt.value=document.querySelector('#reAttempt').checked;
            document.resource8Form.bookId.value = bookId;
            document.resource8Form.chapterId.value = chapterId;
            document.resource8Form.submit();
            $('.loading-icon').removeClass('hidden');
        }
    }

    function copyRead(){
        if(document.getElementById("readResId").value==""){
            $("#readCopyAlert").show().html('Please enter the read material number.');
            $('#readResId').addClass('input-error');
        } else {
            document.resource9Form.bookId.value = bookId;
            document.resource9Form.chapterId.value = chapterId;
            document.resource9Form.submit();
            $('.loading-icon').removeClass('hidden');
        }
    }
    function showLinksUpload(){
        $("#linksupload").show(500);
    }

    function uploadLinks(){
        if(document.getElementById("weblink").value=="") {
            $("#linksUploadAlert").show();
            $("#weblink").focus().addClass('input-error');
        } else if(document.getElementById("linksresourceName").value==""){
            $("#linksUploadNameAlert").show();
            $("#linksresourceName").focus().addClass('input-error');
        } else {
            document.resource4Form.bookId.value = bookId;
            document.resource4Form.chapterId.value = chapterId;
            document.resource4Form.submit();
            $('.loading-icon').removeClass('hidden');
        }
    }

    function showVideoLinksUpload(){
        $("#videolinksupload").show(500);
    }

    function uploadVideoLinks(){
        var resLink=document.getElementById("videolink").value;
        var downloadlink1=document.getElementById("downloadlink1").value;
        var downloadlink2=document.getElementById("downloadlink2").value;
        var downloadlink3=document.getElementById("downloadlink3").value;
          if(document.getElementById("videolink").value=="") {
              $("#videolinksUploadAlert").show();
              $('#videolink').focus().addClass('input-error');
          } else if(document.getElementById("videolinksresourceName").value=="") {
            $("#videolinksNameUploadAlert").show();
            $('#videolinksresourceName').focus().addClass('input-error');
        }else if(resLink.indexOf('\'') >= 0 || resLink.indexOf('"') >= 0 || resLink.indexOf(' ') >= 0 ||
            downloadlink1.indexOf('\'') >= 0 || downloadlink1.indexOf('"') >= 0 ||
            downloadlink2.indexOf('\'') >= 0 || downloadlink2.indexOf('"') >= 0 ||
            downloadlink3.indexOf('\'') >= 0 || downloadlink3.indexOf('"') >= 0){
            alert('please do not enter " or \' or space in the video link ');
        } else {
            document.resource5Form.bookId.value = bookId;
            document.resource5Form.chapterId.value = chapterId;
            var restitlename =document.getElementById("videolinksresourceName").value;
            document.resource5Form.videolinksresourceName.value = encodeURIComponent(restitlename);
           if(document.getElementById("videolink").value==""){
               document.getElementById("videolink").value="blank";
           }
            var urlCheck =document.getElementById("videolink").value;
            if(urlCheck.includes("youtube")||urlCheck.includes("youtu.be")) {
                document.getElementById("videolink").value = getIdFromYouTubeURL(document.getElementById("videolink").value);
            }
           document.resource5Form.submit();
            $('.loading-icon').removeClass('hidden');
        }
    }

    function validateYouTubeUrl(url) {
        if (url != undefined || url != '') {
            var regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=|\?v=)([^#\&\?]*).*/;
            var match = url.match(regExp);

            if (match && match[2].length == 11) {
                // Do anything for being valid
                // if need to change the url to embed url then use below line
                //$('#ytplayerSide').attr('src', 'https://www.youtube.com/embed/' + match[2] + '?autoplay=0');
                return true;
            } else {
                // Do anything for not being valid
                return false;
            }
        }

        return false;
    }

    function getIdFromYouTubeURL(url) {
        if(url.match(/(youtu.be)/)){
            var split_c = "/";
            var split_n = 3;
        }

        if(url.match(/(youtube.com)/)){
            var split_c = "v=";
            var split_n = 1;
        }

        var getYouTubeVideoID = url.split(split_c)[split_n];
        return getYouTubeVideoID.replace(/(&)+(.*)/, "");
    }

    function createMCQ(){
        window.location="/wonderpublish/quizcreator?page=notes&chapterId="+chapterId+"&bookId="+bookId+"&resourceType=Multiple Choice Questions&useType=quiz&mode=create";
    }
    function openRelatedVideos(){
        var chapterName=encodeURIComponent(document.getElementById("chaptername").value)
        window.open("/wonderpublish/relatedVideosAdmin?chapterId="+chapterId+"&chaptername="+chapterName+"&bookId="+bookId,"_blank");
    }

    function createFlashCards(){
        window.location="/wonderpublish/studySet?chapterId="+chapterId+"&bookId="+bookId+"&resourceType=Flash cards&mode=create";
    }

    function createMCQBulk(){
        window.location="/wonderpublish/quizcreatorbulkinput?page=notes&chapterId="+chapterId+"&bookId="+bookId+"&resourceType=Multiple Choice Questions&useType=quiz&mode=create";
    }
    function createMCQFile(){
        window.open("/excel/uploadMCQ?chapterId="+chapterId,"_blank");
    }

    function createQAFile(){
        window.open("/excel/uploadQA?chapterId="+chapterId,"_blank");
    }
    function createMCQWordFile(){
        window.open("/excel/wordUpload?chapterId="+chapterId+"&bookId="+bookId,"_blank");
    }
    function createHTML(quizMode){
        window.location="/wonderpublish/notescreator?chapterId="+chapterId+"&bookId="+bookId+"&resourceType=Notes&page=notes&mode=create&quizMode="+quizMode;
    }

    function createVideoHTML(quizMode){
        window.location="/wonderpublish/videoExplanation?chapterId="+chapterId+"&bookId="+bookId+"&resourceType=videoexplanation&useType=videoexplanation&mode=create";
    }

    function createQandA(resourceType){
        window.location="/wonderpublish/qandaCreator?chapterId="+chapterId+"&bookId="+bookId+"&resourceType="+resourceType+"&useType=QA&mode=create";
    }
    function createMCQRead(resId,quizId,quizName,quizMode){
        window.location="/wonderpublish/notescreator?chapterId="+chapterId+"&bookId="+bookId+"&resourceType=Notes&page=notes&mode=create&quizMode=read&quizId="+quizId+"&resId="+resId+"&quizName="+quizName;
    }

    function openFlashCard(chapterId,id,bookId,resName){
        <%  if(siteMst.mainResourcesPage != null && siteMst.mainResourcesPage == "true"){ %>
        window.open("/resources/ebook?checkurl=true&siteName="+siteName+"&bookId="+bookId+"&flashcard=true&chapterId="+chapterId+"&resourceName="+resName+"&resourceId="+id,'_blank');
        <%}else{%>
        window.open("/library/"+resName+"?checkurl=true&siteName="+siteName+"&bookId="+bookId+"&flashcard=true&chapterId="+chapterId+"&resourceName="+resName+"&resourceId="+id,'_blank');
        <%}%>
    }
    function showQA(id,bookId){
       window.open("/resources/ebook?checkurl=true&siteName=books&bookId="+bookId+"&resId="+id,'_blank');
   }
    function openBook(id,bookId,chapterId){
       window.open("/resources/ebook?checkurl=true&siteName="+siteName+"&bookId="+bookId+"&chapterId="+chapterId+"&pubDesk=true",'_blank');

       localStorage.setItem("storageResId", id);
   }
    function openWebLink(link){
        window.open(link,'_blank');
   }
    function playAudioFile(bookId,id){
       <%if(wsSite){%>
       window.open("/resources/ebook?checkurl=true&siteName=books&bookId="+bookId,'_blank');
       <%}else{%>
       window.open("/library/ebook?checkurl=true&siteName="+siteName+"&bookId="+bookId,'_blank');
       <%}%>
       localStorage.setItem("storeAudio", id);
   }
    function playVideoFile(bookId,id){
        <%if(wsSite){%>
        window.open("/resources/ebook?checkurl=true&siteName=books&bookId="+bookId,'_blank');
        <%}else{%>
        window.open("/library/ebook?checkurl=true&siteName="+siteName+"&bookId="+bookId,'_blank');
        <%}%>
        localStorage.setItem("storeVideo", id);
    }
    function editHTML(quizMode,resId){
        window.open("/wonderpublish/notescreator?page=notes&chapterId="+chapterId+"&bookId="+bookId+"&resourceType=Notes&useType=notes&mode=edit&id="+resId+"&quizMode="+quizMode,'_blank');
    }

    function editMCQ(resId){
        window.open("/wonderpublish/quizcreator?page=notes&id="+resId+"&chapterId="+chapterId+"&bookId="+bookId+"&resourceType=Multiple Choice Questions&useType=quiz&mode=edit",'_blank');
    }

    function quickEditMCQ(resId){
        window.open("/resources/editQuiz?page=notes&resId="+resId+"&chapterId="+chapterId+"&bookId="+bookId+"&resourceType=Multiple Choice Questions&useType=quiz&mode=edit",'_blank');
    }

    async function addMCQsToMockTest(resId) {
        try {
            $('.loading-icon').removeClass('hidden');
            const response = await fetch("/liveMockTests/addMCQsToMockTest", {
                method: "POST",
                headers: {
                    "Content-Type": "application/x-www-form-urlencoded" // Grails expects form-encoded params by default
                },
                body: new URLSearchParams({ mcqResId: resId }) // sends ?mcqResId=123
            });

            const result = await response.json();

            if (result.status === "success") {
                alert("MCQs added to mock test");
                console.log("✅ MCQs added:", result.message);
            } else {
                alert("Error: " + result.message);
                console.warn("⚠️ Error:", result.message);
            }
            $('.loading-icon').addClass('hidden');
            return result;

        } catch (error) {
            console.error("❌ Request failed:", error);
            return { status: "error", message: "Network error" };
        }
    }




    function editVideoHTML(resId){
        window.open("/wonderpublish/videoExplanation?id="+resId+"&chapterId="+chapterId+"&bookId="+bookId+"&resourceType=videoexplanation&useType=videoexplanation&mode=edit",'_blank');
    }

    function editQA(resId){
        window.open("/wonderpublish/qandaCreator?id="+resId+"&chapterId="+chapterId+"&bookId="+bookId+"&resourceType=QA&useType=QA&mode=edit",'_blank');
    }

    function editFlashCards(resId){
        window.open("/wonderpublish/studySet?chapterId="+chapterId+"&resId="+resId+"&bookId="+bookId+"&resourceType=Flash cards&mode=edit",'_blank');
    }

    function delResource(resId){
        $('#deleteResourceModal').modal('show');
        $('#deleteResourceBtn').attr('onclick','deleteBookResource('+resId+')');
    }

    function deleteBookResource(resId) {
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="wonderpublish" action="deleteResource" params="'id='+resId" onSuccess = "window.location.reload();"/>
    }

    var deleteResId;
    function delResourceVideo(resId,testStartDate){
        deleteResId = resId;
        if(confirm("Are you sure to delete this resource?")) {
            var sendNotification = false;
            if (testStartDate != '' && testStartDate != 'Invalid date'){
                if(confirm("Do you want to send class cancelled notification?")){
                    deleteClassNotification(resId);
                }
            }
            <g:remoteFunction controller="wonderpublish" action="deleteResource" params="'id='+resId" onSuccess = 'window.location.reload();'/>
        }
    }

    function deleteClassNotification(resId){
        <g:remoteFunction controller="log" action="deleteLiveVideoAutoNotifications" params="'mode=delete&resId='+resId" onSuccess = 'deleteNotificationSent(data)'/>
    }

    function deleteNotificationSent(data){
        <g:remoteFunction controller="wonderpublish" action="deleteResource" params="'id='+deleteResId" onSuccess = 'window.location.reload();'/>
    }

    function videoPlayerChangedModel(field){
        if("custom"==field.value){
            $("#modalCommentSection").css('display', 'block');
        }else{
            $("#modalCommentSection").hide();
        }
    }

    function submitSearch(){
        var modelresLink="";
        var resLink=document.getElementById("resname").value;
        var downloadlink1=document.getElementById("downloadlink1edit").value;
        var downloadlink2=document.getElementById("downloadlink2edit").value;
        var downloadlink3=document.getElementById("downloadlink3edit").value;
        if(document.getElementById("restitle").value==""){
            $("#restitle").focus().addClass('input-error');
            $("#restitleError").show();
        }else if(resLink.indexOf('\'') >= 0 || resLink.indexOf('"') >= 0 || resLink.indexOf(' ') >= 0 ||
            downloadlink1.indexOf('\'') >= 0 || downloadlink1.indexOf('"') >= 0 ||
            downloadlink2.indexOf('\'') >= 0 || downloadlink2.indexOf('"') >= 0 ||
            downloadlink3.indexOf('\'') >= 0 || downloadlink3.indexOf('"') >= 0){
            alert('please do not enter " or \' or space in the video link ');
        } else if(document.getElementById("resname").value==""){
            $("#resname").focus().addClass('input-error');
            $("#resnameError").show();
        } else{
            if (document.getElementById("resname").value!=""){
                var urlCheck = document.getElementById("resname").value;
                if (urlCheck.includes("youtube") || urlCheck.includes("youtu.be")) {
                    document.getElementById("resname").value = getIdFromYouTubeURL(document.getElementById("resname").value);
                }
                modelresLink = $("#resname").val();
                modelresLink=modelresLink.replace(/&/g,"~");
            }
            else{
                modelresLink="blank";
            }
            var modelresName = $("#restitle").val();
           // modelresName=modelresName.replace(/~/g,"'").replace(/"/g,'~').replace(/&/g,'and').replace(/%/g,' ');
            modelresName=encodeURIComponent(modelresName)
            var modelresId = $("#resid").val();
            var modalTestStartDate = $("#modalTestStartDate").val();
            var modalTestEndDate = $("#modalTestEndDate").val();
            var modalAllowComments=null;
            var modalDisplayComments=null;
            var modalYoutube=null;
            var modalCustom=null;
            var videoPlayer="";
            var downloadlink1 = $("#downloadlink1edit").val();
            var downloadlink2 = $("#downloadlink2edit").val();
            var downloadlink3 = $("#downloadlink3edit").val();
            if(document.getElementById("modalYoutube").checked){
                videoPlayer="youtube";
            } else{
                if(document.getElementById("modalCustom").checked)
                    videoPlayer="custom";
            }

            downloadlink1=downloadlink1.replace(/&/g,"~");
            downloadlink2=downloadlink2.replace(/&/g,"~");
            downloadlink3=downloadlink3.replace(/&/g,"~");
            if(document.getElementById("modalAllowComments").checked) modalAllowComments="on";
            if(document.getElementById("modalDisplayComments").checked) modalDisplayComments="on";
            <g:remoteFunction controller="Wonderpublish" action="updateResourceData"  params="'resId='+modelresId+'&reslink='+modelresLink+'&restitle='+modelresName+'&allowComments='+modalAllowComments+'&displayComments='+modalDisplayComments+
            '&testStartDate='+modalTestStartDate+'&testEndDate='+modalTestEndDate+'&downloadlink1='+downloadlink1+'&downloadlink2='+downloadlink2+'&downloadlink3='+downloadlink3+'&videoPlayer='+videoPlayer"
       onSuccess = "window.location.reload();"/>
            $('.loading-icon').removeClass('hidden');
        }
    }

    function submitSearch1(){
        if(document.getElementById("weblinktitleedit").value=="") {
            $("#weblinktitleedit").focus().addClass("input-error");
            $("#weblinktitleeditError").show();
        } else if (document.getElementById("weblinkurledit").value=="") {
            $("#weblinkurledit").focus().addClass("input-error");
            $("#weblinkurleditError").show();
        } else {
            var modelresId = $("#weblinkresid").val();
            var weblinktitleedit = encodeURIComponent($("#weblinktitleedit").val());
            var weblinkurledit = $("#weblinkurledit").val();
            <g:remoteFunction controller="Wonderpublish" action="updateResourceData"  params="'resId='+modelresId+'&restitle='+weblinktitleedit+'&reslink='+weblinkurledit"
        onSuccess = "window.location.reload();"/>
            $('.loading-icon').removeClass('hidden');
        }
    }

    function deleteChapter(chapterId){
        $('#deleteChapterModal').modal('show');
        $('#deleteChapterBtn').attr('onclick','deleteBookChapters('+chapterId+')');
    }

    function deleteBookChapters(chapterId) {
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="Wonderpublish" action="deleteChapter" params="'chapterId='+chapterId" onSuccess = "deletedBookChapter();"/>
    }

    function deletedBookChapter() {
        window.location.href = "/book-create-new?bookId="+mybookId+"&chapterId=&toc=false";
    }

    function deleteBookTag(id){
        if(published&&noOfTags==1){
            $('#deleteTagError').show();
        }else{
            $('#deleteCategoryTagModal').modal('show');
            $('#deleteCategoryTagBtn').attr('onclick','deleteBookCategoryTags('+id+')');
        }
    }

    function deleteBookCategoryTags(id) {
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="wonderpublish" action="deleteBookTag" params="'id='+id" onSuccess = "window.location.reload();"/>
    }

    function addNewChapter(){
        if(document.getElementById('title').value=="") {
            titleAlertShown=true;
            $('a[href="#bookDetails"]').tab('show');
            $('a[href="#bookDetails"]').on('shown.bs.tab', function () {
                $('.booktitlealert').show();
                $('#title').removeClass('input-success').addClass('input-error').focus();
            });
        } else {
            chapterId = -1;
            document.getElementById('chaptername').value="";
            document.getElementById('chapterDesc').value="";

            $("#chapterdetails").show(500);
            $("#chapterdetailsdetails").hide();
        }
    }

    function chapterDtlUpdate(field){
        if(document.getElementById('chaptername').value != "") {
            var fieldValue=encodeURIComponent(field.value)
            <g:remoteFunction controller="wonderpublish" action="chapterUpdate" onSuccess='updateChapterId(data);'
            params="'columnName='+field.name+'&columnValue='+fieldValue+'&bookId='+bookId+'&chapterId='+chapterId"></g:remoteFunction>
        }
    }

    function previewChapterUpdate(field){
        if(document.getElementById('previewChapter').selectedIndex == 0) {
            $('#previewChapter').removeClass('input-success').addClass('input-error');
        } else {
            $('#previewChapter').removeClass('input-error').addClass('input-success');
            <g:remoteFunction controller="wonderpublish" action="previewChapterUpdate" onSuccess='previewChapterUpdated(data);'
            params="'bookId='+bookId+'&chapterId='+field[field.selectedIndex].value"></g:remoteFunction>
        }
    }

    function previewChapterUpdated(data){

    }

    function checkPrice(field){
        if(priceErrorShown){
            priceErrorShown=false;
        }
        if(isNumeric(field.value)){
            bookDtlUpdate(field);
        }
        else{
            priceErrorShown=true;
        }
    }

    function displayNewAuthorBox(){
        $("#newAuthorLabel").hide(500);
        $("#newauthor").show(500);
        $("#newauthor").focus();
    }

    function addNewAuthor(){
        if(document.getElementById('title').value=="") {
            $('.booktitlealert').show();
            $('#title').removeClass('input-success').addClass('input-error').focus();
            titleAlertShown=true;
        } else {
            if(document.getElementById('publisherId').value!="") {
                $('#addAuthor').modal('show');
                $('#publisher-error').hide();
                $('#publisherId').removeClass('input-error');
            } else{
               $('#publisher-error').show();
               $('#publisherId').removeClass('input-success').addClass('input-error').focus();
            }
        }
    }

    function submitAuthor() {
        if($('#newauthor').val() == "") {
            $('#newauthor-error').show();
            $('#newauthor').addClass('input-error').focus();
        } else {
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="wonderpublish" action="addAuthor" onSuccess='authorAdded(data);'
        params="'bookId='+bookId+'&author='+document.getElementById('newauthor').value+'&publisherId='+document.getElementById('publisherId').value"></g:remoteFunction>
        }
    }

    function authorAdded(data){
        $('.loading-icon').addClass('hidden');
        document.getElementById('newauthor').value="";
        $('#newauthor-error').hide();
        $('#newauthor').removeClass('input-error');
        $('#authors-error').hide();
        $('.bootstrap-select button').removeClass('input-error').addClass('input-success');
       $('#addAuthor').modal('hide');
        var select = document.getElementById("authors");
        var el = document.createElement("option");
        el.textContent = data.author;
        el.value = data.authorId;
        select.appendChild(el);
        updateAuthorSelect(data.authorId);
        $(select).selectpicker('refresh');
        authorSelected(select);
    }

    function authorSelected(field){
        var optionString="";
        var optionAuthors="";
        if(document.getElementById('title').value=="") {
            $('.booktitlealert').show();
            $('#title').removeClass('input-success').addClass('input-error').focus();
            titleAlertShown=true;

        }else {
            for (i = 0; i < field.options.length; i++) {
                if (field.options[i].selected) {
                    optionString = optionString + field.options[i].value+",";
                    optionAuthors = optionAuthors + field.options[i].text+",";
                }
            }

            if(optionString.length>0) {
                optionString = optionString.substr(0, (optionString.length - 1));
                optionAuthors = optionAuthors.substr(0, (optionAuthors.length - 1));
            }

            <g:remoteFunction controller="wonderpublish" action="updateAuthors" onSuccess='updateBookId(data);'
            params="'bookId='+bookId+'&authors='+optionString+'&optionAuthors='+optionAuthors"></g:remoteFunction>
        }
    }

    function addNewPublisher(){
        if(document.getElementById('title').value=="") {
            $('.booktitlealert').show();
            $('#title').removeClass('input-success').addClass('input-error').focus();
            titleAlertShown=true;
        }else {
            $('#addPublisher').modal('show');
        }
    }

    function submitPublisher() {
        if($('#newpublisher').val() == "") {
            document.getElementById('newpublisher-error').innerText = 'Please enter publisher name.';
            $('#newpublisher-error').show();
            $('#newpublisher').addClass('input-error').focus();
        } else {
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="wonderpublish" action="addPublisher" onSuccess='publisherAdded(data);'
            params="'publisher='+document.getElementById('newpublisher').value"></g:remoteFunction>
        }
    }

    function publisherAdded(data){
        $('.loading-icon').addClass('hidden');
        document.getElementById('newpublisher').value="";
        if(data.status=="Created") {
            document.getElementById('newpublisher-error').innerText = '';
            $('#newpublisher-error').show();
            $('#addPublisher').modal('hide');
            var select = document.getElementById("publisherId");
            var el = document.createElement("option");
            el.textContent = data.publisher;
            el.value = data.publisherId;
            select.appendChild(el);
            updatePublisherSelect(data.publisherId);
        } else if(data.status=="Already exists"){
            document.getElementById('newpublisher-error').innerText = 'This publisher is already exists.';
            $('#newpublisher-error').show();
            $('#newpublisher').addClass('input-error').focus();
        }
    }

    function updatePublisherSelect(publisherId){
        var field = document.getElementById("publisherId");
        for (i = 0; i < field.options.length; i++) {
            if(field.options[i].value==publisherId){
                field.options[i].selected=true;
                $('#publisher-error').hide();
                $('#publisherId').removeClass('input-error').addClass('input-success');
                $('.bootstrap-select button span.filter-option').html('Select Author/s');
                $('.bootstrap-select .dropdown-menu.inner').empty();
            }
        }
    }

    function updatePackageBooks(){
        $('.loading-icon').removeClass('hidden');
        var field = document.getElementById("packageBookIds");
        bookDtlUpdate(field);
    }


    function updateCreatedBy(){
        var transferto = document.getElementById("transferto").value;
        if(transferto == "") {
            $('#transferto').addClass('input-error');
        } else {
            $('#transferBookModal').modal('show');
            $('#bookTransferBtn').attr('onclick','confirmTransfer("'+transferto+'")');
        }
    }
    function confirmTransfer(transferto) {
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="wonderpublish" action="updateCreatedBy" params="'transferto='+transferto+'&bookId='+bookId" onSuccess = "transferCompletion(data);"/>
    }
    function transferCompletion(data){
        window.location.href = "/wonderpublish/pubDesk";
    }

    function bookDetailUpdate(field) {
        if (document.getElementById('title').value == "") {
            $('.booktitlealert').show();
            $('#title').removeClass('input-success').addClass('input-error').focus();
            titleAlertShown = true;
            document.getElementById("publisherId").selectedIndex = 0;
        } else {
            $('#title-error').hide();
            var fieldValue = encodeURIComponent(field.value);
            if (field.name == "tags") fieldValue = $('#' + field.name).val();
            if (field.name == "description") {
                document.updateDescriptionForm.description.value = CKEDITOR.instances.description.getData();
                document.updateDescriptionForm.bookId.value = bookId;
                document.updateDescriptionForm.submit();
            } else {

                <g:remoteFunction controller="wonderpublish" action="bookUpdate" onSuccess='updateBookId(data);'
                    params="'columnName='+field.name+'&bookId='+bookId+'&printBook=${params.printBooks}&columnValue='+fieldValue"></g:remoteFunction>

            }
        }
    }
    function bookDtlUpdate(field){
        var selectedVal = $(field).val();
        <%if(!institutePublisher){%>
        var isbnValue=document.getElementById('isbn').value;
        <%}%>
        if(document.getElementById('title').value=="") {
            $('.booktitlealert').show();
            $('#title').removeClass('input-success').addClass('input-error').focus();
            titleAlertShown =true;
            document.getElementById("publisherId").selectedIndex = 0;
        }
        <%  if("evidya".equals(session["entryController"])|| "etexts".equals(session["entryController"])|| "ebouquet".equals(session["entryController"])) { %>
        else if(field.name=="isbn"){
            if((isbnValue.length< 13) && (isbnValue!="")){
                isbnShown=true;
                //document.getElementById('isbn').value="";
                document.getElementById('isbnerror').innerText = 'Please enter 13 digit ISBN number.';
                $('#isbnerror').show();
                $('#isbn').removeClass('input-success').addClass('input-error');
            }
            else if((isbnValue.length === 13) && (isbnValue!="")){
                var isbnNew=document.getElementById('isbn').value;
                document.getElementById('isbnerror').innerText = '';
                $('#isbnerror').hide();
                $('#isbn').removeClass('input-error').addClass('input-success');
                <g:remoteFunction controller="wonderpublish" action="validateIsbn" onSuccess='updateTest(data)'
                  params="'isbn='+isbnValue+'&bookId='+bookId"></g:remoteFunction>

                function updateTest(data) {
                    if (data.valid == "error") {
                        isbnShown=true;
                        //document.getElementById('isbn').value="";
                        document.getElementById('isbnerror').innerText = 'ISBN already present.';
                        $('#isbnerror').show();
                        $('#isbn').removeClass('input-success').addClass('input-error');
                    }
                }
            }
        }
            <%}%>

        else {
            if(titleAlertShown){
                $('.booktitlealert').hide();
                $('#title').removeClass('input-error').addClass('input-success');
                titleAlertShown=false;
            }
            <%   if("evidya".equals(session["entryController"])|| "etexts".equals(session["entryController"])||"ebouquet".equals(session["entryController"])) { %>
            if(isbnShown){
                isbnShown=false;
                document.getElementById('isbnerror').innerText='';
                $('#isbnerror').hide();
                $('#isbn').removeClass('input-error').addClass('input-success');
            }
            <%}%>

            var fieldValue=encodeURIComponent(field.value);

            if(field.name=="tags") fieldValue = $('#'+field.name).val();
            if(field.name=="description") {
                $('.loading-icon').removeClass('hidden');
                document.updateDescriptionForm.description.value=CKEDITOR.instances.description.getData();
                document.updateDescriptionForm.bookId.value=bookId;
                document.updateDescriptionForm.submit();
            }else if (field.name=="chapterDownloadCount" && selectedVal==''){
                $("#chapterDownloadCountError").show();
                $("#chapterDownloadCount").removeClass('input-success').addClass('input-error');
            }else  {
                <g:remoteFunction controller="wonderpublish" action="bookUpdate" onSuccess='updateBookId(data);'
                    params="'columnName='+field.name+'&bookId='+bookId+'&printBook=${params.printBooks}&columnValue='+fieldValue"></g:remoteFunction>
            }
        }

    }

    function bookDtlUpdateDtl(field){
        var fieldValue=encodeURIComponent(field.value);
        var fieldName=field.name;
        <g:remoteFunction controller="wonderpublish" action="bookDtlUpdate" onSuccess='updatedBookDtl(data);'
            params="'columnName='+fieldName+'&bookId='+bookId+'&columnValue='+fieldValue"></g:remoteFunction>
    }

    function updatedBookDtl(data) {
        $('.loading-icon').addClass('hidden');
    }
    function updateIsbnKeyword(){
        var isbnkeywordsIds = document.getElementById("isbnkeywordsIds").value;
        var isbn = document.getElementById("isbn").value;
        <g:remoteFunction controller="institute" action="addKeywordtoIsbn" params="'keyword='+isbnkeywordsIds+'&isbn='+isbn" onSuccess='successupdateIsbnKeyword(data);'/>

    }
    function successupdateIsbnKeyword(data) {
        if(data.valid=="error"){
            $('#keywords-error').show();
            $('#keywords-success').hide();
            $('#isbnkeywordsIds').removeClass('input-success').addClass('input-error');
        }else{
            $('#keywords-error').hide();
            $('#keywords-success').show();
            $('#isbnkeywordsIds').removeClass('input-error').addClass('input-success');
        }
    }
    $('#language').on('change', function() {
        document.getElementById('isbnerror').innerText = '';
       $('#isbnerror').hide();
        $('#isbn').removeClass('input-error input-success');
    });

    function bookPubDtUpdate(){
        if(document.getElementById('dtPublishedId').value=="") {
            $('#monthError').hide(1000);
            $('#yearError').show(1000);
            pubDtErrorShown =true;
            document.getElementById("dtPublishedId").selectedIndex=0;

        } else if(document.getElementById('dtPublished1Id').value=="") {
            $('#yearError').hide(1000);
            $('#monthError').show(1000);
            pubDtErrorShown =true;
            document.getElementById("dtPublished1Id").selectedIndex=0;

        } else {
            if(pubDtErrorShown){
                $('#yearError').hide(1000);
                $('#monthError').hide(1000);
                pubDtErrorShown=false;
            }

            <g:remoteFunction controller="wonderpublish" action="bookPubDtUpdate" onSuccess='updateBookPubDt(data)'
                    params="'pubYear='+document.getElementById('dtPublishedId').value+'&pubMonth='+document.getElementById('dtPublished1Id').value+'&bookId='+bookId"></g:remoteFunction>
        }
    }

    function updateBookPubDt(data) {

    }

    function updateBookId(data) {
        $('.loading-icon').addClass('hidden');
        bookId = data.bookId;
        <%if(!institutePublisher){%>
        if(!mybookId) {
            $('#copyChapterBtn').attr('onclick','copyChapter("'+bookId+'")').show();
            $('#submitCopyChapterBtn').attr('onclick','submitCopy("'+bookId+'")');
        }
        <%}%>
        if("true"==data.newBook) displayDetails();
        displaychapterssection();

        if(data.authors!=null){
            var select = document.getElementById("authors");
            var length = select.options.length;

            for(i = select.options.length - 1 ; i >= 0 ; i--)
            {
                select.remove(i);
            }
            for(i=0;i<data.authors.length;i++){
               var el = document.createElement("option");
                el.textContent = data.authors[i].name;
                el.value = data.authors[i].id;
                select.appendChild(el);
            }
            $(select).selectpicker('refresh');

        }
    }
    function updateChapterId(data) {
        $("#chapterdetailsdetails").show(500);
        chapterId = data.chapterId;
        if("true"==data.newChapter){
            noOfChapters++;
            var str = "<div id='"+chapterId+"' class='d-flex justify-content-between p-2 ui-sortable-handle'>"+
                        "<a href='javascript:getChapterDetails("+chapterId+")' class='greytext' id='chapter"+chapterId+"'> "+noOfChapters+". "+document.getElementById("chaptername").value+" ("+chapterId+")</a>"+
                    "</div>";
            document.getElementById("chaptersList").innerHTML=document.getElementById("chaptersList").innerHTML+str;
            if(previousChapterId!=-1) $("#chapter"+previousChapterId).addClass('greytext');
            $("#chapter"+chapterId).removeClass('greytext');
            previousChapterId=chapterId;
            document.getElementById("addedContents").innerHTML="";
            document.getElementById("chapter-resources_wrapper").innerHTML="";
        }
    }

    function updateBookCover(imgType,elm){
        var selectedFile;
        if(document.getElementById('title').value=="") {
            $('.booktitlealert').show();
            $('#title').removeClass('input-success').addClass('input-error').focus();
            titleAlertShown=true;
        } else {
            $('.loading-icon').removeClass('hidden');
            if(titleAlertShown){
                $('.booktitlealert').hide();
                $('#title').removeClass('input-error').addClass('input-success');
                titleAlertShown=false;
            }
            const maxSizeInBytes = 100 * 1024;
            if(imgType=='cover') {
                 selectedFile = elm.files[0];
                //here I CHECK if the FILE SIZE is bigger than 20 Kb
                if (selectedFile.size > maxSizeInBytes) {
                    alert("Please upload  image less than 100kb");
                    $('.loading-icon').addClass('hidden');
                }else {
                    document.uploadbookcover.bookId.value = bookId;
                    document.uploadbookcover.submit();
                }
            } else {
                selectedFile = elm.files[0];
                if (selectedFile.size > maxSizeInBytes) {
                    alert("Please upload  image less than 100kb");
                    $('.loading-icon').addClass('hidden');
                }else {
                    document.uploadbookheader.bookId.value = bookId;
                    document.uploadbookheader.submit();
                }
            }
        }
    }

    <%if(selectedAuthors!=null){%>
    <g:each in="${selectedAuthors}" var="selectedAuthor">
     updateAuthorSelect('${selectedAuthor.authorId}');
    </g:each>
    <%}%>

    function updateAuthorSelect(authorId){
        var field = document.getElementById("authors");
        for (i = 0; i < field.options.length; i++) {
            if(field.options[i].value==authorId){

                field.options[i].selected=true;
            }
        }
    }

    function getChapterDetails(chapterIdFromList){
        $('.loading-icon').removeClass('hidden');
        var $iframe = $('#htmlreadingcontent');
        $iframe.contents().find('body').html("");
        chapterId = chapterIdFromList;

        if(previousChapterId!=-1) $("#chapter"+previousChapterId).addClass('greytext');
        $("#chapter"+chapterId).removeClass('greytext');
        previousChapterId=chapterId;

        <g:remoteFunction controller="wonderpublish" action="chapterMstDetails" onSuccess='updateChapterDesc(data);'
            params="'chapterId='+chapterIdFromList"></g:remoteFunction>
        if(chapterId!=null && chapterId!="null" && chapterId!="") {
            getTopicDetails(chapterIdFromList,'bookauthor');
        }
        hideDetails();
    }

    function getHtmlsData(resId){
        var $iframe = $('#htmlreadingcontent');
        $iframe.contents().find('body').html("");
        <g:remoteFunction controller="funlearn" action="getHtmlsForWeb"  onSuccess='displayHtmls(data);'
                    params="'resId='+resId" />
    }

    function displayHtmls(data){
        var htmls = data.htmlsContent;
        document.getElementById("htmlreadingcontent").height=screen.availHeight;
        var $iframe = $('#htmlreadingcontent');
        $iframe.contents().find('body').html("");
        $iframe.ready(function() {
            if(data.onlineHtml) {
                htmls = htmls.replace(/\\\\/g , '\\');

                $iframe.contents().find('body').html(htmls);

                var script = $iframe.contents()[0].createElement('script');
                script.type = 'text/javascript';
                script.text = 'renderMathInElement(document.body)';
                $iframe.contents().find('body')[0].appendChild(script);
            } else
                $iframe.contents().find('body').html(htmls.substring(0,htmls.indexOf("<head>")+6)+'<style type="text/css">'+data.cssString+'</style>'+htmls.substring(htmls.indexOf("<head>")+7));
        });
    }

    function updateChapterDesc(data){
        $('.loading-icon').addClass('hidden');
        document.getElementById("chaptername").value=data.chapterName;
        document.getElementById("chapterDesc").value=data.chapterDesc;

        $("#chapterdetailsdetails").show(500);
    }
    <% if(chaptersMst!=null){
        if(chaptersMst.size()>0) {
            if(chapterId!=null && chapterId!="null" && chapterId!="") {%>
    getChapterDetails('${chapterId}');
    <%}
     }
    }%>

   function displaychapterssection(){
       if(document.getElementById('title').value==""){
          // $("#showdetails").hide();
       } else {
          $("#chapterssection").show(500);
       }
       $( "#chaptersList" ).sortable();
       $( "#chaptersList" ).disableSelection();
   }

    function hideDetails(){
        $("#showdetails").show();

    }

    function displayDetails(){
        $("#bookdetails").show(500);
        $("#showdetails").hide();
    }

    displaychapterssection();
    $('#nxt-btn').on('click', function() {
        $('a[href="#bookChapters"]').tab('show');
        $('a[href="#bookChapters"]').on('shown.bs.tab', function () {
            $("html body").animate({ scrollTop: 0 }, "slow");
        })
    });

    $('#back-btns').on('click', function() {
        $('a[href="#bookDetails"]').tab('show');
    });
    $('#videoModal').on('hidden.bs.modal', function () {
        $(".modal-body iframe").attr('src', '');
    });
    $('.nav-tabs a[href="#createBook"]').tab('show');
    $("#videolink").blur(function() {
        var apiKeys = '${apiKey}';
        var apiKey;
        if(apiKeys.indexOf(",")>0){
            var apiKeysArray = apiKeys.split(',');
            var randomIndex =  Math.floor(Math.random() * 5);
            apiKey = apiKeysArray[randomIndex];
        }else{
            apiKey = apiKeys;
        }
        var urlCheck =document.getElementById("videolink").value;
        if(urlCheck.includes("youtube")||urlCheck.includes("youtu.be")) {
            var videoId= getIdFromYouTubeURL(document.getElementById("videolink").value);
            var gUrl = "https://www.googleapis.com/youtube/v3/videos?id=" + videoId + "&key=" + apiKey + "&part=snippet";
            $.get(gUrl, function (data) {
                var youtubetitle =data.items[0].snippet.title;
                youtubetitle = youtubetitle.replace(/'/g,"&#39;")
                document.getElementById("videolinksresourceName").value = youtubetitle;

            });
        }
        $('#videolinksNameUploadAlert').hide();
        $('#videolinksresourceName').removeClass('input-error');
    });

    $("#resname").blur(function() {
        var apiKeys = '${apiKey}';
        var apiKey;
        if(apiKeys.indexOf(",")>0){
            var apiKeysArray = apiKeys.split(',');
            var randomIndex =  Math.floor(Math.random() * 5);
            apiKey = apiKeysArray[randomIndex];
        }else{
            apiKey = apiKeys;
        }
        var urlCheck =document.getElementById("resname").value;
        if(urlCheck.includes("youtube")||urlCheck.includes("youtu.be")) {
            var videoId = getIdFromYouTubeURL(document.getElementById("resname").value);
            var gUrl = "https://www.googleapis.com/youtube/v3/videos?id=" + videoId + "&key=" + apiKey + "&part=snippet";
            $.get(gUrl, function (data) {
                var youtubetitle = data.items[0].snippet.title;
                youtubetitle = youtubetitle.replace(/'/g,"&#39;");
                %{--string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')--}%
                document.getElementById("restitle").value = youtubetitle;

            });
        }

    });

    function showDeeplink(data) {
        $('.loading-icon').addClass('hidden');
        if(data.deeplink!=null && data.deeplink!="" && data.deeplink!="null"){
            alert(data.deeplink)
        }else{
            alert("error")
        }
    }

    function getDeepLink(chapterId,resId,resType,createFor){
        var deeplinkbookId=bookId;
        if(createFor=="book" && document.getElementById('title').value=="") {
            $('.booktitlealert').show();
            $('#title').removeClass('input-success').addClass('input-error').focus();
            titleAlertShown =true;
        }else {
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="log" action="getBookandResDeepLinkById" params="'chapterId='+chapterId+'&resId='+resId+'&resType='+resType+'&bookId='+deeplinkbookId+'&createFor='+createFor" onSuccess='showDeeplink(data);'/>
        }
    }

    function getServerPath() {

        var localPath = window.location.href;
        var thirdIndex = localPath.indexOf('/', 8);
        var serverPath = localPath.substring(0, thirdIndex);
        serverPath = serverPath.replace("publish.","");
        return serverPath;
    }
    <%if("fail".equals(request.getParameter("quizCopyMode"))){%>
    alert("Quiz copy failed");
    <%} %>

</script>

<script>
    function HandleBrowseClick()
    {
        var fileinput = document.getElementById("file3");
        fileinput.click();
    }

    function Handlechange()
    {
        var fileinput = document.getElementById("file3");
        var textinput = document.getElementById("filename");
        textinput.value = fileinput.value;
    }

    <%if(params.chapterId!=null){%>

    $('#bookChaptersTab').trigger('click')
    <%}%>

    var checkedVal = $('input[name="downloadChapters"]:checked').val();

    if (checkedVal=='Yes'){
        $('#downloadChaptersNo').show();
    }else if (checkedVal=='No'){
        $('#downloadChaptersNo').hide();
    }else{
        $('#downloadChaptersNo').hide();
    }

    $('input[name="downloadChapters"]').on('change',function (){
        checkedVal = $('input[name="downloadChapters"]:checked').val();

        if (checkedVal=='Yes'){
            $('#downloadChaptersNo').show();
        }else if (checkedVal=='No'){
            $('#downloadChaptersNo').hide();
        }else{
            $('#downloadChaptersNo').hide();
        }
    })

</script>
<script src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.20/js/dataTables.bootstrap4.min.js"></script>

<script>
    $(document).ready(function () {
        $('#title').on('keyup', function () {
            $('.booktitlealert').hide();
            $(this).removeClass('input-error').addClass('input-success');
        });

        $('#publisherId').on('change', function () {
            if(this.selectedIndex==0 && $('#title').val() != "") {
                $('#publisher-error').show();
                $(this).removeClass('input-success').addClass('input-error');
                $('.bootstrap-select button span.filter-option').html('Select Author/s');
                $('.bootstrap-select .dropdown-menu.inner').empty();
            } else if(this.selectedIndex!=0 && $('#title').val() != ""){
                $('#publisher-error').hide();
                $(this).removeClass('input-error').addClass('input-success');
            }
            $('#authors-error').hide();
            $('.bootstrap-select button').removeClass('input-error input-success');
        });

        $('#authors').on('change', function () {
            if(this.selectedIndex==-1) {
                $('#authors-error').show();
                $('.bootstrap-select button').removeClass('input-success').addClass('input-error');
            } else {
                $('#authors-error').hide();
                $('.bootstrap-select button').removeClass('input-error').addClass('input-success');
            }
        });

        $('#bookTypeId').on('change', function () {
            if(this.selectedIndex==0) {
                $('#booktype-error').show();
                $('#bookTypeId').removeClass('input-success').addClass('input-error');
            } else {
                $('#booktype-error').hide();
                $('#bookTypeId').removeClass('input-error').addClass('input-success');
            }
        });

        $('#addPublisher').on('shown.bs.modal', function () {
            $('#newpublisher').focus().on('keyup', function () {
                document.getElementById('newpublisher-error').innerText = '';
                $('#newpublisher-error').hide();
                $(this).removeClass('input-error');
            });
        }).on('hidden.bs.modal', function () {
            document.getElementById('newpublisher-error').innerText = '';
            $('#newpublisher-error').hide();
            $('#newpublisher').removeClass('input-error');
        });

        $('#addAuthor').on('shown.bs.modal', function () {
            $('#newauthor').focus().on('keyup', function () {
                $('#newauthor-error').hide();
                $(this).removeClass('input-error');
            });
        }).on('hidden.bs.modal', function () {
            $('#newauthor-error').hide();
            $('#newauthor').removeClass('input-error');
        });

        $('#listprice').on('keyup', function () {
            if($(this).val() != "") {
                $('#listprice-error').hide();
                $(this).removeClass('input-error').addClass('input-success');
            } else {
                $('#listprice-error').show();
                $(this).removeClass('input-success').addClass('input-error');
            }
        });

        $('#price').on('keyup', function () {
            if($(this).val() != "") {
                $('#sellprice-error').hide();
                $(this).removeClass('input-error').addClass('input-success');
            } else {
                $('#sellprice-error').show();
                $(this).removeClass('input-success').addClass('input-error');
            }
        });

        $('#isbn').on('keyup', function () {
            if($(this).val() != "") {
                document.getElementById('isbnerror').innerText = '';
                $('#isbnerror').hide();
                $(this).removeClass('input-error').addClass('input-success');
                $('#isbnkeywordsIds').removeClass('input-error');
            } else {
                document.getElementById('isbnerror').innerText = 'Please enter ISBN number.';
                $('#isbnerror').show();
                $(this).removeClass('input-success').addClass('input-error');
            }
        });

        $('#isbnkeywordsIds').on('keyup', function () {
            $('#keywords-error, #keywords-success').hide();
            $(this).removeClass('input-error').addClass('input-success');
        });

        $('#createLevel').on('change', function () {
            if(this.selectedIndex==0) {
                $('#categoryTagsError').show();
                $(this).removeClass('input-success').addClass('input-error');
                $('#createSyllabus,#createGrade,#createSubject').hide();
            } else {
                $('#categoryTagsError').hide();
                $(this).removeClass('input-error').addClass('input-success');
                $('#createGrade,#createSubject').hide();
            }
        });

        $('#createSyllabus').on('change', function () {
            if(this.selectedIndex==0) {
                $(this).removeClass('input-success').addClass('input-error');
                $('#createGrade,#createSubject').hide();
            } else {
                $(this).removeClass('input-error').addClass('input-success');
                $('#createSubject').hide();
            }
        });

        $('#createGrade').on('change', function () {
            if(this.selectedIndex==0) {
                $(this).removeClass('input-success').addClass('input-error');
                $('#createSubject').hide();
            } else {
                $(this).removeClass('input-error').addClass('input-success');
            }
        });

        $('#createSubject').on('change', function () {
            if(this.selectedIndex==0) {
                $(this).removeClass('input-success').addClass('input-error');
            } else {
                $(this).removeClass('input-error').addClass('input-success');
            }
        });

        $('#epubFileInput').on('change', function () {
            $('#epubFileError').hide();
            $(this).removeClass('input-error').addClass('input-success');
        });

        $('#transferto').on('change', function () {
            if(this.selectedIndex==0) {
                $(this).removeClass('input-error input-success');
            } else {
                $(this).removeClass('input-error').addClass('input-success');
            }
        });

        $('#copyQuiz').on('shown.bs.modal', function () {
            $('#resId').focus().on('keyup', function () {
                var checkNumber = $(this).val();

                if(jQuery.isNumeric(checkNumber) == false){
                    $("#quizCopyAlert").show().html('Please enter numbers only.');
                    $(this).addClass('input-error');
                    this.value = this.value.replace(/[^0-9\.]/g,'');
                    return true;
                } else {
                    $("#quizCopyAlert").hide().html('');
                    $(this).removeClass('input-error');
                }
            });
        }).on('hidden.bs.modal', function () {
            $('#quizCopyAlert').hide().html('');
            $('#resId').removeClass('input-error');
        });

        $('#readQuiz').on('shown.bs.modal', function () {
            $('#readResId').focus().on('keyup', function (event) {
                var checkNumber = $(this).val();

                if(jQuery.isNumeric(checkNumber) == false){
                    $("#readCopyAlert").show().html('Please enter numbers only.');
                    $(this).addClass('input-error');
                    this.value = this.value.replace(/[^0-9\.]/g,'');
                    return true;
                } else {
                    $("#readCopyAlert").hide().html('');
                    $(this).removeClass('input-error');
                }
            });
        }).on('hidden.bs.modal', function () {
            $('#readCopyAlert').hide().html('');
            $('#readResId').removeClass('input-error');
        });

        $('#chaptername').on('keyup', function () {
            if(this.value=="") {
                $(this).addClass('input-error');
            } else {
                $(this).removeClass('input-error');
            }
        });

        $('#paidVideos').on('shown.bs.modal', function () {
            $('#mediaresourceName').focus().on('keyup', function () {
                $('#paidVideoNameAlert').hide();
                $(this).removeClass('input-error');
            });
            $('#file10').on('change', function () {
                $('#paidVideoAlert').hide();
                $(this).removeClass('input-error');
            });
        }).on('hidden.bs.modal', function () {
            $('#paidVideoNameAlert,#paidVideoAlert').hide();
            $('#mediaresourceName,#file10').removeClass('input-error');
        });

        $('#referenceWebLinks').on('shown.bs.modal', function () {
            $('#weblink').focus().on('keyup', function () {
                $('#linksUploadAlert').hide();
                $(this).removeClass('input-error');
            });
            $('#linksresourceName').on('keyup', function () {
                $('#linksUploadNameAlert').hide();
                $(this).removeClass('input-error');
            });
        }).on('hidden.bs.modal', function () {
            $('#linksUploadAlert,#linksUploadNameAlert').hide();
            $('#weblink,#linksresourceName').removeClass('input-error');
        });

        $('#referenceVideoLinks').on('shown.bs.modal', function () {
            $('#videolink').focus().on('keyup', function () {
                $('#videolinksUploadAlert').hide();
                $(this).removeClass('input-error');
            });
            $('#videolinksresourceName').on('keyup', function () {
                $('#videolinksNameUploadAlert').hide();
                $(this).removeClass('input-error');
            });
        }).on('hidden.bs.modal', function () {
            $('#videolinksUploadAlert,#videolinksNameUploadAlert').hide();
            $('#videolink,#videolinksresourceName').removeClass('input-error');
        });

        $('#readingMaterials').on('shown.bs.modal', function () {
            $('#resourceName').focus().on('keyup', function () {
                $('#notesUploadAlert').hide();
                $(this).removeClass('input-error');
            });
            $('#file3').on('change', function () {
                $('#UploadSizeAlert').hide();
                document.getElementById('UploadSizeAlert').innerText = '';
                $(this).removeClass('input-error');
            });
        }).on('hidden.bs.modal', function () {
            $('#notesUploadAlert,#UploadSizeAlert').hide();
            $('#resourceName,#file3').removeClass('input-error');
            document.getElementById('UploadSizeAlert').innerText = '';
        });

        $('#restitle').on('keyup', function () {
            $('#restitleError').hide();
            $(this).removeClass('input-error');
        });
        $('#resname').on('keyup', function () {
            $('#resnameError').hide();
            $(this).removeClass('input-error');
        });

        $('#weblinktitleedit').on('keyup', function () {
            $('#weblinktitleeditError').hide();
            $(this).removeClass('input-error');
        });
        $('#weblinkurledit').on('keyup', function () {
            $('#weblinkurleditError').hide();
            $(this).removeClass('input-error');
        });

        $('#chapterDownloadCount').on('change', function () {
            if(this.selectedIndex==0) {
                $('#chapterDownloadCountError').show();
                $(this).removeClass('input-success').addClass('input-error');
            } else {
                $('#chapterDownloadCountError').hide();
                $(this).removeClass('input-error').addClass('input-success');
            }
        });

        $('#resourceStartDatePicker').click(function () {
            $('#videolinksresourceStartDate').datetimepicker('show');
        });
        $('#resourceEndDatePicker').click(function () {
            $('#videolinksresourceEndDate').datetimepicker('show');
        });
        $('#modalTestStartDatePicker').click(function () {
            $('#modalTestStartDate').datetimepicker('show');
        });
        $('#modalTestEndDatePicker').click(function () {
            $('#modalTestEndDate').datetimepicker('show');
        });

        $('#transferto').on('change', function () {
            if($('#title').val() == "") {
                $('.booktitlealert').show();
                $('#title').addClass('input-error').focus();
                document.getElementById('transferto').selectedIndex = 0;
                $(this).removeClass('input-success');
            }
        });

        if(mybookId) {
            $('#copyChapterBtn').attr('onclick','copyChapter("'+mybookId+'")').show();
            $('#submitCopyChapterBtn').attr('onclick','submitCopy("'+mybookId+'")');
        }

    });

    $('body').on('click', function(e) {
        if ($(e.target).closest('.bootstrap-select .dropdown-menu').length == 0) {
            $('.bootstrap-select').removeClass('open');
        }
    });

    function onlyNumberKey(evt) {
        // Only ASCII charactar in that range allowed
        var ASCIICode = (evt.which) ? evt.which : evt.keyCode
        if (ASCIICode > 31 && (ASCIICode < 48 || ASCIICode > 57))
            return false;
        return true;
    }
</script>
<script>
    $('input[name=convert]').change(function (event) {
        if(event.target.value == "false" || event.target.value == false){
            var str ="<i>Allow download (not secure)</i>\n" +
                "                                <div class=\"d-flex align-items-center\">\n" +
                "                                    <input type=\"radio\" name=\"pdf_download\" value=\"yes\" id=\"pdf_download_yes\" class=\"d-none\">\n" +
                "                                    <label for=\"pdf_download_yes\" class=\"pdf_download_yes\">Yes</label>\n" +
                "                                    <input type=\"radio\" name=\"pdf_download\" value=\"no\" id=\"pdf_download_no\"  class=\"d-none\" checked>\n" +
                "                                    <label for=\"pdf_download_no\" class=\"pdf_download_no\">No</label>\n" +
                "\n" +
                "\n" +
                "                                </div>"
            $("#allow_pdf_download").html(str);
        }else {
            $("#allow_pdf_download").html("");
        }
    });
    $("#videolink").blur(function() {
        var urlCheck =document.getElementById("videolink").value;
        if(urlCheck.includes("youtube")||urlCheck.includes("youtu.be")) {
            var apiKeys = '${apiKey}';
            var apiKey;
            if(apiKeys.indexOf(",")>0){
                var apiKeysArray = apiKeys.split(',');
                var randomIndex =  Math.floor(Math.random() * 5);
                apiKey = apiKeysArray[randomIndex];
            }else{
                apiKey = apiKeys;
            }
            var videoId = getIdFromYouTubeURL(document.getElementById("videolink").value);
            var gUrl = "https://www.googleapis.com/youtube/v3/videos?id=" + videoId + "&key=" + apiKey + "&part=snippet";
            $.get(gUrl, function (data) {
                var youtubetitle = data.items[0].snippet.title;
                document.getElementById("videolinksresourceName").value = youtubetitle;

            });
        }


    });
</script>
<g:render template="/bookPrice/priceManager"></g:render>
</html>

<asset:stylesheet href="wonderslate/flashcard.css" async="true"/>
<style>
    .cancel-revise:hover{
        background: none !important;
    }
</style>
<script>
    var newStudySet=false;
    var flashTemplate="";
    var rotateSlider;
    var setId;
    var resname;
    var doneMode=false;
    //create card first time;
function createNewSet() {
   document.getElementById('htmlreadingcontent').innerHTML="";
    flashCardEmptyTemplate();
}



//create Empty template in existing card
    function flashCardEmptyTemplate() {
        document.getElementById('htmlreadingcontent').style.display='block';
        var isEmpty = document.getElementById('htmlreadingcontent').innerHTML == "";
        var flashEmptyTemplate;
        if(isEmpty){
            newStudySet=true;
            studySetResId=-1;
            flashEmptyTemplate= "<div id='slider-wrapper' style='display:none;'></div>"+
                "<div class=\"col-12 main-wrapper non-ws mt-4\">\n"+
              " <div class=\"form-group\">\n" +
                "            <div class=\"d-flex setname-wrapper col-12 col-md-5\">\n" +
                "                <button class=\"back-flashCard\" onclick=\"javascript:backToMain();\"><i class=\"material-icons\">keyboard_backspace</i> </button>\n" +
                "                <input type=\"text\" class=\"form-control\" placeholder=\"Enter a title, like 'Magnetism, Science class 10'\" id=\"revisionTitle\">\n" +
                "            </div>\n" +
                "</div>\n"+
                "<div class=\"card-box col-12 col-md-6 col-lg-4\" id='empty'>\n" +
                                 "<span class='card-count'></span>" +
                                "<div class=\"cardHeader\">\n" +
                                    " <p>Front</p>\n" +
                                    "<button class='delete-card'><i class='material-icons'>delete</i></button>"+
                "                </div>\n" +
                "                <div class=\"revision-question\">\n" +
                "                    <form>\n" +
                "                        <textarea class=\"form-control\" placeholder=\"Front\" id=\"term\" maxlength='500'></textarea>\n" +
                "                    </form>\n" +
                "                </div>\n" +
                "                <button class=\"add-text mb-2\">Back</button>\n" +
                "                <div class=\"revision-answer\">\n" +
                "                   <textarea class=\"form-control mb-2\" placeholder=\"Back\" value='' id=\"definition\" maxlength='500'></textarea>\n" +
                "                </div>\n" +


                "            </div>\n" +
            "</div>";
            flashEmptyTemplate += "<div class=\"col-12 mt-3 submit-buttons non-ws text-center\">\n" +
             "        <a href=\"javascript:addKeyValueCard();\" class=\"btn btn-default save-card\">\n" +
                "            <span>+ Add Card</span>\n" +
                "        </a>\n" +
                "    </div>";
            document.getElementById('htmlreadingcontent').innerHTML += flashEmptyTemplate;
        }
        else{
            flashEmptyTemplate =
                "<div class=\"col-12 col-md-6 col-lg-4 mt-4\" id='empty'>\n"+
                "<div class=\"card-box\">\n" +
            "<span class='card-count'></span>" +
            "<div class=\"cardHeader\">\n" +
            " <p>Front</p>\n" +
            "<button class='delete-card'><i class='material-icons'>delete</i></button>"+
            "                </div>\n" +
                "                <div class=\"revision-question\">\n" +
                "                    <form>\n" +
                "                        <textarea class=\"form-control\" placeholder=\"Front\" id=\"term\" maxlength='500'></textarea>\n" +
                "                    </form>\n" +
                "                </div>\n" +
            "                <button class=\"add-text mb-2\">Back</button>\n" +
                "                <div class=\"revision-answer\">\n" +
                "                   <textarea class=\"form-control mb-2\" placeholder=\"Back\" value='' id=\"definition\" maxlength='500'></textarea>\n" +
                "                </div>\n" +


            "            </div>\n" +
            "        </div>";

             document.getElementById('my-cards').innerHTML += flashEmptyTemplate;

        }
        document.getElementById('content-data-all').style.display='none';
        $('#allAddButton').hide();
    }


    function addKeyValueCard() {
        // addRevisionToAllTab();
        var noOfItems=1;

        if(document.getElementById("revisionTitle").value==""){
            document.getElementById("revisionTitle").focus();
        } else if(document.getElementById("term").value=="") {
            document.getElementById("term").focus();
        } else if(document.getElementById("definition").value=="") {
            document.getElementById("definition").focus();
        } else {
            var title=encodeURIComponent(document.getElementById("revisionTitle").value);
            //  $('.loading-icon').removeClass('hidden');
            var params = "noOfItems="+noOfItems+"&chapterId="+previousChapterId+"&studySetResId="+studySetResId+"&title="+title;

            for (i = 0; i < noOfItems;i++) {
                params += "&term"+i+"="+encodeURIComponent(document.getElementById("term").value);
                params += "&definition"+i+"="+encodeURIComponent(document.getElementById("definition").value);
            }

            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="resources" action="addKeyValues" onSuccess='keyValuesSaved(data);' params="params"></g:remoteFunction>
        }
    }

function keyValuesSaved(data) {
    $('.loading-icon').addClass('hidden');

    if(doneMode){
        backToSlider(studySetResId);
    }
    else{
        getRevisionSet(data.resId);
    }
    addRevisionToAllTab(data.title,data.resId);

}
//Open Exisiting flashcard

    function goToEditScreen(resId) {
        setId=resId;
        document.getElementById('content-data-all').style.display='none';
        document.getElementById('htmlreadingcontent').style.display='block';
        //flashCardEmptyTemplate();
        getRevisionSet(resId);
        doneMode=false;
    }
//get Existing Revision set

function getRevisionSet(id) {

    <g:remoteFunction controller="funlearn" action="getFlashCards" onSuccess='displayRevisionSets(data);' params="'resId='+id"/>
}

//slider Wrapper
    function sliderTemplate() {
        var slideTemplate="";
        slideTemplate="<div id='slider-wrapper'></div>";
        document.getElementById('content-data-all').style.display='none';
        document.getElementById('htmlreadingcontent').style.display='block';
        document.getElementById('htmlreadingcontent').innerHTML +=slideTemplate;
    }

//get slider data
    function reviseNow(resId,name) {
        var id=resId;
        resname=name;
        if(resname==undefined){
            var titleName=document.getElementById('revisionTitle').value;
            resname=titleName;
        }
        sliderTemplate();
        $('.loading-icon').removeClass('hidden');

        <g:remoteFunction controller="funlearn" action="getFlashCards" onSuccess='displaySlider(data);' params="'resId='+id"/>
    }


//Display slider
    function displaySlider(data){
        var colors=['#2EBAC6','#0D5FCE','#6FCF97','#F2C94C','#C20232','#FC7753','#E40039','#1abc9c','#FD7272','#55E6C1','#17c0eb'];
        var keyValues=data.keyValues;
        var slider="";
        slider +="<div class='d-flex align-items-center justify-content-between mb-4 mt-md-4 p-3'>" +
            "<button id='back-slider' onclick='javascript:backToMain()' class='btn btn-back d-flex align-items-center'>" +
            "<i class='material-icons mr-1'>arrow_back_ios</i> Back" +
            "</button>" ;
        slider +="<div><h4>"+resname+"</h4></div>";
        if(data.canEdit=="true") {
            slider += "<div><button class='d-flex align-items-center edit' onclick='javascript:goToEditScreen(" + data.resId + ")'> <i class='material-icons'>edit</i> Edit</button> </div>" ;
        }
        else{
            slider += "<div></div>" ;
        }
        slider+="</div>";
        if(keyValues.length !=0) {
            slider += "<div class=\"flashcards non-ws\">\n";
            for (var i = 0; i < keyValues.length; i++) {
                slider += "    <div class=\"flash-card\">\n" +
                    "        <div class=\"flip-box-inner\">\n" +
                    "            <div class=\"flip-box-front\">\n" +
                    "<p>" + keyValues[i].term + "</p>" +
                    "            </div>\n" +
                    "            <div class=\"flip-box-back\">\n" +
                    "<p>" + keyValues[i].definition + "</p>" +
                    "            </div>\n" +
                    "        </div>\n" +
                    "    </div>\n";
            }

            slider += "    <button class=\"card-previous\" id=\"buttonPrev\">\n" +
                "        <i class=\"material-icons\">\n" +
                "            arrow_back\n" +
                "        </i>\n" +
                "    </button>\n" +
                "    <button class=\"card-next\" id=\"buttonNext\">\n" +
                "        <i class=\"material-icons\">\n" +
                "            arrow_forward\n" +
                "        </i>\n" +
                "    </button>\n" +
                "<button class='turn-slide' onclick='javascript:turnSlider()'><i class='material-icons'>loop</i> </button>" +
                "</div>\n" +
                "\n" +
                "<div>\n" +
                "    <p id=\"totalSlides\"></p>\n" +
                "    <span id=\"activeSlide\"></span>\n" +

                "</div>";
        }
        else{
            slider +="<div class='text-center d-flex align-items-center justify-content-center' style='height:60vh'>" +
                "<div>"+
                "<h4>Your set is Empty.</h4>"+
                "<button class='d-flex align-items-center edit m-auto' onclick='javascript:goToEditScreen(" + data.resId + ")'> " +
                "Add</button> " +
                "</div>"+
                "</div>" ;
        }
        $('.loading-icon').addClass('hidden');
        document.getElementById('htmlreadingcontent').innerHTML=slider;
        if(keyValues.length === 1){
            document.getElementById("buttonNext").style.display = 'none';
            document.getElementById("buttonPrev").style.display = 'none';
        }

        $('#slider-wrapper').show();
        $('.main-wrapper,.submit-buttons,#allAddButton').hide();
        var front = document.querySelectorAll(".flip-box-front");
        var back = document.querySelectorAll(".flip-box-back");
        rotateSlider=document.querySelectorAll('.flip-box-inner');
        for(var i=0 ; i < front.length; i++) {
            front[i].style.background = colors[i%11];
            back[i].style.background = colors[i%11];
        }
        $('.flash-card').first().addClass('active');
        $('.flash-card').hide();
        $('.flash-card.active').show();
            if($('.flash-card').hasClass('active')){
               $('.flash-card.active').next('.flash-card').addClass('slide2');
                $('.flash-card.active').next().next('.flash-card').addClass('slide3');
            }
        $('#buttonNext').click(function(){
            document.getElementById("buttonPrev").disabled = false;
            $('.flash-card.active').removeClass('active').addClass('oldActive');
            $('.flash-card').removeClass('slide2 slide3 slide-left slide-right');

            if ( $('.oldActive').is(':last-child')) {
                $('.flash-card').first().addClass('active');
            }
            else{
                $('.oldActive').next('.flash-card').addClass('active');
                $('.oldActive').next().next('.flash-card').addClass('slide2');
                $('.oldActive').next().next('.flash-card').next('.flash-card').addClass('slide3');
            }
            $('.oldActive').removeClass('oldActive').addClass('slide-left');
            $('.flash-card').fadeOut();
            $('.flash-card.active').fadeIn();
            if($('.flash-card:last').hasClass('active')){
                document.getElementById("buttonNext").disabled = true;
            }

        });
        $('#buttonPrev').click(function(){
            document.getElementById("buttonNext").disabled = false;
            $('.flash-card.active').removeClass('active').addClass('oldActive');
            $('.flash-card').removeClass('slide2 slide3 slide-right slide-left');
            if ( $('.oldActive').is(':first-child')) {
                $('.flash-card').last().addClass('active');
            }
            else{
                $('.oldActive').prev('.flash-card').addClass('active').addClass('slide-right');;
                $('.oldActive').prev().prev('.flash-card').addClass('slide3');
                $('.oldActive').prev().prev('.flash-card').prev('.flash-card').addClass('slide2');
            }
            $('.oldActive').removeClass('oldActive');
            $('.flash-card').fadeOut();
            $('.flash-card.active').fadeIn();
            if($('.flash-card:first').hasClass('active')){
                document.getElementById("buttonPrev").disabled = true;
            }
        });
        if(showPubSlide){
            $('#content-data-all').hide().addClass('d-none');
            $('#htmlreadingcontent').show();
        }

    }


//Display Revision set
function displayRevisionSets(data) {
    var keyValues=data.keyValues;
    newStudySet=false;
    studySetResId = data.resId;
    var displaySet="";
    displaySet +=" <div class=\"form-group\">\n" +
        "            <div class=\"d-flex setname-wrapper col-12 col-md-5 mt-md-4\">\n" +
        "                <button class=\"back-flashCard\" onclick=\"javascript:backToSlider(" + studySetResId + ");\"><i class=\"material-icons\">keyboard_backspace</i> </button>\n" +
        "                <input type=\"text\" class=\"form-control\" placeholder=\"Enter a title, like 'Magnetism, Science class 10'\" id=\"revisionTitle\">\n" +
        "            </div>\n" +
        "</div>"+"<div class='row pl-4 pr-4' id='my-cards'>";

    for(var i=0;i<keyValues.length;i++){
        displaySet +="<div class=\"col-12 col-md-6 col-lg-4 mt-4\" id=\"card"+keyValues[i].id+"\">\n" +
            "            <div class=\"card-box\">\n" +
            "<span class='card-count'></span>"+
            "                <div class=\"cardHeader\">\n" +
             "                    <p>Front</p>\n" +
            "<button class='delete-card' onclick='javascript:deleteCard("+keyValues[i].id+")'><i class='material-icons'>delete</i></button>"+
            "                </div>\n" +
            "                <div class=\"revision-question\">\n" +
            "                    <form>\n" +
            "                        <textarea class=\"form-control\" placeholder=\"Add Your Question here\" id=\"term"+keyValues[i].id+"\" onchange=\"editFlashCard("+keyValues[i].id+");\" maxlength='500'>"+keyValues[i].term+"</textarea>\n"+
            "                    </form>\n" +
            "                </div>\n" +
            "                <button class=\"add-text mb-2\">Back</button>\n" +
            "                <div class=\"revision-answer\">\n" +
            "                   <textarea class=\"form-control mb-2\" placeholder=\"\" value='"+keyValues[i].definition+"' id=\"definition"+keyValues[i].id+"\" onchange=\"editFlashCard("+keyValues[i].id+");\" maxlength='500'>"+keyValues[i].definition+"</textarea>\n" +
            "                </div>\n" +


            "            </div>\n" +
            "        </div>";
    }
    displaySet +="</div>"+
     "<div class=\"col-12 mt-3 submit-buttons non-ws text-center\">\n" +
        "        <a href=\"javascript:reviseNow(" + data.resId + ");\" class=\"btn btn-default cancel-revise\" style='border: 1px solid #EB5757;color: #EB5757;'>"+
        "            <span>Cancel</span>\n" +
        "        </a>\n" +
        "        <a href=\"javascript:addKeyValueCard();\" class=\"btn btn-default save-card\">\n" +
        "            <span>+ Add Card</span>\n" +
        "        </a>\n" +
        "    </div>"+
        "<div class='row col-12 col-lg-10 saveCard mt-4 justify-content-center'>"+
        "        <a href=\"javascript:done();\" class=\"btn btn-default saveSubmit\">\n" +
        "            <span>Save & Submit</span>\n" +
        "        </a>\n" +"</div>";
    $('.loading-icon').addClass('hidden');
    document.getElementById('htmlreadingcontent').innerHTML = displaySet;
   document.getElementById("revisionTitle").value=data.resourceName;
    if(data.canEdit=="true") {
        flashCardEmptyTemplate();
    }
}

    function done(){
        //if from chapter page, update the all list, depending upon whether it is new or not.
        if(document.getElementById("definition").value==""&&document.getElementById("term").value==""){
            backToSlider(studySetResId);
        }
        else{
             doneMode=true;
            addKeyValueCard();
        }
    }

//Edit Card
    function editFlashCard(keyValueId){
        var term =  encodeURIComponent(document.getElementById("term"+keyValueId).value);
        var definition = encodeURIComponent(document.getElementById("definition"+keyValueId).value);
        if((term && definition)!= '') {
            <g:remoteFunction controller="wonderpublish" action="updateFlashCard"
            params="'term='+term+'&definition='+definition+'&keyValueId='+keyValueId"></g:remoteFunction>
        }
        else{
            alert('Edited card should not be empty.');
        }
    }

//delete card

    function deleteCard(keyValueId) {
        $("#card"+keyValueId).remove();
        <g:remoteFunction controller="wonderpublish" action="deleteFlashCard" params="'keyValueId='+keyValueId"></g:remoteFunction>
    }

    function turnSlider() {
        $(".flash-card.active > .flip-box-inner").toggleClass("flipCard");
        $(".turn-slide > i").toggleClass("rotate-icon");
    }

    //Back from slider to card
function backSlider() {
    $('#slider-wrapper').hide();
    $('.main-wrapper,.submit-buttons,#allAddButton').show();
}
//Back to Main Page
function backToMain() {
    $('#content-data-all').show();
   document.getElementById('htmlreadingcontent').innerHTML='';
    $('#allAddButton').css('display','flex');
}
function backToSlider(resId,name) {
    reviseNow(resId,name);
}
    function addRevisionToAllTab(linkName,resId){
        var cStr = "<div class=\"container\">\n" +
            "<div class=\"all-container\">";
        cStr +="<div class=\"container-wrapper\">\n" +
            "<div class='d-flex justify-content-between align-items-center'>"+
            "<p class='quiz-number'></p>";
        cStr +="</div>";
        cStr += "        <div class=\"media\">\n" ;
        cStr +="            <i class=\"align-self-center material-icons yellow\">\n" +
            "                note\n" +
            "            </i>\n";
        cStr += "            <div class=\"media-body\">\n" +
            "                <span class=\"date mt-0\">Added by you now</span>\n" +
            "                <p class=\"title\">"+linkName+"</p>\n" +
            "                <div class=\"d-flex align-items-center justify-content-between\">\n" ;
        cStr += "                    <a class=\"mb-0 readnow\" href='javascript:getStudySets(" + resId + ")'>Revise</a>\n";


        cStr +=  "                </div>\n" +
            "            </div>\n" +
            "        </div>\n" +
            "    </div>\n";
        cStr +=     "        </div>\n" +
            "      </div>" ;
        document.getElementById('content-data-all').innerHTML += cStr;
    }

</script>

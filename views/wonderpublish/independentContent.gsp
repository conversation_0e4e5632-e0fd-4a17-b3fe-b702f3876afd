<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<asset:stylesheet href="imageoverlay.css"/>
<asset:stylesheet href="bootstrap-select.css"/>
<asset:stylesheet href="jquery.simple-dtpicker.css"/>
<link href="https://cdn.datatables.net/1.10.20/css/dataTables.bootstrap4.min.css" type="text/css" rel="stylesheet">

<script>
    var loggedIn=false;
</script>
<style>
.column_id{
    white-space:normal;
    width: 80px;
}
.column_resource_name{
    white-space:normal;
    width: 450px;
}
#resData {
    table-layout:fixed;
}
#resData td:nth-child(2) {
    max-width: 450px;
}
#resData td {
    white-space:inherit;
    word-wrap: break-word;
}
@media (max-width: 1400px) {
    #resData td:nth-child(2) {
        max-width: 350px;
    }
    .column_resource_name{
        width: 350px;
    }
    .column_id{
        width: 70px;
    }
}
.datepicker table {
    border-collapse: unset;
}
.datepicker .datepicker-days td, .datepicker .datepicker-days th {
    width: 25px !important;
    height: 25px !important;
}
@media screen and (max-width: 767px){
    div.dataTables_wrapper div.dataTables_paginate ul.pagination {
        margin: 10px 0 30px !important;
        justify-content: center !important;
    }
    table.dataTable.nowrap th, table.dataTable.nowrap td {
        white-space: normal !important;
    }
    table.dataTable>tbody>tr.child span.dtr-title {
        min-width: 100% !important;
    }
}

/*able.dataTable th, table.dataTable td {*/
    /*white-space: normal !important;*/
/*}*/


table.dataTable {
    width: 100% !important;
}
.form-group .btn-group {
    width: 100%; }

    .pagination li a {
        font-size: 14px;
    }
#resData .btn-outline-primary:focus, #resData .btn-outline-primary:active {
    background-color: #007bff !important;
    color: #FFF !important;
}
#resData .btn-outline-danger:focus, #resData .btn-outline-danger:active {
    background-color: #dc3545 !important;
    color: #FFF !important;
}

#content-books .form-group a {
    line-height: normal;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>


<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);" >
    <div class="modal createBook-modal" id="referenceVideoLinks" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">

                <!-- Modal Header -->
                <div class="modal-header">
                    <button type="button"  data-dismiss="modal" class="btn-close">close</button>
                    <h4 class="text-center">Reference Video Links</h4>
                </div>

                <!-- Modal body -->
                <div class="modal-body">
                    <div class="container">
                        <g:uploadForm name="resource5Form" url="[action:'addIndepentdentContentlink',controller:'resourceCreator']"  method="post">
                            <div class="row justify-content-center">
                                <div class="form-group col-12">
                                    <label>link</label>
                                    <g:textField id="videolink" class="form-control w-100" name="link"  placeholder=""  />
                                </div>
                                <div class="form-group col-12">
                                    <label>Title</label>
                                    <g:textArea id="videolinksresourceName" class="form-control w-100" name="resourceName"  placeholder="" />
                                </div>
                                <div class="form-group col-12">
                                    <label>Start Date</label>
                                    <g:textField id="videolinksresourceStartDate" class="form-control w-100" name="resourceStartDate"  placeholder="" autocomplete="off"/>
                                </div>
                                <div class="form-group col-12">
                                    <label>End Date</label>
                                    <g:textField id="videolinksresourceEndDate" class="form-control w-100" name="resourceEndDate"  placeholder="" autocomplete="off"/>
                                </div>

                                <div class="alert-thin alert alert-warning col-sm-12 text-left red mx-0" style="display: none;" id="videolinksUploadAlert">
                                    ** Enter  name.
                                </div>
                                <input type="hidden" name="resourceType" value="Reference Videos">
                                <input type="hidden" name="useType" value="notes">
                                <input type="hidden" name="quizMode">
                                <input type="hidden" name="indContentType">
                                <input type="hidden" name="page">
                            </div>
                            Video Player
                            <div id="videoPlayer">
                                <input type="radio" name="videoPlayer" value="youtube"  onchange="videoPlayerChanged(this)">&nbsp;Youtube
                            &nbsp;&nbsp;<input type="radio" name="videoPlayer" value="custom" checked onchange="videoPlayerChanged(this)">&nbsp;Custom player
                                <div id="allowCommentsSection" class="mt-2">
                                    <div>
                                        <p>Allow comments for live video&nbsp;&nbsp;<input type="checkbox" name="allowComments" checked></p>
                                        <p>Display comments after live video&nbsp;&nbsp;<input type="checkbox" name="displayComments"></p>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">  Download video links:</div>
                                    <div class="form-group col-12 d-flex mt-2">
                                        <label class="control-label mr-2">  360p: </label>
                                        <g:textField id="downloadlink1" class="form-control w-100" name="downloadlink1"/>
                                    </div>
                                    <div class="form-group col-12 d-flex">
                                        <label class="control-label mr-2">  540p: </label>
                                        <g:textField id="downloadlink2" class="form-control w-100" name="downloadlink2"/>
                                    </div>
                                    <div class="form-group col-12 d-flex">
                                        <label class="control-label mr-2">  720p: </label>
                                        <g:textField id="downloadlink3" class="form-control w-100" name="downloadlink3"/>
                                    </div>
                                </div>
                            </div>



                            <div class="col-12 mt-2 pl-0">
                                <button type="button" onclick="javascript:uploadVideoLinks()" class="btn btn-primary col-4">Add</button>
                            </div>

                        </g:uploadForm>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <div class="modal createBook-modal" id="readingMaterials" data-keyboard="false" data-backdrop="static">

        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">

                <!-- Modal Header -->
                <div class="modal-header">

                    <button type="button"  data-dismiss="modal" class="btn-close">close</button>
                    <h4>Reading Material</h4>
                </div>

                <!-- Modal body -->
                <div class="modal-body">
                    <g:uploadForm name="resource3Form" url="[action:'addIndependentContentFile',controller:'resourceCreator']"  method="post">
                        <div class="container">

                            <div class="row align-items-center">

                                <div class="form-group w-100">

                                    <label>Resource Name</label>
                                    <g:textField id="resourceName" class="form-control w-100" name="resourceName"  placeholder="Enter Resource Name" />
                                </div>
                            </div>
                            <div class="row align-items-center">
                                <div class="form-group w-100">
                                    <label>Upload File</label>

                                    <input id="file3" type="file" class="form-control w-100" name="file"  accept=".epub , .pdf" />
                                </div>
                            </div>
                            <div class="row">
                                <div>
                                    <i>Render as pdf only (not secure)</i>
                                    <div id="render_as_pdf">
                                        <input type="radio" name="convert" value="false" id="pdfyes" class="d-none">
                                        <label for="pdfyes" class="pdfyes">Yes</label>
                                        <input type="radio" name="convert" value="true" id="pdfno"  class="d-none" checked>
                                        <label for="pdfno" class="pdfno">No</label>


                                    </div>
                                    <div id="allow_pdf_download">

                                    </div>
                                    <div class="mt-2">
                                        <button type="button" onclick="javascript:uploadNotes()" class="btn btn-primary">Submit</button>
                                    </div>

                                    <div class="alert-thin alert alert-warning col-sm-12 text-left red" style="display: none;" id="notesUploadAlert">
                                        ** Enter a name for this reading material.
                                    </div>
                                    <div class="alert-thin alert alert-warning col-sm-12 text-left red" style="display: none;" id="UploadSizeAlert">
                                        ** File size exceeds 25mb.
                                    </div>
                                    <input type="hidden" name="resourceType" value="Notes">
                                    <input type="hidden" name="useType" value="notes">
                                    <input type="hidden" name="mode" value="create">
                                    <input type="hidden" name="page">
                                    <input type="hidden" name="quizMode">
                                    <input type="hidden" name="indContentType">
                                </div>
                            </div>
                        </div>
                    </g:uploadForm>
                </div>

            </div>
        </div>
    </div>
    <div class="modal createBook-modal" id="referenceWebLinks" data-keyboard="false" data-backdrop="static" >
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">

                <!-- Modal Header -->
                <div class="modal-header">

                    <button type="button"  data-dismiss="modal" class="btn-close">close</button>
                    <h4>Reference Web Links</h4>
                </div>

                <!-- Modal body -->
                <div class="modal-body">
                    <div class="container">

                        <g:uploadForm name="resource4Form" url="[action:'addIndepentdentContentlink',controller:'resourceCreator']"  method="post">
                            <div class="row">
                                                       <div class="form-group col-12">
                                                           <label>Web link</label>
                            <g:textField id="weblink" class="form-control w-100" name="link"  placeholder="Web link"  />
                            </div>
                            <div class="form-group col-12">
                                <label>Resource Name</label>
                                <g:textField id="linksresourceName" class="form-control w-100" name="resourceName"  placeholder="Name" />
                            </div>
                            <div class="buk3 pl-3 mt-2 col-12">
                                <button type="button" onclick="javascript:uploadLinks()" class="btn btn-primary col-4">Add</button>
                            </div>


                            <div class="alert-thin alert alert-warning col-sm-12 text-left red" style="display: none;" id="linksUploadAlert">
                                ** Enter both link and the name.
                            </div>
                            <input type="hidden" name="resourceType" value="Reference Web Links">
                            <input type="hidden" name="page">
                            <input type="hidden" name="quizMode">
                            <input type="hidden" name="indContentType">

                        </g:uploadForm>
                    </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <div class="modal createBook-modal" id="editVideoLinks" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">

                <!-- Modal Header -->
                <div class="modal-header">
                    <button type="button"  data-dismiss="modal" class="btn-close">close</button>
                    <h4 class="modal-title">Reference Video Links</h4>
                </div>

                <!-- Modal body -->
                <div class="modal-body">

                <div class="container">
                        <div class="row justify-content-center">
                            <div class="form-group col-12">
                                <label>link</label>
                                <input class="form-control w-100" type='text' name='resname' id='resname'>
                            </div>
                            <div class="form-group col-12">
                                <label>Title</label>
                                <textarea class="form-control w-100" name='restitle' id='restitle'></textarea>
                            </div>
                            <div class="form-group col-12">
                                <label>Start Date</label>
                                <input class="form-control w-100" type='text' name='modalTestStartDate' id='modalTestStartDate' autocomplete="off">
                            </div>
                            <div class="form-group col-12">
                                <label>End Date</label>
                                <input class="form-control w-100" type='text' name='modalTestEndDate' id='modalTestEndDate' autocomplete="off">
                            </div>

                            <div class="alert-thin alert alert-warning col-sm-12 text-left red" style="display: none;" id="modalvideolinksUploadAlert">
                                ** Enter  name.
                            </div>
                            <input type="hidden" name="resourceType" value="Reference Videos">
                            <input type="hidden" name="useType" value="notes">
                            <input type="hidden" name='number' id='resid'>
                            <input type="hidden" name="indContentType" id='inContentType'>
                            <input type="hidden" name="page" id="resType">
                        </div>
                        Video Player
                        <div id="modalVideoPlayer">
                            <input type="radio" name="videoPlayer" value="youtube"  id="modalYoutube" onchange="videoPlayerChangedModel(this)">&nbsp;Youtube
                        &nbsp;&nbsp;<input type="radio" name="videoPlayer" value="custom" id="modalCustom" onchange="videoPlayerChangedModel(this)">&nbsp;Custom player
                            <div id="modalCommentSection" class="mt-2">
                                <div>
                                    <p>Allow comments for live video&nbsp;&nbsp;<input type="checkbox" name="allowComments" id="modalAllowComments"></p>
                                    <p>Display comments after live video&nbsp;&nbsp;<input type="checkbox" name="displayComments" id="modalDisplayComments"></p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">  Download video links:</div>
                                <div class="form-group col-12 d-flex mt-2">
                                    <label class="control-label mr-2">  360p: </label>
                                    <input class="form-control w-100" type='text' name='downloadlink1' id='modaldownloadlink1'>
                                </div>
                                <div class="form-group col-12 d-flex">
                                    <label class="control-label mr-2">  540p: </label>
                                    <input class="form-control w-100" type='text' name='downloadlink2' id='modaldownloadlink2'>
                                </div>
                                <div class="form-group col-12 d-flex">
                                    <label class="control-label mr-2">  720p: </label>
                                    <input class="form-control w-100" type='text' name='downloadlink3' id='modaldownloadlink3'>
                                </div>
                            </div>
                        </div>

                        <div class="col-12 mt-2 pl-0">
                            <button type="button" onclick="javascript:updateVideoLink()" class="btn btn-primary col-4">Update</button>
                        </div>

                    </div>

                </div>
            </div>
        </div>
    </div>

    <div class="modal createBook-modal" id="editWebLinks" data-keyboard="false" data-backdrop="static" >
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">

                <!-- Modal Header -->
                <div class="modal-header">

                    <button type="button"  data-dismiss="modal" class="btn-close">close</button>
                    <h4>Reference Web Links</h4>
                </div>

                <!-- Modal body -->
                <div class="modal-body">
                    <div class="container">

                            <div class="row">
                                <div class="form-group col-12">
                                    <label>Web link</label>
                                <input class="form-control w-100" type='text' name='link' id='weblinkurledit'>
                                </div>
                                <div class="form-group col-12">
                                    <label>Resource Name</label>
                                <input class="form-control w-100" type='text' name='resourceName' id='weblinktitleedit'>
                                </div>
                                <div class="buk3 pl-3 mt-2 col-12">
                                    <button type="button" onclick="javascript:updateLink()" class="btn btn-primary col-4">Update</button>
                                </div>

                                <div class="alert-thin alert alert-warning col-sm-12 text-left red mx-0" style="display: none;" id="modalLinksUploadAlert">
                                    ** Enter both link and the name.
                                </div>
                                <input type="hidden" name="resourceType" value="Reference Web Links">
                                <input type="hidden" name="page">
                                <input type="hidden" name="quizMode">
                                <input type="hidden" name="indContentType">
                                <input type="hidden" name='number' id='weblinkresid'>

                    </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <div class='row' >
        <div class='col-md-9 main p-4' style=" margin: 40px auto; float: none;">
            <div id="content-books">
                <div class="form-inline align-items-end">
                    <div class="form-group col-md-12 mt-4">
                        <label for="content" style="display: block; margin-right: 15px;">Content Type</label>
                        <select name="content" id="content" class="form-control col-4 mr-3" style="">
                            <option value="">Select</option>
                            <option value="currentaffairs">Current affairs</option>
                            <option value="quiz">Quiz of the day</option>
                            <option value="jobalerts">Job Alerts</option>
                            <option value="indnotes">Notes</option>
                        </select>
                        <button onclick="getResDetails()" class="btn btn-lg btn-primary col-2">Get Details</button>
                    </div>
                      <div class="form-group mt-3" id="download1" style="display:none;">
                          <a href="javascript:" id="download-btn1" class="border-dark p-0">Add Reading Material</a>
                      </div>
                      <div class="form-group mt-3" id="pdfupload" style="display:none;">
                          <a href="" data-toggle="modal" data-target="#readingMaterials" class="border-dark border-left p-0 pl-4 ml-4">Add pdf</a>
                      </div>
                      <div class="form-group mt-3" id="videoupload" style="display:none;">
                          <a href="" data-toggle="modal" data-target="#referenceVideoLinks" class="border-dark border-left p-0 pl-4 ml-4">Add Reference Video link</a>
                      </div>
                      <div class="form-group mt-3" id="weblinkupload" style="display:none;">
                          <a href="" data-toggle="modal" data-target="#referenceWebLinks" class="border-dark border-left p-0 pl-4 ml-4">Reference Web link</a>
                      </div>
                </div>
                <div class="form-group col-md-12 mt-4">
                    <table id="resData" class="table table-striped table-bordered dt-reponsive" style="display: none;">
                        <thead>
                        <tr class="bg-primary text-white">
                            <th style="text-align: center">Id</th>
                            <th style="text-align: center">Name</th>
                            <th style="text-align: center">Resource Type</th>
                            <th style="text-align: center">Action</th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div style="margin-top: 10px;">
                    <div id="errormsg" class="alert alert-danger has-error" role="alert" style="display: none; background: none;"></div>
                    <div id="successmsg" style="display: none"></div>
                    <div id="batchUsers" style="display: none"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/footer"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<%}%>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="clock.js"/>
<asset:javascript src="moment.min.js"/>
<asset:javascript src="jquery.simple-dtpicker.js"/>
%{--<asset:javascript src="jquery.simple-dtpicker.js"/>--}%
%{--<asset:stylesheet href="jquery.simple-dtpicker.css"/>--}%
<script src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.20/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>



<script>
    var res;
    $('#resData').hide();
    function getResDetails() {
        $('#download1').show();
        if ($.fn.dataTable.isDataTable('#resData')) {
            $('#resData').DataTable().destroy();
        }
        if (document.getElementById("content")[document.getElementById("content").selectedIndex].value==""){
            document.getElementById("errormsg").innerHTML="Please select Content Type";
            $("#errormsg").show();
            $("#batchUsers").hide();
            $('#download1').hide();
        } else {
            var content = document.getElementById("content")[document.getElementById("content").selectedIndex].value;
            if (content=="currentaffairs" || content=="jobalerts") {
                $("#download1").show();
                $("#weblinkupload").show();
                $("#pdfupload").show();
                $("#videoupload").show();
            }
            if(content=="quiz"){
                document.getElementById("download-btn1").innerHTML = "Add Quiz";
            } else {
                document.getElementById("download-btn1").innerHTML = "Add Reading Material";
            }
            $("#errormsg").hide();
            $('#resData').show();
            $('#resData').DataTable({
                //'responsive': true,
                "scrollX": true,
                'destroy': true,
                //'processing': true,
                'serverSide': true,
                'ordering': false,
                'retrieve': true,
                "lengthChange": false,
                'ajax': {
                    'url': '/pubdesk/getindependentResourcDetails',
                    'type': 'GET',
                    'data': function (outData) {
                        outData.siteId = 21;
                        outData.pageType = content;
                        return outData;
                    },
                    dataFilter: function (inData) {
                        res = JSON.parse(inData);
                        var bookArr = JSON.parse(res.data);
                        res.data=bookArr;
                        return JSON.stringify(res);
                    },
                    error: function (err, status) {
                        console.log(err);
                    },
                },
                'columns': [

                    {
                        'data': 'id',
                        'render': function (data, type, row) {
                            if(row.id != null) {
                                return "<div class='column_id'>" + row.id + "</div>";
                            }
                        }

                    },
                    {
                        'data': 'resourceName',
                        'render': function (data, type, row) {
                            if(row.id != null) {
                                if (row.resourceName!="" && row.resourceName!=null) {
                                    return "<div class='column_resource_name'>" + row.resourceName + "</div>";
                                }
                            }
                        }
                    },
                    {
                        'data': 'indContentType',
                        'render': function (data, type, row) {
                            if (row.id != null) {
                                if (row.indContentType!="" && row.indContentType!=null) {
                                    return row.indContentType;
                                }else{
                                    var content = document.getElementById("content")[document.getElementById("content").selectedIndex].value;
                                    if(content=="quiz") {
                                        return "quiz";
                                    }else{
                                        return "Reading Material";
                                    }
                                }
                            }
                        }
                    },
                    {
                        'data': 'id',
                        'searchable': 'false',
                        'render': function (data, type, row) {
                            if(row.id != null) {
                                if(row.indContentType=="weblink"){
                                    return "<input type=\"button\" class='btn btn-sm btn-outline-primary mr-2' onclick='editWeblink(\"" + row.id + "\",\"" + row.resourceName.replace(/'/g,"&#39;") + "\",\"" + row.resLink + "\");' value='Edit'>" +
                                        "<input type=\"button\" class='btn btn-sm btn-outline-danger' onclick='deleteHtml(" + row.id + ");' value='Delete'>";
                                }else if(row.indContentType=="video"){
                                    return "<input type='button' class='btn btn-sm btn-outline-primary mr-2' onclick='editVideo(\"" + row.resLink + "\",\"" + row.id + "\",\"" + row.resourceName.replace(/'/g,"&#39;") + "\",\"" + row.resType + "\",\"" + row.indContentType +"\",\"" + row.videoPlayer + "\",\"" + row.allowComments + "\",\"" + row.displayComments + "\",\"" + row.testStartDate.replace(/=/g, ':') + "\",\"" + row.testEndDate.replace(/=/g, ':') + "\",\"" + row.downloadlink1 + "\",\"" + row.downloadlink2 + "\",\"" + row.downloadlink3 + "\");' value='Edit'>" +
                                        "<input type='button' class='btn btn-sm btn-outline-danger' onclick='deleteHtml(" + row.id + ");' value='Delete'>";
                                }else if(row.indContentType=="pdf"){
                                    return  "<input type=\"button\" class='btn btn-sm btn-outline-danger' onclick='deleteHtml(" + row.id + ");' value='Delete'>";
                                }else
                                {
                                    return "<input type=\"button\" class='btn btn-sm btn-outline-primary mr-2' onclick='createHTMLedit(" + row.id + ");' value='Edit'>" +
                                        "<input type=\"button\" class='btn btn-sm btn-outline-danger' onclick='deleteHtml(" + row.id + ");' value='Delete'>";
                                }
                            }
                        }
                    },
                ],

            });

        }

    }

    $('input[name=convert]').change(function (event) {
        if(event.target.value == "false" || event.target.value == false){
            var str ="<i>Allow download (not secure)</i>\n" +
                "                                <div id=\"\">\n" +
                "                                    <input type=\"radio\" name=\"pdf_download\" value=\"yes\" id=\"pdf_download_yes\" class=\"d-none\">\n" +
                "                                    <label for=\"pdf_download_yes\" class=\"pdf_download_yes pdfyes\">Yes</label>\n" +
                "                                    <input type=\"radio\" name=\"pdf_download\" value=\"no\" id=\"pdf_download_no\"  class=\"d-none\" checked>\n" +
                "                                    <label for=\"pdf_download_no\" class=\"pdf_download_no pdfno\">No</label>\n" +
                "\n" +
                "\n" +
                "                                </div>"
            $("#allow_pdf_download").html(str);
        }else {
            $("#allow_pdf_download").html("");
        }
    })

    function videoPlayerChanged(field){
        if("custom"==field.value){
            $("#allowCommentsSection").show();
        }else{
            $("#allowCommentsSection").hide();
        }
    }

    function videoPlayerChangedModel(field){
        if("custom"==field.value){
            $("#modalCommentSection").css('display', 'flex');
        }else{
            $("#modalCommentSection").hide();
        }
    }


    var modelresLink = '';
    var modelresName= '';
    var modelresId= '';
    var modalallowComments = '';
    var modaldisplayComments = '';
    var modalvideoPlayer='';
    var modalStartDate = '';
    var modalEndDate = '';
    var modalresType = '';
    var modalinContentType = '';
    var downloadlinkone = '';
    var downloadlinktwo = '';
    var downloadlinkthree = '';
    var modelweblinktitle = '';
    var modelweblinkurl = '';
    var modelweblinkresId='';
    function editVideo(videoLink,id,title,resType,contentType,videoPlayer,allowComments,displayComments,testStartDate,testEndDate,downloadLink1,downloadLink2,downloadLink3) {
        if(testStartDate == '' || testStartDate == 'Invalid date') modalStartDate = '';
        else modalStartDate = testStartDate.replace(/#/g,":").replace(/null/g," ");
        if(testEndDate == '' || testEndDate == 'Invalid date') modalEndDate = '';
        else modalEndDate = testEndDate.replace(/#/g,":").replace(/null/g," ");
        modelresLink = videoLink;
        modelresName = title.replace(/'/g,"&#39;");
        modelresId = id;
        modalvideoPlayer=videoPlayer;
        modalCustom=videoPlayer;
        modalallowComments=allowComments;
        modaldisplayComments=displayComments;
        modalresType=resType;
        modalinContentType=contentType;
        downloadlinkone = downloadLink1.replace(/#/g,":").replace(/null/g," ");
        downloadlinktwo = downloadLink2.replace(/#/g,":").replace(/null/g," ");
        downloadlinkthree = downloadLink3.replace(/#/g,":").replace(/null/g," ");
        $("#modalCommentSection").hide();
        $("#editVideoLinks").modal('show');
    }

    $("#editVideoLinks").on('shown.bs.modal', function(){
        $("#restitle").val(modelresName);
        if(modelresLink.includes("/")){
            $("#resname").val(modelresLink.replace(/#/g,":"));
        }else{
            $("#resname").val("https://www.youtube.com/watch?v="+modelresLink);
        }

        $("#resid").val(modelresId);
        $('#modalTestStartDate').val(modalStartDate);
        $('#modalTestEndDate').val(modalEndDate);
        $('#resType').val(modalresType);
        $('#inContentType').val(modalinContentType);
        $('#modaldownloadlink1').val(downloadlinkone);
        $('#modaldownloadlink2').val(downloadlinktwo);
        $('#modaldownloadlink3').val(downloadlinkthree);

        if(modalvideoPlayer=="custom"){
            if(modalCustom=="custom") document.getElementById("modalCustom").checked=true;
            if(modalallowComments=="on") document.getElementById("modalAllowComments").checked=true;
            if(modaldisplayComments=="on") document.getElementById("modalDisplayComments").checked=true;
            $("#modalCommentSection").show();
        }else {
            if(modalallowComments=="on") document.getElementById("modalAllowComments").checked=true;
            if(modaldisplayComments=="on") document.getElementById("modalDisplayComments").checked=true;
            document.getElementById("modalYoutube").checked=true;
        }
    });

    function updateVideoLink(){
        var content = document.getElementById("content")[document.getElementById("content").selectedIndex].value;
        if(document.getElementById("restitle").value==""||document.getElementById("resname").value==""){
            alert("Enter  link and title.");
        }else {
            var urlCheck = document.getElementById("resname").value;
            if (urlCheck.includes("youtube") || urlCheck.includes("youtu.be")) {
                document.getElementById("resname").value = getIdFromYouTubeURL(document.getElementById("resname").value);
            }
            var modelresName = $("#restitle").val();
            modelresName = encodeURIComponent(modelresName);
            var modelresId = $("#resid").val();
            var modelresLink = $("#resname").val();
            modelresLink = modelresLink.replace(/&/g, "~");
            var modalTestStartDate = $("#modalTestStartDate").val();
            var modalTestEndDate = $("#modalTestEndDate").val();
            var modalAllowComments = null;
            var modalDisplayComments = null;
            var modalYoutube = null;
            var modalCustom = null;
            var videoPlayer = "";
            var downloadlink1 = $("#modaldownloadlink1").val();
            var downloadlink2 = $("#modaldownloadlink2").val();
            var downloadlink3 = $("#modaldownloadlink3").val();
            if (document.getElementById("modalYoutube").checked) {
                videoPlayer = "youtube";
            } else {
                if (document.getElementById("modalCustom").checked)
                    videoPlayer = "custom";
            }

            downloadlink1 = downloadlink1.replace(/&/g, "~");
            downloadlink2 = downloadlink2.replace(/&/g, "~");
            downloadlink3 = downloadlink3.replace(/&/g, "~");
            var quizMode=content;
            var page=content;
            var indContentType="video";
            if (document.getElementById("modalAllowComments").checked) modalAllowComments = "on";
            if (document.getElementById("modalDisplayComments").checked) modalDisplayComments = "on";
                        <g:remoteFunction controller="Wonderpublish" action="updateResourceData"  params="'resId='+modelresId+'&reslink='+modelresLink+'&restitle='+modelresName+'&allowComments='+modalAllowComments+'&displayComments='+modalDisplayComments+
                        '&testStartDate='+modalTestStartDate+'&testEndDate='+modalTestEndDate+'&downloadlink1='+downloadlink1+'&downloadlink2='+downloadlink2+'&downloadlink3='+downloadlink3+'&videoPlayer='+videoPlayer+'&quizMode='+quizMode+'&page='+page+'&indContentType='+indContentType"
                   onSuccess = "alert('Updated Successfully');window.location.reload();"/>
            $('.loading-icon').removeClass('hidden');
            $('#editVideoLinks').hide();

        }
    }

    function editWeblink(id,title,link){
        modelweblinkresId = id;
        modelweblinktitle=title;
        modelweblinkurl=link.replace(/#/g,":").replace(/null/g," ");;

        $("#editWebLinks").modal('show');

    }

    $("#editWebLinks").on('shown.bs.modal', function(){
        $('#weblinktitleedit').val(modelweblinktitle);
        $('#weblinkurledit').val(modelweblinkurl);
        $('#weblinkresid').val(modelweblinkresId);
    });

    function updateLink(){
        var content = document.getElementById("content")[document.getElementById("content").selectedIndex].value;
        if(document.getElementById("weblinktitleedit").value==""||document.getElementById("weblinkurledit").value==""){
            alert("Enter link and the name.");
        } else {
            var modelresId = $("#weblinkresid").val();
            var weblinktitleedit = encodeURIComponent($("#weblinktitleedit").val());
            var weblinkurledit = $("#weblinkurledit").val();
            var quizMode=content;
            var page=content;
            var indContentType="weblink";
            <g:remoteFunction controller="Wonderpublish" action="updateResourceData"  params="'resId='+modelresId+'&restitle='+weblinktitleedit+'&reslink='+weblinkurledit+'&quizMode='+quizMode+'&page='+page+'&indContentType='+indContentType"
        onSuccess = "alert('Updated Successfully');window.location.reload();"/>
            $('.loading-icon').removeClass('hidden');
            $('#editWebLinks').hide();
        }
    }

    $('#startDate, #endDate').datepicker({
        format: 'dd-mm-yyyy',
        //startView: 1,
        //todayBtn: "linked",
        //clearBtn: true,
        autoclose: true,
        todayHighlight: true,
        orientation: "bottom auto",
        endDate: '+0d'
    });


    $('#download-btn1').on('click', function() {
        var content = document.getElementById("content")[document.getElementById("content").selectedIndex].value;
       if(content==""){
           document.getElementById("errormsg").innerHTML="Please select Content Type";
           $("#errormsg").show();
           $("#batchUsers").hide();
       }else{
           if(content=="quiz") {
               window.location = "/wonderpublish/quizcreator?resourceType=Multiple Choice Questions&useType=quiz&mode=create&page=" + content;
           }else{
               window.location.href = "/wonderpublish/notescreator?resourceType=Notes&useType=notes&mode=create&page=" + content;
           }

       }

    });


    function deleteHtml(id) {
        var content = document.getElementById("content")[document.getElementById("content").selectedIndex].value;
        <g:remoteFunction controller="wonderpublish" action="deleteIndependentResId"  params="'id='+id+'&page='+content" onSuccess = "bookDeleted(data);"/>
    }

    function bookDeleted(data) {
        if(data.status=="OK"){
            alert("Deleted successfully");
            location.reload();
        }

    }

    function createHTMLedit(id){
        var id =id;
        var content = document.getElementById("content")[document.getElementById("content").selectedIndex].value;
        if(content=="quiz") {
            window.location = "/wonderpublish/quizcreator?resourceType=Multiple Choice Questions&useType=quiz&mode=edit&page=" + content + "&id=" + id;
        }else{
            window.location = "/wonderpublish/notescreator?resourceType=Notes&useType=notes&mode=edit&page=" + content + "&id=" + id;
        }
    }
    function uploadVideoLinks(){
        $("#videolinksUploadAlert").hide(500);
        if(document.getElementById("videolinksresourceName").value==""){
            $("#videolinksUploadAlert").show(500);
        } else {
            var restitlename =document.getElementById("videolinksresourceName").value;
            document.resource5Form.videolinksresourceName.value = encodeURIComponent(restitlename);
            if(document.getElementById("videolink").value==""){
                document.getElementById("videolink").value="blank";
            }
            var urlCheck =document.getElementById("videolink").value;
            if(urlCheck.includes("youtube")||urlCheck.includes("youtu.be")) {
                document.getElementById("videolink").value = getIdFromYouTubeURL(document.getElementById("videolink").value);
            }
            $("#booksaving").show(500);
            var selectedValue=document.getElementById("content")[document.getElementById("content").selectedIndex].value;
            document.resource5Form.quizMode.value=selectedValue;
            document.resource5Form.page.value=selectedValue;
            document.resource5Form.indContentType.value="video";
            document.resource5Form.submit();
            $('.loading-icon').removeClass('hidden');
            $('#referenceVideoLinks').hide();
        }
    }

    function uploadLinks(){
        $("#linksUploadAlert").hide(500);

        if(document.getElementById("linksresourceName").value==""||document.getElementById("weblink").value==""){
            $("#linksUploadAlert").show(500);
        } else {
            $("#booksaving").show(500);
            $('.loading-icon').removeClass('hidden');
            $('#referenceWebLinks').hide();
            var selectedValue=document.getElementById("content")[document.getElementById("content").selectedIndex].value;
            document.resource4Form.quizMode.value=selectedValue;
            document.resource4Form.page.value=selectedValue;
            document.resource4Form.indContentType.value="weblink";
            document.resource4Form.submit();
        }
    }


    function getIdFromYouTubeURL(url) {
        if(url.match(/(youtu.be)/)){
            var split_c = "/";
            var split_n = 3;
        }

        if(url.match(/(youtube.com)/)){
            var split_c = "v=";
            var split_n = 1;
        }

        var getYouTubeVideoID = url.split(split_c)[split_n];
        return getYouTubeVideoID.replace(/(&)+(.*)/, "");
    }
    $('*[name=resourceStartDate]').appendDtpicker({
        "futureOnly": true,
        "autodateOnStart": false,
        "minuteInterval": 5,
        // "dateOnly":true
    });
    $('*[name=resourceEndDate]').appendDtpicker({
        "futureOnly": true,
        "autodateOnStart": false,
        "minuteInterval": 5,
        // "dateOnly":true
    });
    $('*[name=modalTestStartDate]').appendDtpicker({
        "futureOnly": true,
        "autodateOnStart": false,
        "minuteInterval": 5,
        // "dateOnly":true
    });

    $('*[name=modalTestEndDate]').appendDtpicker({
        "futureOnly": true,
        "autodateOnStart": false,
        "minuteInterval": 5,
        // "dateOnly":true
    });
    $("#videolink").blur(function() {
        var urlCheck =document.getElementById("videolink").value;
        if(urlCheck.includes("youtube")||urlCheck.includes("youtu.be")) {
            var apiKeys = '${apiKey}';
            var apiKey;
            if(apiKeys.indexOf(",")>0){
                var apiKeysArray = apiKeys.split(',');
                var randomIndex =  Math.floor(Math.random() * 5);
                apiKey = apiKeysArray[randomIndex];
            }else{
                apiKey = apiKeys;
            }
            var videoId = getIdFromYouTubeURL(document.getElementById("videolink").value);
            var gUrl = "https://www.googleapis.com/youtube/v3/videos?id=" + videoId + "&key=" + apiKey + "&part=snippet";
            console.log("gUrl===="+gUrl)
            $.get(gUrl, function (data) {
                var youtubetitle = data.items[0].snippet.title;
                youtubetitle = youtubetitle.replace(/'/g,"&#39;")
                document.getElementById("videolinksresourceName").value = youtubetitle;

            });
        }


    });

    $("#resname").blur(function() {
        var urlCheck =document.getElementById("resname").value;
        if(urlCheck.includes("youtube")||urlCheck.includes("youtu.be")) {
            var apiKeys = '${apiKey}';
            var apiKey;
            if(apiKeys.indexOf(",")>0){
                var apiKeysArray = apiKeys.split(',');
                var randomIndex =  Math.floor(Math.random() * 5);
                apiKey = apiKeysArray[randomIndex];
            }else{
                apiKey = apiKeys;
            }
            var videoId = getIdFromYouTubeURL(document.getElementById("resname").value);
            var gUrl = "https://www.googleapis.com/youtube/v3/videos?id=" + videoId + "&key=" + apiKey + "&part=snippet";
            console.log("gUrl===="+gUrl)
            $.get(gUrl, function (data) {
                var youtubetitle = data.items[0].snippet.title;
                youtubetitle = youtubetitle.replace(/'/g,"&#39;")
                document.getElementById("restitle").value = youtubetitle;

            });
        }


    });


    var validFileSize = true;
    $('#file3').change(function(event) {
        var _size = this.files[0].size;
        if(_size >= 25000000){
            validFileSize = false;
        }else{
            validFileSize = true;
        }
    });

    function uploadNotes(){
        $("#notesUploadAlert").hide(500);
        $("#UploadSizeAlert").hide(500);
        if(document.resource3Form.resourceName.value==""){
            $("#notesUploadAlert").show(500);
        }else if(!validFileSize) $("#UploadSizeAlert").show(500);
        else {
            $("#booksaving").show(500);
            $('.loading-icon').removeClass('hidden');
            $('#readingMaterials').hide();
            var selectedValue=document.getElementById("content")[document.getElementById("content").selectedIndex].value;
            document.resource3Form.quizMode.value=selectedValue;
            document.resource3Form.page.value=selectedValue;
            document.resource3Form.indContentType.value="pdf";
            document.resource3Form.submit();

        }
    }

    $(function() {
        $('#content').change(function(){
            $('#resData_wrapper').hide();
            var selectedValue=document.getElementById("content")[document.getElementById("content").selectedIndex].value;
            if(selectedValue=="currentaffairs" || selectedValue=="jobalerts"){
                $("#download1").show();
                $("#weblinkupload").show();
                $("#pdfupload").show();
                $("#videoupload").show();
            }else{
                $("#download1").show();
                $("#weblinkupload").hide();
                $("#pdfupload").hide();
                $("#videoupload").hide();
            }
            if(selectedValue=="quiz"){
                document.getElementById("download-btn1").innerHTML = "Add Quiz";
            } else {
                document.getElementById("download-btn1").innerHTML = "Add Reading Material";
            }
        });
    });
</script>
</body>
</html>

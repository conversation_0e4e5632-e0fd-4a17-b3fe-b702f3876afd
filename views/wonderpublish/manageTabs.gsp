<g:render template="/wonderpublish/loginChecker"></g:render>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>

<section class="library">
    <div class="container managetabs">
        <h3 class="mb-3">Manage Tabs</h3>
        <div class="col-md-12 form-inline row">
            <g:select id="createLevel" class="form-control col-md-4" name="createLevel" from="${levelsMst}" optionKey="name" optionValue="name"
                      noSelection="['':'Select']" onchange="javascript:getCreateSyllabus(this.value)"/>
            <select id="createSyllabus" class="form-control col-md-4 ml-3" name="createSyllabus" style="display: none" onchange="javascript:getSyllabusDetails(this.value)"></select>
            <a id="addButton" class="btn btn-primary col-md-2 m-0 ml-3 form-control disabled" data-toggle="collapse" href="#expandForm" role="button" aria-expanded="false" aria-controls="expandForm">
                Add New
            </a>
        </div>

        <div class="adding_area col-md-12 my-4 p-0">
            <div class="collapse" id="expandForm">
                <div class="card card-body">
                    <div id="addGroup" class="form-inline row mt-3" style="display: none">
                        <h5 id="addTitleText" class="ml-3 mb-3">Add New Details</h5>
                        <div class="col-md-12 form-group">
                            <input type="text" class="form-control col-md-6" name="newsyllabus" id="newsyllabus" placeholder="Syllabus or Exam group">
                            <div id="degreeTypeSection" class="col-md-3 pr-0 ml-3" style="display: none">
                                <select class="form-control w-100" name="degreeType" id="degreeType">
                                    <option value="Bachelor">Bachelor</option>
                                    <option value="Master">Master</option>
                                    <option value="Doctorate">Doctorate</option>
                                </select>
                            </div>
                            <a class="add col-md-1 btn btn-primary form-control m-0 ml-3" href="javascript:addNewSyllabus();" id="newSyllabusLabel">Add</a>
                            <span class="alert p-0 mb-4" id="syllabusError"></span>
                        </div>

                        <div class="col-md-12 form-group">
                            <input type="text" class="form-control col-md-6" name="newsubject" id="newsubject" placeholder="Subject">
                            <a class="add col-md-1 btn btn-primary form-control m-0 ml-3" href="javascript:addNewSubject();">Add</a>
                            <span class="alert p-0" id="subjectError"></span>
                        </div>

                        <div id="addExamGroup" class="col-md-12 form-group" style="display: none">
                            <input type="text" class="form-control col-md-6"  name="newExam" id="newExam" placeholder="Grade / Exam">
                            <div id="displayState" class="col-md-3 pr-0" style="display: none">
                                <select  id="newState" class="form-control w-100" required>
                                    <option value="">State</option>
                                    <option value="Andaman and Nicobar Islands">Andaman and Nicobar Islands</option>
                                    <option value="Andhra Pradesh">Andhra Pradesh</option>
                                    <option value="Arunachal Pradesh">Arunachal Pradesh</option>
                                    <option value="Assam">Assam</option>
                                    <option value="Bihar">Bihar</option>
                                    <option value="Chandigarh">Chandigarh</option>
                                    <option value="Chhattisgarh">Chhattisgarh</option>
                                    <option value="Dadra and Nagar Haveli">Dadra and Nagar Haveli</option>
                                    <option value="Goa">Goa</option>
                                    <option value="Gujarat">Gujarat</option>
                                    <option value="Haryana">Haryana</option>
                                    <option value="Himachal">Himachal Pradesh</option>
                                    <option value="Jammu & Kashmir">Jammu & Kashmir</option>
                                    <option value="Jharkhand">Jharkhand</option>
                                    <option value="Karnataka">Karnataka</option>
                                    <option value="Kerala">Kerala</option>
                                    <option value="Ladakh">Ladakh</option>
                                    <option value="Lakshadweep">Lakshadweep</option>
                                    <option value="Madhya Pradesh">Madhya Pradesh</option>
                                    <option value="Maharashtra">Maharashtra</option>
                                    <option value="Manipur">Manipur</option>
                                    <option value="Meghalaya">Meghalaya</option>
                                    <option value="Mizoram">Mizoram</option>
                                    <option value="Nagaland">Nagaland</option>
                                    <option value="Odisha">Odisha</option>
                                    <option value="Puducherry">Puducherry</option>
                                    <option value="Punjab">Punjab</option>
                                    <option value="Rajasthan">Rajasthan</option>
                                    <option value="Sikkim">Sikkim</option>
                                    <option value="Tamil Nadu">Tamil Nadu</option>
                                    <option value="Telangana">Telangana</option>
                                    <option value="The Government of NCT of Delhi">The Government of NCT of Delhi</option>
                                    <option value="Tripura">Tripura</option>
                                    <option value="Uttarakhand">Uttarakhand</option>
                                    <option value="Uttar Pradesh">Uttar Pradesh</option>
                                    <option value="West Bengal">West Bengal</option>
                                </select>
                            </div>
                            <a class="add col-md-1 btn btn-primary form-control m-0 ml-3" href="javascript:addNewExam();">Add</a>
                            <div class="alert p-0" id="examError"></div>
                        </div>

                        <div class="col-md-2 orange" id="booksaving" style="display: none">
                            Saving...<i class="fa fa-spinner fa-3x fa-spin"></i>
                        </div>
                    </div>

                </div>
            </div>
        </div>

        <div class="row col-md-12 m-0 p-0 pb-5" id="subjectsandgrades" style="display:none;">
            <div class="col-md-5 p-0" id="subjects"></div>
            <div class="col-md-6 offset-md-1 p-0" id="grades"></div>
        </div>

    </div>

    <div class="container addNewArrivals">
        <h3>Add New Arrivals</h3>
        <div class="d-flex col-md-12 form-inline row">
            <input type="text" class="form-control col-md-4" id="naBookIds" placeholder="Enter Book IDs Ex. 12234,1235">
            <button class="btn btn-primary col-md-2 m-0 ml-3 form-control" id="addNAbtn">Add books</button>
            <br>
        </div>
        <p class="text-danger" id="invalidBookIds" style="display:none;"></p>
    </div>
</section>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/footer"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<%}%>

<script>

    $(document).ready(function(){
        $('#createLevel').on('change', function(){
            var level = $(this).val();
            console.log(level);
            if(level=='') {
                $('#addButton').addClass('disabled');
                $('#subjectsandgrades').hide();
                $('#expandForm').removeClass('show');
            } else {
                $('#addButton').removeClass('disabled');
            }
        });
    });

    function htmlDecode( html ) {
        var a = document.createElement( 'a' ); a.innerHTML = html;
        return a.textContent;
    };
    
    function getCreateSyllabus(level){
            $('#createSyllabus').hide();
            $('#createGrade').hide();
            $('#createSubject').hide();
            $("#addGroup").hide();
            $("#addExamGroup").hide();
            $("#displayState").hide();
            if(level) {
                $("#booksaving").show(500);
                <g:remoteFunction controller="wonderpublish" action="getSyllabus"  onSuccess='initializeCreateSyllabus(data);'
        params="'level='+level"/>
            }

    }

    function initializeCreateSyllabus(data){
        $("#subjectsandgrades").hide();
        syllabusList = data.results;
        var select = document.getElementById("createSyllabus");
        select.options.length = 1;
        for(var i=0;i< syllabusList.length; i++) {

            el = document.createElement("option");
            el.textContent = syllabusList[i].syllabus;
            el.value = syllabusList[i].syllabus;
            select.appendChild(el);
        }

        select.focus();
        $('#createSyllabus').show();
        $("#booksaving").hide();
        $("#addGroup").show();
        $("#degreeTypeSection").hide();
        var level = document.getElementById("createLevel").value;
         if(level=="Competitive Exams"||level=="Engineering Entrances"||level=="Government Recruitments"||level=="ITI & Polytechnic"||level=="Medical Entrances"){
            document.getElementById("newsyllabus").placeholder="Name of Exams group";
            document.getElementById("createSyllabus")[0].textContent="Exams";
            document.getElementById('addButton').innerHTML='Add New';
            document.getElementById('addTitleText').innerHTML='Add Exam Details';
            $("#addExamGroup").show();


        }
        else if(level=="School"){
            document.getElementById("newsyllabus").placeholder="New Syllabus";
            document.getElementById("createSyllabus")[0].textContent="Syllabus";
             document.getElementById('addButton').innerHTML='Add New Syllabus';
             document.getElementById('addTitleText').innerHTML='Add Syllabus Details';
        }
         else if(level=="General"){
             document.getElementById("newsyllabus").placeholder="New Group";
             document.getElementById("createSyllabus")[0].textContent="Group";
             document.getElementById('addButton').innerHTML='Add New Group';
             document.getElementById('addTitleText').innerHTML='Add Group Details';
             $("#addExamGroup").show();
         }
         else if(level=="College"){
             document.getElementById("newsyllabus").placeholder="New Course";
             document.getElementById("createSyllabus")[0].textContent="Courses";
             document.getElementById('addButton').innerHTML='Add New Course';
             document.getElementById('addTitleText').innerHTML='Add Course Details';
             $("#degreeTypeSection").show();
         }
        else {
             document.getElementById("newsyllabus").placeholder="New Syllabus";
             document.getElementById("createSyllabus")[0].textContent="Syllabus";
             document.getElementById('addButton').innerHTML='Add New Syllabus';
             document.getElementById('addTitleText').innerHTML='Add Syllabus Details';
             $("#addExamGroup").show();
        }

    }


    function initializeCreateSubjects(data){

        var subjects = data.results;
        var select = document.getElementById("createSubject");
        select.options.length = 1;
        for(var i=0;i<  subjects.length; i++) {

            el = document.createElement("option");
            el.textContent =  subjects[i].subject;
            el.value =  subjects[i].subject;
            select.appendChild(el);
        }

        select.focus();
        $('#createSubject').show();
        $("#booksaving").hide();
    }
    function addNewSyllabus(){
       var level = document.getElementById("createLevel").value;
        var syllabus = htmlDecode(document.getElementById("newsyllabus").value);
        if(syllabus) {
            var degreeType = "";
            if(level=="College") degreeType = document.getElementById("degreeType")[document.getElementById("degreeType").selectedIndex].value;
            document.getElementById("syllabusError").innerHTML="";
            syllabus = encodeURIComponent(syllabus);
            <g:remoteFunction controller="wonderpublish" action="addSyllabus" onSuccess='syllabusAdded(data);'
            params="'level='+level+'&syllabus='+syllabus+'&degreeType='+degreeType"></g:remoteFunction>
        }else{
            document.getElementById("syllabusError").innerHTML="Please enter a value";
            document.getElementById("newsyllabus").focus();
        }
    }

    function syllabusAdded(data){
        if("OK"==data.status) {
            var select = document.getElementById("createSyllabus");
            var el = document.createElement("option");
            el.textContent = htmlDecode(document.getElementById("newsyllabus").value);
            el.value = htmlDecode(document.getElementById("newsyllabus").value);
            select.appendChild(el);
            document.getElementById("syllabusError").innerHTML="Syllabus added successfully";
            document.getElementById("syllabusError").style.color='green';
        }else{
            document.getElementById("syllabusError").innerHTML="Syllabus already present";
            document.getElementById("syllabusError").style.color='red';
        }
    }
    $('#createLevel,#createSyllabus').on('change', function() {
        document.getElementById("newsyllabus").value='';
        document.getElementById("newsubject").value='';
        document.getElementById("syllabusError").innerHTML='';
        document.getElementById("subjectError").innerHTML='';

    });
    function addNewSubject(){
        var level = htmlDecode(document.getElementById("createLevel").value);
        var syllabus = document.getElementById("createSyllabus");
        var subject = htmlDecode(document.getElementById("newsubject").value);
        var errorString="Select syllabus";
        var hasError=true;
        if(subject) {

            if(level!="School"&&syllabus.selectedIndex==0){
                errorString="Please select a syllabus to which subject to be added";
            }else {
                hasError=false;
                document.getElementById("subjectError").innerHTML = "";
                if(level=="School") syllabus = "School"; else syllabus = syllabus[syllabus.selectedIndex].value;
                syllabus = encodeURIComponent(syllabus);
                subject = encodeURIComponent(subject);
                <g:remoteFunction controller="wonderpublish" action="addSubject" onSuccess='subjectAdded(data);'
            params="'subject='+subject+'&syllabus='+syllabus"></g:remoteFunction>
            }
        }else{
            errorString = "Please enter a value";
        }

        if(hasError){
            document.getElementById("subjectError").innerHTML=errorString;
            document.getElementById("newsubject").focus();
        }
    }

    function subjectAdded(data){
       if("OK"==data.status) {
            document.getElementById("subjectError").innerHTML="Subject added";
            var subject=htmlDecode(document.getElementById('newsubject').value);
           var table = document.querySelector("#subjects > table");
           var row = table.insertRow(-1);
           var cell=row.insertCell(-1);
           cell.innerHTML=subject;

           document.getElementById("subjectError").style.color='green';

        }else{
            document.getElementById("subjectError").innerHTML="Subject already present";
           document.getElementById("subjectError").style.color='red';
        }
    }

    function addNewExam(){
        var level = document.getElementById("createLevel").value;
        var syllabus = document.getElementById("createSyllabus");
        var exam = document.getElementById("newExam").value;
        var state = document.getElementById("newState");
        var errorString="Select Exam group";
        var hasError=true;
        if(exam) {
            if(syllabus.selectedIndex==0){
                errorString="Please select the exam group  to which exam needs to be added";
                document.getElementById("createSyllabus").focus();
            }else if(syllabus.value=="State Job Exams"&&state.selectedIndex==0){
                errorString="Please select the state to which exam needs to be added";
                document.getElementById("state").focus();
            }
            else {
                hasError=false;
                document.getElementById("examError").innerHTML = "";
                syllabus = syllabus[syllabus.selectedIndex].value;
                state = state[state.selectedIndex].value;
                syllabus = encodeURIComponent(syllabus);
                state = encodeURIComponent(state);
                exam = encodeURIComponent(exam);
                <g:remoteFunction controller="wonderpublish" action="addExam" onSuccess='examAdded(data);'
            params="'grade='+exam+'&syllabus='+syllabus+'&state='+state"></g:remoteFunction>
            }
        }else{
            errorString = "Please enter a value";
            document.getElementById("newExam").focus();

        }

        if(hasError){
            document.getElementById("examError").innerHTML=errorString;

        }
    }

    function examAdded(data){
        if("OK"==data.status) {
            document.getElementById("examError").innerHTML="Grade / Exam added";
            document.getElementById("examError").style.color='green';
        }else{
            document.getElementById("examError").innerHTML="Grade / Exam already present";
            document.getElementById("examError").style.color='red';
        }
    }

    function getSyllabusDetails(){
        $("#subjectsandgrades").hide();
        document.getElementById("subjects").innerHTML="";
        document.getElementById("grades").innerHTML="";
        var syllabus = document.getElementById("createSyllabus").value;
        if(document.getElementById("createLevel").value=="School") syllabus="School";
        if(document.getElementById("createLevel").value=="Competitive Exams"&&syllabus=="State Job Exams") $("#displayState").show();
        else $("#displayState").hide();
        syllabus = encodeURIComponent(syllabus);
        <g:remoteFunction controller="wonderpublish" action="getSyllabusDetails" onSuccess='displaySubjectsAndGrades(data);'
            params="'syllabus='+syllabus"></g:remoteFunction>
    }

    function displaySubjectsAndGrades(data){
        var subjects=data.subjects;
        var grades=data.grades;
        var featuredGrades = data.featuredGrades;
        var htmlStr="<table class='table table-bordered table-hover'><thead class='thead-dark'><th>Subjects</th></thead>";
        if(subjects.length>0){
            for(i=0;i<subjects.length;i++){
                htmlStr +="<tr><td>"+subjects[i].subject+"</td></tr>";
            }
            htmlStr +="</table>";
            document.getElementById("subjects").innerHTML=htmlStr;
        }
        if(grades.length>0){
            if(document.getElementById("createLevel").value=="Competitive Exams") {
                htmlStr="<table class='table table-bordered sorted'><thead class='thead-dark'><th>Exams</th><th class='text-center'>Featured</th>" ;
                <%  if("books".equals(session['entryController'])) { %>
            htmlStr+= "<th class='text-center'>Syllabus</th>";
                <%  }  %>
            htmlStr+= "</thead>";
            }

             else {
                htmlStr = "<table class='table table-bordered sorted'><thead class='thead-dark'><th>Grades</th><th class='text-center'>Featured</th></thead>";
            }
            var featured="";
            for(i=0;i<grades.length;i++) {
                featured = "";
                for (j = 0; j < featuredGrades.length; j++) {
                    if (featuredGrades[j].gradeId == grades[i].id) {
                        featured = "checked";
                        break;
                    }
                }
                if (document.getElementById("createLevel").value == "Competitive Exams") {
                    htmlStr += "<tr><td>" + grades[i].grade + "</td><td class='text-center custom-control custom-checkbox'><input type='checkbox' class='custom-control-input' id='featuredCheck" + (grades[i].id) + "' " +
                        "name='featuredCheck" + (grades[i].id) + "' " + featured + "><label class='custom-control-label' for='featuredCheck" + (grades[i].id) + "'></label>" +
                        "<a class='featured_btn col-md-1 btn btn-sm btn-primary form-control m-0 ml-3 " + featured + "' href='javascript:updateFeaturedGrade(" + grades[i].id + ");'>Update</a>\n" +
                        "</td>" ;

                    <%  if("books".equals(session['entryController'])) { %>
                    if (grades[i].syllabusResId!="" && grades[i].syllabusResId!=null && grades[i].syllabusResId!="0") {
                        htmlStr += " <td class='text-center'><input type=\"button\" class='btn btn-sm btn-default featured_btn border border-dark' onclick='createHTMLedit(" + grades[i].syllabusResId + ");' value='Edit'></td></tr>";
                    } else {
                        htmlStr += " <td class='text-center'><input type=\"button\" class='btn btn-sm btn-primary featured_btn' onclick='createHTML(" + grades[i].id + ");' value='Add'></td></tr>";
                    }
                    <%  }  %>


                }

                // 'bookSelectChanged();'
                else{

                    htmlStr +="<tr><td>"+grades[i].grade+"</td><td class='text-center custom-control custom-checkbox'><input type='checkbox' class='custom-control-input' id='featuredCheck"+(grades[i].id)+"' " +
                        "name='featuredCheck"+(grades[i].id)+"' "+featured+"><label class='custom-control-label' for='featuredCheck"+(grades[i].id)+"'></label>" +
                        "<a class='featured_btn col-md-1 btn btn-sm btn-primary form-control m-0 ml-3 "+featured+"' href='javascript:updateFeaturedGrade("+grades[i].id+");'>Update</a>\n" +
                        "</td></tr>";
                }

            }
            htmlStr +="</table>";
            document.getElementById("grades").innerHTML=htmlStr;
        }
        $("#subjectsandgrades").show();

    }

    function createHTML(id){
       var gradeid =id;
        window.location="/wonderpublish/notescreator?resourceType=Notes&useType=notes&mode=create&page=syllabus&gradeid="+gradeid;
    }

    function createHTMLedit(id){
        var id =id;
        window.location="/wonderpublish/notescreator?resourceType=Notes&useType=notes&mode=edit&page=syllabus&id="+id;
    }
    function updateFeaturedGrade(gradeId){
        var featuredGrade="false";
        if(document.getElementById("featuredCheck"+gradeId).checked){
            featuredGrade="true";
            //$('#featured_btn'+gradeId).addClass('checked');
        }
        //alert("featuredGrade="+featuredGrade);
        <g:remoteFunction controller="wonderpublish" action="updateFeaturedGrade" onSuccess='featuredGradeUpdated(data);'
            params="'gradeId='+gradeId+'&featured='+featuredGrade"></g:remoteFunction>

    }

    function featuredGradeUpdated(data){
        alert(data.status);
    }
</script>
<script>
    var defaultSiteName="eutkarsh";
    // $('[data-toggle="popover"]').popover();
</script>


<script>
    var addNAbtn = document.getElementById('addNAbtn');
    var newArrivalsBookIds = document.getElementById('naBookIds');

    addNAbtn.addEventListener('click',function () {
        $('.loading-icon').removeClass('hidden');
        if (newArrivalsBookIds.value!="" && newArrivalsBookIds.value != " " && newArrivalsBookIds.value!=undefined){
            var bookIds = newArrivalsBookIds.value.trim();
            <g:remoteFunction controller="pubdesk" action="addNewArrivals" params="'bookIds='+bookIds" onSuccess="bookAdded(data)" />
        }
    })

    function bookAdded(data) {
        $('.loading-icon').removeClass('hidden');
        newArrivalsBookIds.value = "";
        if (data.invalidId!=''&& data.invalidId!=null){
            document.getElementById('invalidBookIds').innerHTML = 'Invalid Book Ids : '+data.invalidId;
            document.getElementById('invalidBookIds').style.display = 'block';
        }else{
            document.getElementById('invalidBookIds').style.display = 'none';
        }
        alert("New Arrival Books Added Successfully!");
    }
</script>
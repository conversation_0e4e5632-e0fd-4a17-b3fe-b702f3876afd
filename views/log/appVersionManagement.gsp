<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>


<script>
    var loggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>


<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid publish-management" style="min-height: calc(100vh - 160px);">
    <div class='row'>
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <label for="siteId" style="display: block;">Site</label>
                        <g:select id="siteId" class="form-control" optionKey="id" optionValue="clientName"
                                  name="siteId" from="${sites}" noSelection="['':'Select']"/>
                    <label for="appType" style="display: block;">AppType</label>
                    <select id="appType" class="form-control">
                        <option>Select One</option>
                        <option value="android">Android</option>
                        <option value="ios">iOS</option>
                    </select><br>

                        <button class="btn btn-primary" style="" onclick="getAppVersionDetails();">Get Details</button><br>

                </div>




                <div id="errormsg" class="alert alert-danger has-error" role="alert" style="display: none; background: none;"></div>
                <div id="successmsg" style="display: none">Details updated</div>
                <div id="batchUsers" style="display: none"></div>
            </div>
        </div>
    </div>
    <div class="row" style="display: none" id="detailsRow">
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <label for="appVersion" style="display: block;">Details</label>
            <div class="dflex_pub">
                <input type="text" class="form-control" name="appVersion" id="appVersion"  placeholder="App Version"></input><br>
                <input type="text" class="form-control" name="versionNo" id="versionNo"  placeholder="Custom version number"></input><br>
                <textarea class="form-control" name="details" id="details"  placeholder="Releases notes"></textarea><br>
                <button class="btn btn-primary" style="" onclick="updateAppVersionDetails();">Update</button><br>

            </div>
        </div>
    </div>
</div>


<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/footer"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<%}%>

<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>

<script>

    function getAppVersionDetails(){
        $("#errormsg").hide();
        if(document.getElementById("siteId").selectedIndex==0){
            document.getElementById("errormsg").innerHTML="Please select the site.";
            $("#errormsg").show();
        }
        else if(document.getElementById("appType").selectedIndex==0){
            document.getElementById("errormsg").innerHTML="Please select the app type.";
            $("#errormsg").show();
        }else{
            $('.loading-icon').removeClass('hidden');
            $("#errormsg").hide();
        var siteId = document.getElementById("siteId")[document.getElementById("siteId").selectedIndex].value;
        var appType = document.getElementById("appType")[document.getElementById("appType").selectedIndex].value;
            <g:remoteFunction controller="log" action="getAppVersionDetails" params="'siteId='+siteId+'&appType='+appType" onSuccess = "displayDetails(data);"/>

        }
    }

    function displayDetails(data){
        $('.loading-icon').addClass('hidden');
        if("OK"==data.status){
            document.getElementById("appVersion").value=data.appVersion.appVersion;
            document.getElementById("versionNo").value=data.appVersion.versionNo;
            document.getElementById("details").innerHTML=data.appVersion.details;
        }else{
            document.getElementById("appVersion").value="";
            document.getElementById("versionNo").value="";
            document.getElementById("details").innerHTML="<ol><li></li></ol>";
        }
        $("#detailsRow").show();
    }
    function updateAppVersionDetails(){
        $("#errormsg").hide();
        if(document.getElementById("siteId").selectedIndex==0){
            document.getElementById("errormsg").innerHTML="Please select the site.";
            $("#errormsg").show();
        }
        else if(document.getElementById("appType").selectedIndex==0){
            document.getElementById("errormsg").innerHTML="Please select the app type.";
            $("#errormsg").show();
        }else if(document.getElementById("appVersion").value===""){
            document.getElementById("errormsg").innerHTML="Please add the app verison number.";
            $("#errormsg").show();
        }
        else if(document.getElementById("versionNo").value===""){
            document.getElementById("errormsg").innerHTML="Please add the custom version number.";
            $("#errormsg").show();
        }
        else if(document.getElementById("details").value===""){
            document.getElementById("errormsg").innerHTML="Please add the release notes.";
            $("#errormsg").show();
        }else{
            $('.loading-icon').removeClass('hidden');
            $("#errormsg").hide();
            var siteId = document.getElementById("siteId")[document.getElementById("siteId").selectedIndex].value;
            var appType = document.getElementById("appType")[document.getElementById("appType").selectedIndex].value;
            var appVersion = document.getElementById("appVersion").value;
            var versionNo = document.getElementById("versionNo").value;
            var details = document.getElementById("details").value;
            <g:remoteFunction controller="log" action="updateAppVersion"
            params="'siteId='+siteId+'&appType='+appType+'&appVersion='+appVersion+'&versionNo='+versionNo+'&details='+details" onSuccess = "versionUpdated(data);"/>

        }
    }


    function versionUpdated(data){
        $('.loading-icon').addClass('hidden');
        $("#successmsg").show();
    }


</script>


</body>
</html>
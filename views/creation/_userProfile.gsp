<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<style>
.orders .payment-details table td {
    border: none;
    padding: 0;
}
.orders .payment-details table td span {
    color: rgba(68, 68, 68, 0.6);
    font-size: 8px;
    font-family: 'Rubik', sans-serif;
    font-weight: 500;
}
.orders .payment-details table td p {
    font-size: 14px;
    font-family: 'Merriweather', serif;
    color: #444444;
    margin-bottom: 0;
}
.orders .payment-details table td:last-child p {
    color: #B72319;
}
.orders .payment-details table td:last-child p .rupees {
    font-size: 14px;
    font-family: 'Merriweather', serif;
    color: #B72319;
    margin-right: 2px;
}
.orders .payment-details table td:nth-child(2) {
    padding: 0 7px;
}
.orders .media .media-body {
    border-bottom: none;
}
.orders .media .media-body div p {
    max-height: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}
.extend-validity p {
    color: #3FC300;
}
.extend-validity button.btn-success {
    background: #3FC300;
    border-radius: 10px;
    width: 180px;
    border: none;
}
.extend-info .card {
    background: #3FC400;
    border-radius: 0px 0px 20px 20px;
}
.extend-info table tbody tr td {
    border-top: 0;
    border-right: 0;
    border-left: 0;
    border-color: #ffffff;
    padding: 0.5rem;
    vertical-align: middle;
    color: #ffffff;
}
.extend-info table tbody tr td button {
    background: #FFFFFF;
    border-radius: 10px;
    width: 100%;
    color: #3FC400;
    font-weight: normal;
}
.extend-info table tbody tr td:nth-child(2) {
    text-align: right;
}
.extend-info table tbody tr td:nth-child(3) {
    text-align: right;
}
.extend-info table tbody tr:last-child td {
    border-bottom: 0;
}
@media only screen and (min-device-width: 320px) and (max-device-width: 480px) {
    .user_profile {
        margin-top: 0;
    }
}
@media (max-width: 575px) {
    .users-orders > p {
        padding-right: 0 !important;
        padding-left: 0 !important;
    }
    .users-orders .container {
        padding: 0;
    }
    .orders .payment-details > div p {
        font-size: 13px;
    }
    .orders .payment-details table td.discount_applied {
        width: 60px;
    }
}
</style>
<section class="user_profile">
    <div class="container">
        <div class="row justify-content-lg-center">
            <div class="col col-lg-9">
                <ul class="nav nav-tabs" role="tablist" id="profile-menu">
                    <li class="nav-item">
                        <a class="nav-link active" data-toggle="tab" data-target="#profile">Profile</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" data-target="#orders">Orders</a>
                    </li>
                </ul>
                <div class="tab-content">
                    <div class="jumbotron tab-pane active" id="profile">
                        <form class="update-user" enctype="multipart/form-data" role="form" name="uploadProfileImage" id="uploadProfileImage" action="/creation/uploadProfileImage" method="post">
                            <div class="media d-block d-sm-flex">
                                <input type="hidden" name="type" value="user">
                                <input type="hidden" name="source" value="userProfile">
                                <input type="hidden" name="sourceController" value="creation">
                                <div class="profile-wrapper">
                                    <%if(session['userdetails'].profilepic!=null){%>
                                    <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="img-responsive align-self-start mr-3 rounded-circle edit-img">
                                    <%}else if("24"=="${session['siteId']}") {%>
                                    <img src="${assetPath(src: 'ebouquet/user-icon.png')}" class="img-responsive align-self-start mr-3 rounded-circle edit-img" alt="">
                                    <%}else {%> <img src="${assetPath(src: 'wonderslate/user-icon.png')}" class="img-responsive align-self-start mr-3 rounded-circle edit-img" alt=""><%}%>
                                    <input id="fileoption1" class="d-none" name="file" type="file"  accept="image/png, image/jpeg, image/gif ,image/svg" onchange="updateProfile();"/>
                                    <a href="javascript:updateImage();" class="rounded-circle edit-btn" style="bottom:75px;"><i class="material-icons">edit</i></a>
                                    <p id="file-error" style="font-size: 12px;
                                    margin-top: 0.5rem;
                                    text-align: center;">Please Upload Image below 2mb</p>
                                </div>
                        </form>
                    <div class="media-body">
                        <g:form name="userProfile" url="[action:'updateUserProfile',controller:'creation']" method="post">
                            <span class="input-login">
                                <input class="input-field input-field-login" type="text" id="name" maxlength="30" name="name" onkeypress="return alpha(event)" value="<%= session["userdetails"]!=null?session["userdetails"].name:"" %>">
                                <label class="input-label input-label-login input-label-login-color-1" for="name">
                                    <span class="input-label-content input-label-content-login">Name</span>
                                </label>
                            </span>

                            <span class="input-login">
                                <input class="input-field input-field-login mobile-input" type="text" name="mobile" oninput="numberOnly(this.id);" id="mobile"
                                       value="<%= session["userdetails"]!=null?session["userdetails"].mobile:""%>" required maxlength="10" minlength="10"
                                    <%=(grailsApplication.config.grails.appServer.siteName=="e-Utkarsh" && session["userdetails"]!=null && session["userdetails"].mobile!=null)?"disabled":""%>>
                                <label class="input-label input-label-login input-label-login-color-1" for="mobile">
                                    <span class="input-label-content input-label-content-login">Phone Number</span>
                                </label>
                            </span>
                            <div class="input-error-tooltip" style="display: none;">
                                <div class="input-error-tooltip-inner">Please enter 10 digit mobile number.</div>
                            </div>
                            <span class="input-login">
                                <%if((session['userdetails'].email==null) || (session['userdetails'].email=="<EMAIL>") || (session['userdetails'].email=="")){%>
                                <input type="email" name="email" id="email" class="input-field input-field-login" placeholder="Please update you email">
                                <%} else { %>
                                <input type="email" name="email" id="email" class="input-field input-field-login" value="<%= session["userdetails"]!=null?session["userdetails"].email:""%>" disabled="disabled">
                                <% } %>
                                <label class="input-label input-label-login input-label-login-color-1" for="email">
                                    <span class="input-label-content input-label-content-login">Email</span>
                                </label>
                            </span>
                            <span class="input-login">
                                <input class="input-field input-field-login" type="text" id="signup-password" value="••••••••••••" name="password" readonly disabled="disabled">
                                <label class="input-label input-label-login input-label-login-color-1" for="signup-password">
                                    <span class="input-label-content input-label-content-login">Password</span>
                                </label>
                                <a href="" data-toggle="modal" data-target="#change-password-modal" class="change-password">CHANGE</a>
                            </span>

                            <span class="input-login">

                                <span id="defaultState">
                                    State
                                    <select name="state" id="stateSelect" size="1" class="form-control">
                                        <option value="" selected="selected">Select State</option>
                                    </select>
                                </span>

                                %{--<%if((session['userdetails'].state==null) || (session['userdetails'].state=="")){%>
                                <span id="defaultState">
                                    State
                                    <select name="state" id="stateSelect" size="1" class="form-control">
                                        <option value="" selected="selected">Select State</option>
                                    </select>
                                </span>
                                <%} else { %>
                                <span id="showState" style="display: none;">
                                    State
                                    <select name="state" id="stateSelect" size="1" class="form-control">
                                        <option value="" selected="selected">Select State</option>
                                    </select>
                                </span>

                                <input class="input-field input-field-login" type="text" id="selectedState" value="<%= session["userdetails"].state %>" readonly disabled="disabled">
                                <label class="input-label input-label-login input-label-login-color-1" id="selectedStateLabel" for="selectedState">
                                    <span class="input-label-content input-label-content-login">State</span>
                                </label>
                                <a href="javascript:" id="changeState" class="change-password">CHANGE</a>
                                <% } %>--}%
                            </span>

                            <span class="input-login">

                                <span id="defaultDistrict">
                                    District
                                    <select name="district" id="districtSelect" size="1" class="form-control">
                                        <option value="" selected="selected">Select District</option>
                                    </select>
                                </span>

                                %{--<%if((session['userdetails'].district==null) || (session['userdetails'].district=="")){%>
                                <span id="defaultDistrict">
                                    District
                                    <select name="district" id="districtSelect" size="1" class="form-control">
                                        <option value="" selected="selected">Select District</option>
                                    </select>
                                </span>
                                <%} else { %>
                                <span id="showDistrict" style="display: none;">
                                    District
                                    <select name="district" id="districtSelect" size="1" class="form-control">
                                        <option value="" selected="selected">Select District</option>
                                    </select>
                                </span>

                                <input class="input-field input-field-login" type="text" id="selectedDistrict" value="<%= session["userdetails"].district %>" readonly disabled="disabled">
                                <label class="input-label input-label-login input-label-login-color-1" id="selectedDistrictLabel" for="selectedDistrict">
                                    <span class="input-label-content input-label-content-login">District</span>
                                </label>
                                <a href="javascript:" id="changeDistrict" class="change-password">CHANGE</a>
                                <% } %>--}%
                            </span>

                            <button id="updateButton" onclick="javascript:formSubmit();" class="btn btn-lg continue mt-2">Update</button>
                            </div>
                            <input type="hidden" name="mode" value="update">

                        </g:form>
                    </div>
                </div>
                <div class="jumbotron tab-pane orders" id="orders">
                    <div id="orders-wrapper"></div>
                </div>
            </div>
        </div>
    </div>

</section>

<g:render template="/creation/changePasswordModal"></g:render>
<g:render template="/wonderpublish/bookReviewModal"></g:render>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<asset:javascript src="moment.min.js"/>
<asset:javascript src="moment-timezone.js"/>
<asset:javascript src="moment-timezone-utils.js"/>
<script>
    $('#profile-tabs a').click(function(e) {
        e.preventDefault();
        $(this).tab('show');
    });

    $("ul.nav-tabs > li > a").on("shown.bs.tab", function(e) {
        var id = $(e.target)!=null && $(e.target).attr("href")!=null?$(e.target).attr("href").substr(1):"";
        window.location.hash = id;
    });

    var hash = window.location.hash;
    $('#profile-tabs a[href="' + hash + '"]').tab('show');
</script>

<script>

    function alpha(e) {
        var k;
        document.all ? k = e.keyCode : k = e.which;
        return ((k > 64 && k < 91) || (k > 96 && k < 123) || k == 8 || k == 32 || (k >= 48 && k <= 57));
    }

    function numberOnly(id) {
        var element = document.getElementById(id);
        var regex = /[^0-9]/gi;
        element.value = element.value.replace(regex, "");
    }



</script>
<g:render template="/usermanagement/ordersListInclude"></g:render>
<script src="https://checkout.razorpay.com/v1/checkout.js"></script>
<script>
    var stateObject = {
        "Andhra Pradesh": [
            "Anantapur",
            "Chittoor",
            "East Godavari",
            "Guntur",
            "Krishna",
            "Kurnool",
            "Nellore",
            "Prakasam",
            "Srikakulam",
            "Visakhapatnam",
            "Vizianagaram",
            "West Godavari",
            "YSR Kadapa"
        ],
        "Arunachal Pradesh": [
            "Tawang",
            "West Kameng",
            "East Kameng",
            "Papum Pare",
            "Kurung Kumey",
            "Kra Daadi",
            "Lower Subansiri",
            "Upper Subansiri",
            "West Siang",
            "East Siang",
            "Siang",
            "Upper Siang",
            "Lower Siang",
            "Lower Dibang Valley",
            "Dibang Valley",
            "Anjaw",
            "Lohit",
            "Namsai",
            "Changlang",
            "Tirap",
            "Longding"
        ],
        "Assam": [
            "Baksa",
            "Barpeta",
            "Biswanath",
            "Bongaigaon",
            "Cachar",
            "Charaideo",
            "Chirang",
            "Darrang",
            "Dhemaji",
            "Dhubri",
            "Dibrugarh",
            "Goalpara",
            "Golaghat",
            "Hailakandi",
            "Hojai",
            "Jorhat",
            "Kamrup Metropolitan",
            "Kamrup",
            "Karbi Anglong",
            "Karimganj",
            "Kokrajhar",
            "Lakhimpur",
            "Majuli",
            "Morigaon",
            "Nagaon",
            "Nalbari",
            "Dima Hasao",
            "Sivasagar",
            "Sonitpur",
            "South Salmara-Mankachar",
            "Tinsukia",
            "Udalguri",
            "West Karbi Anglong"
        ],
        "Bihar": [
            "Araria",
            "Arwal",
            "Aurangabad",
            "Banka",
            "Begusarai",
            "Bhagalpur",
            "Bhojpur",
            "Buxar",
            "Darbhanga",
            "East Champaran (Motihari)",
            "Gaya",
            "Gopalganj",
            "Jamui",
            "Jehanabad",
            "Kaimur (Bhabua)",
            "Katihar",
            "Khagaria",
            "Kishanganj",
            "Lakhisarai",
            "Madhepura",
            "Madhubani",
            "Munger (Monghyr)",
            "Muzaffarpur",
            "Nalanda",
            "Nawada",
            "Patna",
            "Purnia (Purnea)",
            "Rohtas",
            "Saharsa",
            "Samastipur",
            "Saran",
            "Sheikhpura",
            "Sheohar",
            "Sitamarhi",
            "Siwan",
            "Supaul",
            "Vaishali",
            "West Champaran"
        ],
        "Chandigarh (UT)": [
            "Chandigarh"
        ],
        "Chhattisgarh": [
            "Balod",
            "Baloda Bazar",
            "Balrampur",
            "Bastar",
            "Bemetara",
            "Bijapur",
            "Bilaspur",
            "Dantewada (South Bastar)",
            "Dhamtari",
            "Durg",
            "Gariyaband",
            "Janjgir-Champa",
            "Jashpur",
            "Kabirdham (Kawardha)",
            "Kanker (North Bastar)",
            "Kondagaon",
            "Korba",
            "Korea (Koriya)",
            "Mahasamund",
            "Mungeli",
            "Narayanpur",
            "Raigarh",
            "Raipur",
            "Rajnandgaon",
            "Sukma",
            "Surajpur  ",
            "Surguja"
        ],
        "Dadra and Nagar Haveli (UT)": [
            "Dadra & Nagar Haveli"
        ],
        "Daman and Diu (UT)": [
            "Daman",
            "Diu"
        ],
        "Delhi (NCT)": [
            "Central Delhi",
            "East Delhi",
            "New Delhi",
            "North Delhi",
            "North East  Delhi",
            "North West  Delhi",
            "Shahdara",
            "South Delhi",
            "South East Delhi",
            "South West  Delhi",
            "West Delhi"
        ],
        "Goa": [
            "North Goa",
            "South Goa"
        ],
        "Gujarat": [
            "Ahmedabad",
            "Amreli",
            "Anand",
            "Aravalli",
            "Banaskantha (Palanpur)",
            "Bharuch",
            "Bhavnagar",
            "Botad",
            "Chhota Udepur",
            "Dahod",
            "Dangs (Ahwa)",
            "Devbhoomi Dwarka",
            "Gandhinagar",
            "Gir Somnath",
            "Jamnagar",
            "Junagadh",
            "Kachchh",
            "Kheda (Nadiad)",
            "Mahisagar",
            "Mehsana",
            "Morbi",
            "Narmada (Rajpipla)",
            "Navsari",
            "Panchmahal (Godhra)",
            "Patan",
            "Porbandar",
            "Rajkot",
            "Sabarkantha (Himmatnagar)",
            "Surat",
            "Surendranagar",
            "Tapi (Vyara)",
            "Vadodara",
            "Valsad"
        ],
        "Haryana": [
            "Ambala",
            "Bhiwani",
            "Charkhi Dadri",
            "Faridabad",
            "Fatehabad",
            "Gurgaon",
            "Hisar",
            "Jhajjar",
            "Jind",
            "Kaithal",
            "Karnal",
            "Kurukshetra",
            "Mahendragarh",
            "Mewat",
            "Palwal",
            "Panchkula",
            "Panipat",
            "Rewari",
            "Rohtak",
            "Sirsa",
            "Sonipat",
            "Yamunanagar"
        ],
        "Himachal Pradesh": [
            "Bilaspur",
            "Chamba",
            "Hamirpur",
            "Kangra",
            "Kinnaur",
            "Kullu",
            "Lahaul &amp; Spiti",
            "Mandi",
            "Shimla",
            "Sirmaur (Sirmour)",
            "Solan",
            "Una"
        ],
        "Jammu and Kashmir": [
            "Anantnag",
            "Bandipore",
            "Baramulla",
            "Budgam",
            "Doda",
            "Ganderbal",
            "Jammu",
            "Kargil",
            "Kathua",
            "Kishtwar",
            "Kulgam",
            "Kupwara",
            "Leh",
            "Poonch",
            "Pulwama",
            "Rajouri",
            "Ramban",
            "Reasi",
            "Samba",
            "Shopian",
            "Srinagar",
            "Udhampur"
        ],
        "Jharkhand": [
            "Bokaro",
            "Chatra",
            "Deoghar",
            "Dhanbad",
            "Dumka",
            "East Singhbhum",
            "Garhwa",
            "Giridih",
            "Godda",
            "Gumla",
            "Hazaribag",
            "Jamtara",
            "Khunti",
            "Koderma",
            "Latehar",
            "Lohardaga",
            "Pakur",
            "Palamu",
            "Ramgarh",
            "Ranchi",
            "Sahibganj",
            "Seraikela-Kharsawan",
            "Simdega",
            "West Singhbhum"
        ],
        "Karnataka": [
            "Bagalkot",
            "Ballari (Bellary)",
            "Belagavi (Belgaum)",
            "Bengaluru (Bangalore) Rural",
            "Bengaluru (Bangalore) Urban",
            "Bidar",
            "Chamarajanagar",
            "Chikballapur",
            "Chikkamagaluru (Chikmagalur)",
            "Chitradurga",
            "Dakshina Kannada",
            "Davangere",
            "Dharwad",
            "Gadag",
            "Hassan",
            "Haveri",
            "Kalaburagi (Gulbarga)",
            "Kodagu",
            "Kolar",
            "Koppal",
            "Mandya",
            "Mysuru (Mysore)",
            "Raichur",
            "Ramanagara",
            "Shivamogga (Shimoga)",
            "Tumakuru (Tumkur)",
            "Udupi",
            "Uttara Kannada (Karwar)",
            "Vijayapura (Bijapur)",
            "Yadgir"
        ],
        "Kerala": [
            "Alappuzha",
            "Ernakulam",
            "Idukki",
            "Kannur",
            "Kasaragod",
            "Kollam",
            "Kottayam",
            "Kozhikode",
            "Malappuram",
            "Palakkad",
            "Pathanamthitta",
            "Thiruvananthapuram",
            "Thrissur",
            "Wayanad"
        ],
        "Lakshadweep (UT)": [
            "Agatti",
            "Amini",
            "Androth",
            "Bithra",
            "Chethlath",
            "Kavaratti",
            "Kadmath",
            "Kalpeni",
            "Kilthan",
            "Minicoy"
        ],
        "Madhya Pradesh": [
            "Agar Malwa",
            "Alirajpur",
            "Anuppur",
            "Ashoknagar",
            "Balaghat",
            "Barwani",
            "Betul",
            "Bhind",
            "Bhopal",
            "Burhanpur",
            "Chhatarpur",
            "Chhindwara",
            "Damoh",
            "Datia",
            "Dewas",
            "Dhar",
            "Dindori",
            "Guna",
            "Gwalior",
            "Harda",
            "Hoshangabad",
            "Indore",
            "Jabalpur",
            "Jhabua",
            "Katni",
            "Khandwa",
            "Khargone",
            "Mandla",
            "Mandsaur",
            "Morena",
            "Narsinghpur",
            "Neemuch",
            "Panna",
            "Raisen",
            "Rajgarh",
            "Ratlam",
            "Rewa",
            "Sagar",
            "Satna",
            "Sehore",
            "Seoni",
            "Shahdol",
            "Shajapur",
            "Sheopur",
            "Shivpuri",
            "Sidhi",
            "Singrauli",
            "Tikamgarh",
            "Ujjain",
            "Umaria",
            "Vidisha"
        ],
        "Maharashtra": [
            "Ahmednagar",
            "Akola",
            "Amravati",
            "Aurangabad",
            "Beed",
            "Bhandara",
            "Buldhana",
            "Chandrapur",
            "Dhule",
            "Gadchiroli",
            "Gondia",
            "Hingoli",
            "Jalgaon",
            "Jalna",
            "Kolhapur",
            "Latur",
            "Mumbai City",
            "Mumbai Suburban",
            "Nagpur",
            "Nanded",
            "Nandurbar",
            "Nashik",
            "Osmanabad",
            "Palghar",
            "Parbhani",
            "Pune",
            "Raigad",
            "Ratnagiri",
            "Sangli",
            "Satara",
            "Sindhudurg",
            "Solapur",
            "Thane",
            "Wardha",
            "Washim",
            "Yavatmal"
        ],
        "Manipur": [
            "Bishnupur",
            "Chandel",
            "Churachandpur",
            "Imphal East",
            "Imphal West",
            "Jiribam",
            "Kakching",
            "Kamjong",
            "Kangpokpi",
            "Noney",
            "Pherzawl",
            "Senapati",
            "Tamenglong",
            "Tengnoupal",
            "Thoubal",
            "Ukhrul"
        ],
        "Meghalaya": [
            "East Garo Hills",
            "East Jaintia Hills",
            "East Khasi Hills",
            "North Garo Hills",
            "Ri Bhoi",
            "South Garo Hills",
            "South West Garo Hills ",
            "South West Khasi Hills",
            "West Garo Hills",
            "West Jaintia Hills",
            "West Khasi Hills"
        ],
        "Mizoram": [
            "Aizawl",
            "Champhai",
            "Kolasib",
            "Lawngtlai",
            "Lunglei",
            "Mamit",
            "Saiha",
            "Serchhip"
        ],
        "Nagaland": [
            "Dimapur",
            "Kiphire",
            "Kohima",
            "Longleng",
            "Mokokchung",
            "Mon",
            "Peren",
            "Phek",
            "Tuensang",
            "Wokha",
            "Zunheboto"
        ],
        "Odisha": [
            "Angul",
            "Balangir",
            "Balasore",
            "Bargarh",
            "Bhadrak",
            "Boudh",
            "Cuttack",
            "Deogarh",
            "Dhenkanal",
            "Gajapati",
            "Ganjam",
            "Jagatsinghapur",
            "Jajpur",
            "Jharsuguda",
            "Kalahandi",
            "Kandhamal",
            "Kendrapara",
            "Kendujhar (Keonjhar)",
            "Khordha",
            "Koraput",
            "Malkangiri",
            "Mayurbhanj",
            "Nabarangpur",
            "Nayagarh",
            "Nuapada",
            "Puri",
            "Rayagada",
            "Sambalpur",
            "Sonepur",
            "Sundargarh"
        ],
        "Puducherry (UT)": [
            "Karaikal",
            "Mahe",
            "Pondicherry",
            "Yanam"
        ],
        "Punjab": [
            "Amritsar",
            "Barnala",
            "Bathinda",
            "Faridkot",
            "Fatehgarh Sahib",
            "Fazilka",
            "Ferozepur",
            "Gurdaspur",
            "Hoshiarpur",
            "Jalandhar",
            "Kapurthala",
            "Ludhiana",
            "Mansa",
            "Moga",
            "Muktsar",
            "Nawanshahr (Shahid Bhagat Singh Nagar)",
            "Pathankot",
            "Patiala",
            "Rupnagar",
            "Sahibzada Ajit Singh Nagar (Mohali)",
            "Sangrur",
            "Tarn Taran"
        ],
        "Rajasthan": [
            "Ajmer",
            "Alwar",
            "Banswara",
            "Baran",
            "Barmer",
            "Bharatpur",
            "Bhilwara",
            "Bikaner",
            "Bundi",
            "Chittorgarh",
            "Churu",
            "Dausa",
            "Dholpur",
            "Dungarpur",
            "Hanumangarh",
            "Jaipur",
            "Jaisalmer",
            "Jalore",
            "Jhalawar",
            "Jhunjhunu",
            "Jodhpur",
            "Karauli",
            "Kota",
            "Nagaur",
            "Pali",
            "Pratapgarh",
            "Rajsamand",
            "Sawai Madhopur",
            "Sikar",
            "Sirohi",
            "Sri Ganganagar",
            "Tonk",
            "Udaipur"
        ],
        "Sikkim": [
            "East Sikkim",
            "North Sikkim",
            "South Sikkim",
            "West Sikkim"
        ],
        "Tamil Nadu": [
            "Ariyalur",
            "Chennai",
            "Coimbatore",
            "Cuddalore",
            "Dharmapuri",
            "Dindigul",
            "Erode",
            "Kanchipuram",
            "Kanyakumari",
            "Karur",
            "Krishnagiri",
            "Madurai",
            "Nagapattinam",
            "Namakkal",
            "Nilgiris",
            "Perambalur",
            "Pudukkottai",
            "Ramanathapuram",
            "Salem",
            "Sivaganga",
            "Thanjavur",
            "Theni",
            "Thoothukudi (Tuticorin)",
            "Tiruchirappalli",
            "Tirunelveli",
            "Tiruppur",
            "Tiruvallur",
            "Tiruvannamalai",
            "Tiruvarur",
            "Vellore",
            "Viluppuram",
            "Virudhunagar"
        ],
        "Telangana": [
            "Adilabad",
            "Bhadradri Kothagudem",
            "Hyderabad",
            "Jagtial",
            "Jangaon",
            "Jayashankar Bhoopalpally",
            "Jogulamba Gadwal",
            "Kamareddy",
            "Karimnagar",
            "Khammam",
            "Komaram Bheem Asifabad",
            "Mahabubabad",
            "Mahabubnagar",
            "Mancherial",
            "Medak",
            "Medchal",
            "Nagarkurnool",
            "Nalgonda",
            "Nirmal",
            "Nizamabad",
            "Peddapalli",
            "Rajanna Sircilla",
            "Rangareddy",
            "Sangareddy",
            "Siddipet",
            "Suryapet",
            "Vikarabad",
            "Wanaparthy",
            "Warangal (Rural)",
            "Warangal (Urban)",
            "Yadadri Bhuvanagiri"
        ],
        "Tripura": [
            "Dhalai",
            "Gomati",
            "Khowai",
            "North Tripura",
            "Sepahijala",
            "South Tripura",
            "Unakoti",
            "West Tripura"
        ],
        "Uttarakhand": [
            "Almora",
            "Bageshwar",
            "Chamoli",
            "Champawat",
            "Dehradun",
            "Haridwar",
            "Nainital",
            "Pauri Garhwal",
            "Pithoragarh",
            "Rudraprayag",
            "Tehri Garhwal",
            "Udham Singh Nagar",
            "Uttarkashi"
        ],
        "Uttar Pradesh": [
            "Agra",
            "Aligarh",
            "Allahabad",
            "Ambedkar Nagar",
            "Amethi (Chatrapati Sahuji Mahraj Nagar)",
            "Amroha (J.P. Nagar)",
            "Auraiya",
            "Azamgarh",
            "Baghpat",
            "Bahraich",
            "Ballia",
            "Balrampur",
            "Banda",
            "Barabanki",
            "Bareilly",
            "Basti",
            "Bhadohi",
            "Bijnor",
            "Budaun",
            "Bulandshahr",
            "Chandauli",
            "Chitrakoot",
            "Deoria",
            "Etah",
            "Etawah",
            "Faizabad",
            "Farrukhabad",
            "Fatehpur",
            "Firozabad",
            "Gautam Buddha Nagar",
            "Ghaziabad",
            "Ghazipur",
            "Gonda",
            "Gorakhpur",
            "Hamirpur",
            "Hapur (Panchsheel Nagar)",
            "Hardoi",
            "Hathras",
            "Jalaun",
            "Jaunpur",
            "Jhansi",
            "Kannauj",
            "Kanpur Dehat",
            "Kanpur Nagar",
            "Kanshiram Nagar (Kasganj)",
            "Kaushambi",
            "Kushinagar (Padrauna)",
            "Lakhimpur - Kheri",
            "Lalitpur",
            "Lucknow",
            "Maharajganj",
            "Mahoba",
            "Mainpuri",
            "Mathura",
            "Mau",
            "Meerut",
            "Mirzapur",
            "Moradabad",
            "Muzaffarnagar",
            "Pilibhit",
            "Pratapgarh",
            "RaeBareli",
            "Rampur",
            "Saharanpur",
            "Sambhal (Bhim Nagar)",
            "Sant Kabir Nagar",
            "Shahjahanpur",
            "Shamali (Prabuddh Nagar)",
            "Shravasti",
            "Siddharth Nagar",
            "Sitapur",
            "Sonbhadra",
            "Sultanpur",
            "Unnao",
            "Varanasi"
        ],
        "West Bengal": [
            "Alipurduar",
            "Bankura",
            "Birbhum",
            "Burdwan (Bardhaman)",
            "Cooch Behar",
            "Dakshin Dinajpur (South Dinajpur)",
            "Darjeeling",
            "Hooghly",
            "Howrah",
            "Jalpaiguri",
            "Kalimpong",
            "Kolkata",
            "Malda",
            "Murshidabad",
            "Nadia",
            "North 24 Parganas",
            "Paschim Medinipur (West Medinipur)",
            "Purba Medinipur (East Medinipur)",
            "Purulia",
            "South 24 Parganas",
            "Uttar Dinajpur (North Dinajpur)"
        ]

    }

    // Change State and District Function


        var stateSel = document.getElementById("stateSelect"),
            districtSel = document.getElementById("districtSelect");
        for (var state in stateObject) {
            stateSel.options[stateSel.options.length] = new Option(state, state);
        }
        stateSel.onchange = function () {
            districtSel.length = 1; // remove all options bar first
            if (this.selectedIndex < 1) return; // done
            var district = stateObject[stateSel.value];
            for (var i = 0; i < district.length; i++) {
                districtSel.options[districtSel.options.length] = new Option(district[i], district[i]);
            }
        }
        // stateSel.onchange(); // reset in case page is reloaded
        stateSel.onload = function () {
            districtSel.length = 1; // remove all options bar first
            if (this.selectedIndex < 1) return; // done
            var district = stateObject[stateSel.value];
            for (var i = 0; i < district.length; i++) {
                districtSel.options[districtSel.options.length] = new Option(district[i], district[i]);
            }
        }
        stateSel.onload(); // reset in case page is reloaded

        $('#stateSelect').val('<%= session["userdetails"].state %>').trigger('change');
        $('#districtSelect').val('<%= session["userdetails"].district %>').trigger('change');

    var defaultSiteName="${grailsApplication.config.grails.appServer.default}";
    var siteId="${session["siteId"]}";
    var name="";
    var image="";
    <%com.wonderslate.data.SiteMst siteMst = com.wonderslate.data.SiteMst.findById(session["siteId"])%>

        if(siteId==1) {
            name = "${grailsApplication.config.grails.appServer.siteName}";
            image="https://www.wonderslate.com/assets/wonderslate/logo.svg";
        }else if (siteId==5){
            name = "Blackspine";
            image="/assets/landingpageImages/blackspine_logo.png";
        }else if(siteId==3){
            name = "Arihant Publications";
            image="/assets/arihant/footer-logo.png";
        }


    function openRazorPay(price,title,bookId,type,chaptersId) {
        var options = {
            "key": "${siteMst.razorPayKeyId}",
            "amount": price*100, // 2000 paise = INR 20
            "name": name,
            "description": title+" (Course "+bookId+")",
            "image": image,
            "handler": function (response) {
                $('.loading-icon').removeClass('hidden');
                $(".lmodal").show();
                $('body').removeClass('loaded');
                window.location = "/wonderpublish/purchase?razorpay_payment_id=" + response.razorpay_payment_id + "&bookId="+bookId+"&type="+type+"&chaptersId="+chaptersId;
            },
            "prefill": {
                "name": "<%=(session.getAttribute("userdetails")!=null && session['userdetails'].name!=null)?"${session['userdetails'].name}":""%>",
                "email": "<%=(session.getAttribute("userdetails")!=null && session['userdetails'].email!=null)?"${session['userdetails'].email}":""%>",
                "contact": "<%=(session.getAttribute("userdetails")!=null && session['userdetails'].mobile!=null)?"${session['userdetails'].mobile}":""%>"
            },
            "notes": {
                "bookId":bookId,
                "username":"<%=(session.getAttribute("userdetails")!=null && session['userdetails'].username!=null)?"${session['userdetails'].username}":""%>"
            },
            "theme": {
                "color": "#F37254"
            },
            "readonly": {
                "email": 1,
                "contact": 1
            }
        };
        var rzp1 = new Razorpay(options);
        rzp1.open();
    }


</script>
<script>
    <%if("true".equals(params.forgottenPassword)){%>
    $('#change-password-modal').modal('show');
    <%}%>
</script>


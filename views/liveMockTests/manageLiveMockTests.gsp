<g:render template="/${session['entryController']}/navheader_new"></g:render>

<style>
    body {
        background: #f8f9fa !important;
    }
    main {
        min-height: 75vh;
        margin-top: 4rem;
    }
    .ws_container {
        width: calc(100% - 30%);
        margin: 0 auto;
    }
    .liveMockTests {
        margin-top: 2rem;
        margin-bottom: 3rem;
    }
    .liveMockTests__title {
        font-size: 1.7rem;
        margin-bottom: 2rem;
        text-align: center;
        color: #333;
    }

    /* Modern Tab Navigation */
    .nav-tabs {
        border-bottom: 2px solid #e9ecef;
        margin-bottom: 30px;
        display: flex;
        flex-wrap: wrap;
        gap: 0;
    }

    .nav-tabs li {
        margin-bottom: -2px;
        border: none;
    }

    .nav-tabs li a {
        border: none;
        border-radius: 8px 8px 0 0;
        padding: 12px 24px;
        color: #6c757d;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s ease;
        background: #f8f9fa;
        margin-right: 4px;
        position: relative;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .nav-tabs li a:hover {
        background: #e9ecef;
        color: #495057;
        border: none;
    }

    .nav-tabs li.active a {
        background: #fff;
        color: #007bff;
        border: none;
        border-bottom: 2px solid #007bff;
        font-weight: 600;
    }

    .nav-tabs li a i {
        font-size: 16px;
    }

    .tab-pane {
        min-height: 400px;
    }

    /* Modern Table Styles */
    .table-responsive {
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        overflow: hidden;
        border: 1px solid #e9ecef;
    }

    .table {
        margin-bottom: 0;
        border-collapse: separate;
        border-spacing: 0;
    }

    .table thead th {
        background: #667eea;
        color: #fff;
        border: none;
        font-weight: 600;
        padding: 18px 16px;
        vertical-align: middle;
        text-transform: uppercase;
        font-size: 12px;
        letter-spacing: 0.5px;
        position: relative;
    }

    .table thead th:first-child {
        border-top-left-radius: 12px;
    }

    .table thead th:last-child {
        border-top-right-radius: 12px;
    }

    .table tbody td {
        padding: 16px;
        vertical-align: middle;
        border-top: 1px solid #f1f3f4;
        border-bottom: none;
        font-size: 14px;
        color: #2c3e50;
    }

    .table tbody tr {
        transition: all 0.3s ease;
        border-bottom: 1px solid #f8f9fa;
    }

    .table tbody tr:hover {
        background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .table tbody tr:last-child td:first-child {
        border-bottom-left-radius: 12px;
    }

    .table tbody tr:last-child td:last-child {
        border-bottom-right-radius: 12px;
    }

    /* Modern cell content styling */
    .table tbody td strong {
        color: #2c3e50;
        font-weight: 600;
    }

    .table tbody td .test-name {
        font-weight: 600;
        color: #2c3e50;
        font-size: 15px;
    }

    .table tbody td .test-meta {
        color: #6c757d;
        font-size: 12px;
        margin-top: 2px;
    }

    /* Modern Status badges */
    .status-badge {
        padding: 6px 14px;
        border-radius: 25px;
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        display: inline-flex;
        align-items: center;
        gap: 4px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .status-badge.ongoing {
        background: linear-gradient(135deg, #00b894, #00a085);
        color: #fff;
    }

    .status-badge.upcoming {
        background: linear-gradient(135deg, #fdcb6e, #e17055);
        color: #fff;
    }

    .status-badge.completed {
        background: linear-gradient(135deg, #74b9ff, #0984e3);
        color: #fff;
    }

    .status-badge::before {
        content: '';
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: rgba(255,255,255,0.8);
    }

    /* Modern Delete button */
    .btn-delete {
        background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 8px;
        font-size: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 6px;
        box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .btn-delete:hover {
        background: linear-gradient(135deg, #ff5252, #d32f2f);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
    }

    .btn-delete:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);
    }

    .btn-delete:disabled {
        background: linear-gradient(135deg, #95a5a6, #7f8c8d);
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    /* Modern data styling */
    .data-cell {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

    .data-primary {
        font-weight: 600;
        color: #2c3e50;
        font-size: 14px;
    }

    .data-secondary {
        color: #7f8c8d;
        font-size: 12px;
    }

    .metric-badge {
        background: linear-gradient(135deg, #a29bfe, #6c5ce7);
        color: white;
        padding: 4px 10px;
        border-radius: 15px;
        font-size: 11px;
        font-weight: 600;
        display: inline-block;
    }

    .time-badge {
        background: linear-gradient(135deg, #fd79a8, #e84393);
        color: white;
        padding: 4px 10px;
        border-radius: 15px;
        font-size: 11px;
        font-weight: 600;
        display: inline-block;
    }

    /* Loading and empty states */
    .loading-spinner {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }

    .loading-spinner i {
        margin-bottom: 15px;
        color: #007bff;
    }

    .no-tests {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }

    .no-tests i {
        margin-bottom: 15px;
    }

    .no-tests h4 {
        margin-bottom: 10px;
        color: #495057;
    }

    #ranksModal,
    #testDetailsModal{
        z-index: 9999;
    }

    /* Modern Responsive Design */
    @media (max-width: 1200px) {
        .ws_container {
            width: calc(100% - 10%);
        }
    }

    @media (max-width: 768px) {
        .ws_container {
            width: calc(100% - 4%);
        }
        .nav-tabs li a{
            padding: 12px 6px;
            gap: 5px;
            font-size: 12px;
        }
        .table-responsive {
            font-size: 12px;
            border-radius: 8px;
        }
        .table thead th {
            padding: 12px 8px;
            font-size: 10px;
        }
        .table tbody td {
            padding: 10px 8px;
        }
        .data-primary {
            font-size: 12px;
        }
        .data-secondary {
            font-size: 10px;
        }
        .btn-delete {
            padding: 6px 10px;
            font-size: 10px;
        }
        .metric-badge,
        .time-badge {
            font-size: 9px;
            padding: 3px 8px;
        }
        .status-badge {
            font-size: 9px;
            padding: 4px 10px;
        }
    }

    @media (max-width: 480px) {
        .table thead th:nth-child(2) {
            display: none;
        }
        .table tbody td:nth-child(2) {
            display: none;
        }
        .liveMockTests__title {
            font-size: 1.4rem;
        }
        .btn-sm {
            padding: 4px 8px;
            font-size: 10px;
        }
        .modal-body {
            padding: 20px 15px;
        }
        .detail-group {
            flex-direction: column;
            align-items: flex-start;
        }
        .detail-group label {
            min-width: auto;
            margin-bottom: 5px;
        }
    }

    .tabpanel{
        padding: 0 !important;
    }

    /* Modal Styles */
    .modal-content {
        border-radius: 12px;
        border: none;
        box-shadow: 0 10px 40px rgba(0,0,0,0.2);
    }

    .modal-header {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-radius: 12px 12px 0 0;
        border-bottom: none;
        padding: 20px 30px;
        display: flex;
    }

    .modal-title {
        font-weight: 600;
        font-size: 1.25rem;
    }

    .modal-body {
        padding: 30px;
    }

    .detail-group {
        margin-bottom: 15px;
        display: flex;
        align-items: center;
    }

    .detail-group label {
        font-weight: 600;
        color: #2c3e50;
        min-width: 120px;
        margin-bottom: 0;
        margin-right: 10px;
    }

    .detail-group span {
        color: #34495e;
        background: #f8f9fa;
        padding: 5px 10px;
        border-radius: 6px;
        flex: 1;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        border-radius: 8px;
        padding: 8px 16px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #5a6fd8, #6a42a0);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }

    .btn-success {
        background: linear-gradient(135deg, #00b894, #00a085);
        border: none;
        border-radius: 8px;
        padding: 8px 16px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 184, 148, 0.3);
        color: white;
    }

    .btn-success:hover {
        background: linear-gradient(135deg, #00a085, #008f75);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 184, 148, 0.4);
        color: white;
    }

    .btn-sm {
        padding: 6px 12px;
        font-size: 12px;
    }

    .ml-2 {
        margin-left: 8px;
    }
</style>

<main>
    <div class="ws_container">
        <section class="liveMockTests">
            <h1 class="liveMockTests__title">Manage Live Mock Tests</h1>

            <!-- Tab Navigation -->
            <ul class="nav nav-tabs" role="tablist">
                <li role="presentation" class="active">
                    <a href="#ongoing-tests" aria-controls="ongoing-tests" role="tab" onclick="switchTab(event, 'ongoing')" data-tab="ongoing">
                        <i class="fa fa-play-circle"></i> On-Going
                    </a>
                </li>
                <li role="presentation">
                    <a href="#upcoming-tests" aria-controls="upcoming-tests" role="tab" onclick="switchTab(event, 'upcoming')" data-tab="upcoming">
                        <i class="fa-solid fa-hourglass-start"></i> Up-coming
                    </a>
                </li>
                <li role="presentation">
                    <a href="#completed-tests" aria-controls="completed-tests" role="tab" onclick="switchTab(event, 'completed')" data-tab="completed">
                        <i class="fa fa-check-circle"></i> Completed
                    </a>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Ongoing Tests Tab -->
                <div role="tabpanel" class="tab-pane in active" id="ongoing-tests">
                    <div id="ongoing-tests-content" class="tests-content">
                        <div class="loading-spinner">
                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                            <p>Loading ongoing tests...</p>
                        </div>
                    </div>
                    <div id="ongoing-tests-pagination"></div>
                </div>

                <!-- Upcoming Tests Tab -->
                <div role="tabpanel" class="tab-pane" id="upcoming-tests">
                    <div id="upcoming-tests-content" class="tests-content">
                        <div class="loading-spinner">
                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                            <p>Loading upcoming tests...</p>
                        </div>
                    </div>
                    <div id="upcoming-tests-pagination"></div>
                </div>

                <!-- Completed Tests Tab -->
                <div role="tabpanel" class="tab-pane" id="completed-tests">
                    <div id="completed-tests-content" class="tests-content">
                        <div class="loading-spinner">
                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                            <p>Loading completed tests...</p>
                        </div>
                    </div>
                    <div id="completed-tests-pagination"></div>
                </div>
            </div>
        </section>
    </div>
</main>

<!-- Test Details Modal -->
<div class="modal fade" id="testDetailsModal" tabindex="-1" role="dialog" aria-labelledby="testDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="testDetailsModalLabel">Test Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="detail-group">
                            <label>Test Name:</label>
                            <span id="modal-testName"></span>
                        </div>
                        <div class="detail-group">
                            <label>Resource ID:</label>
                            <span id="modal-resId"></span>
                        </div>
                        <div class="detail-group">
                            <label>Quiz ID:</label>
                            <span id="modal-quizId"></span>
                        </div>
                        <div class="detail-group">
                            <label>Questions:</label>
                            <span id="modal-questions"></span>
                        </div>
                        <div class="detail-group">
                            <label>Duration:</label>
                            <span id="modal-duration"></span>
                        </div>
                        <div class="detail-group">
                            <label>Status:</label>
                            <span id="modal-status"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="detail-group">
                            <label>Start Date:</label>
                            <span id="modal-startDate"></span>
                        </div>
                        <div class="detail-group">
                            <label>End Date:</label>
                            <span id="modal-endDate"></span>
                        </div>
                        <div class="detail-group">
                            <label>Result Date:</label>
                            <span id="modal-resultDate"></span>
                        </div>
                        <div class="detail-group">
                            <label>Created By:</label>
                            <span id="modal-createdBy"></span>
                        </div>
                        <div class="detail-group">
                            <label>Date Created:</label>
                            <span id="modal-dateCreated"></span>
                        </div>
                        <div class="detail-group">
                            <label>Languages:</label>
                            <span id="modal-languages"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    var currentTab = 'ongoing';
    var currentPage = {
        ongoing: 1,
        upcoming: 1,
        completed: 1,
    };
    var pageSize = 10;

    // Initialize the page
    loadTests('ongoing', 1);

    // Function to switch tabs
    window.switchTab = function(event, tabType) {
        event.preventDefault();

        // Update tab navigation
        var tabs = document.querySelectorAll('.nav-tabs li');
        var tabPanes = document.querySelectorAll('.tab-pane');

        tabs.forEach(function(tab) {
            tab.classList.remove('active');
        });

        tabPanes.forEach(function(pane) {
            pane.classList.remove('active', 'in');
        });

        // Activate current tab
        event.target.closest('li').classList.add('active');
        document.getElementById(tabType + '-tests').classList.add('active', 'in');

        currentTab = tabType;
        loadTests(tabType, currentPage[tabType]);
    };

    // Function to load tests based on tab type and page
    async function loadTests(tabType, page) {
        if (!page) page = 1;

        var contentId = tabType + '-tests-content';
        var paginationId = tabType + '-tests-pagination';

        // Show loading spinner
        var contentElement = document.getElementById(contentId);
        contentElement.innerHTML = '<div class="loading-spinner">' +
            '<i class="fa fa-spinner fa-spin fa-2x"></i>' +
            '<p>Loading ' + tabType + ' tests...</p>' +
            '</div>';

        // Clear pagination
        document.getElementById(paginationId).innerHTML = '';

        var apiUrl;
        switch(tabType) {
            case 'ongoing':
                apiUrl = '${createLink(controller: 'liveMockTests', action: 'getOngoingMockTests')}';
                break;
            case 'upcoming':
                apiUrl = '${createLink(controller: 'liveMockTests', action: 'getUpcomingMockTests')}';
                break;
            case 'completed':
                apiUrl = '${createLink(controller: 'liveMockTests', action: 'getCompletedMockTestsAdmin')}';
                break;
        }

        try {
            // Use fetch with async/await
            const response = await fetch(apiUrl + '?max=' + pageSize + '&page=' + page, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Network response was not ok: ' + response.status);
            }

            const data = await response.json();

            if (data.status === 'success') {
                renderTestsTable(data.data, contentId, tabType);
                renderPagination(data.pagination, paginationId, tabType);
                currentPage[tabType] = page;
            } else {
                showError(contentId, data.message || 'Failed to load tests');
            }
        } catch (error) {
            console.error('Error loading tests:', error);
            showError(contentId, 'Failed to load tests. Please try again.');
        }
    }

    // Function to render tests in table format
    function renderTestsTable(tests, contentId, tabType) {
        var contentElement = document.getElementById(contentId);

        if (!tests || tests.length === 0) {
            contentElement.innerHTML = '<div class="no-tests">' +
                '<i class="fa fa-inbox fa-3x" style="color: #dee2e6; margin-bottom: 15px;"></i>' +
                '<h4>No tests found</h4>' +
                '<p>There are no tests available in this category.</p>' +
                '</div>';
            return;
        }

        var html = '<div class="table-responsive">' +
            '<table class="table">' +
            '<thead>' +
            '<tr>' +
            '<th>Test Name</th>' +
            '<th>Res ID</th>' +
            '<th>Status</th>' +
            '<th>Actions</th>' +
            '</tr>' +
            '</thead>' +
            '<tbody>';

        for (var i = 0; i < tests.length; i++) {
            html += renderTestRow(tests[i], tabType);
        }

        html += '</tbody></table></div>';
        contentElement.innerHTML = html;
    }

    // Function to render individual test row
    function renderTestRow(test, tabType) {
        var statusClass = tabType;
        var statusText = tabType.charAt(0).toUpperCase() + tabType.slice(1);

        return '<tr>' +
            '<td>' +
                '<div class="data-cell">' +
                    '<div class="data-primary">' + (test.resourceName || 'Untitled Test') + '</div>' +
                '</div>' +
            '</td>' +
            '<td>' +
                '<div class="data-cell">' +
                    '<div class="data-primary">' + (test.mcqResId || 'N/A') + '</div>' +
                '</div>' +
            '</td>' +
            '<td><span class="status-badge ' + statusClass + '">' + statusText + '</span></td>' +
            '<td>' +
                '<button class="btn btn-primary btn-sm" onclick="viewTestDetails(' + test.id + ', \'' + encodeURIComponent(JSON.stringify(test)) + '\')" ' +
                'title="View Test Details">' +
                '<i class="fa fa-eye"></i> View Details' +
                '</button>' +
                '<button class="btn btn-success btn-sm ml-2" onclick="viewTestRanks(' + test.mcqResId + ', ' + test.id + ', \'' + (test.resourceName || 'Test') + '\')" ' +
                'title="View Test Ranks">' +
                '<i class="fa fa-trophy"></i> View Ranks' +
                '</button>' +
                (tabType === 'upcoming' ? '<button class="btn-delete btn-sm ml-2" onclick="deleteTest(' + test.id + ', \'' + (test.resourceName || 'this test') + '\')" ' +
                    'title="Delete Test">' +
                    '<i class="fa fa-trash"></i> Delete' +
                    '</button>' :'')+
            '</td>' +
            '</tr>';
    }

    // Function to render pagination
    function renderPagination(pagination, paginationId, tabType) {
        var paginationElement = document.getElementById(paginationId);

        if (!pagination || pagination.totalPages <= 1) {
            paginationElement.innerHTML = '';
            return;
        }

        var html = '<nav aria-label="Page navigation"><ul class="pagination justify-content-center">';

        // Previous button
        if (pagination.currentPage > 1) {
            html += '<li class="page-item">' +
                '<a class="page-link" href="#" onclick="changePage(' + (pagination.currentPage - 1) + ', \'' + tabType + '\')">' +
                '<i class="fa fa-chevron-left"></i> Previous</a></li>';
        }

        // Page numbers
        var startPage = Math.max(1, pagination.currentPage - 2);
        var endPage = Math.min(pagination.totalPages, pagination.currentPage + 2);

        if (startPage > 1) {
            html += '<li class="page-item"><a class="page-link" href="#" onclick="changePage(1, \'' + tabType + '\')">1</a></li>';
            if (startPage > 2) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }

        for (var i = startPage; i <= endPage; i++) {
            var activeClass = (i === pagination.currentPage) ? ' active' : '';
            html += '<li class="page-item' + activeClass + '">' +
                '<a class="page-link" href="#" onclick="changePage(' + i + ', \'' + tabType + '\')">' + i + '</a></li>';
        }

        if (endPage < pagination.totalPages) {
            if (endPage < pagination.totalPages - 1) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
            html += '<li class="page-item"><a class="page-link" href="#" onclick="changePage(' + pagination.totalPages + ', \'' + tabType + '\')">' + pagination.totalPages + '</a></li>';
        }

        // Next button
        if (pagination.currentPage < pagination.totalPages) {
            html += '<li class="page-item">' +
                '<a class="page-link" href="#" onclick="changePage(' + (pagination.currentPage + 1) + ', \'' + tabType + '\')">' +
                'Next <i class="fa fa-chevron-right"></i></a></li>';
        }

        html += '</ul></nav>';
        paginationElement.innerHTML = html;
    }

    // Handle pagination clicks
    window.changePage = function(page, tabType) {
        if (page && tabType) {
            loadTests(tabType, page);
        }
    };

    // Function to view test details in modal
    window.viewTestDetails = function(testId, testDataEncoded) {
        try {
            var testData = JSON.parse(decodeURIComponent(testDataEncoded));

            // Populate modal fields
            document.getElementById('modal-testName').textContent = testData.resourceName || 'N/A';
            document.getElementById('modal-resId').textContent = testData.mcqResId || 'N/A';
            document.getElementById('modal-quizId').textContent = testData.quizId || 'N/A';
            document.getElementById('modal-questions').textContent = (testData.mcqCount || 0) + ' Questions';
            document.getElementById('modal-duration').textContent = (testData.totalTime || 0) + ' minutes';
            document.getElementById('modal-status').textContent = testData.status ? testData.status.charAt(0).toUpperCase() + testData.status.slice(1) : 'N/A';

            document.getElementById('modal-startDate').textContent = testData.testStartDate ? formatDate(new Date(testData.testStartDate)) : 'Not set';
            document.getElementById('modal-endDate').textContent = testData.testEndDate ? formatDate(new Date(testData.testEndDate)) : 'Not set';
            document.getElementById('modal-resultDate').textContent = testData.testResultDate ? formatDate(new Date(testData.testResultDate)) : 'Not set';
            document.getElementById('modal-createdBy').textContent = testData.createdBy || 'Unknown';
            document.getElementById('modal-dateCreated').textContent = testData.dateCreated ? formatDate(new Date(testData.dateCreated)) : 'Not set';

            var languages = [];
            if (testData.language1) languages.push(testData.language1);
            if (testData.language2) languages.push(testData.language2);
            document.getElementById('modal-languages').textContent = languages.length > 0 ? languages.join(', ') : 'Not specified';

            // Show modal
            $('#testDetailsModal').modal('show');
        } catch (error) {
            console.error('Error displaying test details:', error);
            alert('Error displaying test details');
        }
    };

    // Function to view test ranks
    window.viewTestRanks = async function(mcqResId, liveMockMstId, testName) {
        try {
            // Show loading state
            const loadingHtml = '<div class="text-center p-4">' +
                '<i class="fa fa-spinner fa-spin fa-2x text-primary"></i>' +
                '<p class="mt-2">Loading ranks...</p>' +
                '</div>';

            // Create or update ranks modal
            let ranksModal = document.getElementById('ranksModal');
            if (!ranksModal) {
                // Create modal if it doesn't exist
                const modalHtml =
                    '<div class="modal fade" id="ranksModal" tabindex="-1" role="dialog" aria-labelledby="ranksModalLabel" aria-hidden="true">' +
                        '<div class="modal-dialog modal-lg" role="document">' +
                            '<div class="modal-content">' +
                                '<div class="modal-header">' +
                                    '<h5 class="modal-title" id="ranksModalLabel">Test Rankings - ' + testName + '</h5>' +
                                    '<button type="button" class="close" data-dismiss="modal" aria-label="Close">' +
                                        '<span aria-hidden="true">&times;</span>' +
                                    '</button>' +
                                '</div>' +
                                '<div class="modal-body" id="ranksModalBody">' +
                                    loadingHtml +
                                '</div>' +
                                '<div class="modal-footer">' +
                                    '<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>' +
                                '</div>' +
                            '</div>' +
                        '</div>' +
                    '</div>';

                document.body.insertAdjacentHTML('beforeend', modalHtml);
                ranksModal = document.getElementById('ranksModal');
            } else {
                // Update existing modal
                document.getElementById('ranksModalLabel').textContent = 'Test Rankings - ' + testName;
                document.getElementById('ranksModalBody').innerHTML = loadingHtml;
            }

            // Show modal
            $('#ranksModal').modal('show');

            // Fetch ranks data
            const response = await fetch('/prepjoy/getAllRanks?resId=' + mcqResId + '&live_mock_mst_id=' + liveMockMstId, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Network response was not ok: ' + response.status);
            }

            const data = await response.json();

            if (data.status === 'success') {
                displayRanks(data.ranks, data.currentUserRank);
            } else {
                document.getElementById('ranksModalBody').innerHTML =
                    '<div class="alert alert-warning">' + (data.message || 'No ranks available') + '</div>';
            }
        } catch (error) {
            console.error('Error loading ranks:', error);
            document.getElementById('ranksModalBody').innerHTML =
                '<div class="alert alert-danger">Error loading ranks. Please try again.</div>';
        }
    };

    // Function to format time from seconds to m:s format
    function formatTimeFromSeconds(timeInSeconds) {
        if (!timeInSeconds || timeInSeconds === 0) return 'N/A';
        const minutes = Math.floor(timeInSeconds / 60);
        const seconds = Math.floor(timeInSeconds % 60);
        return minutes + ":" + (seconds < 10 ? '0' : '') + seconds;
    }

    // Function to display ranks in the modal
    function displayRanks(ranks, currentUserRank) {
        let html = '';

        if (currentUserRank) {
            const formattedTime = formatTimeFromSeconds(currentUserRank.userTime);
            html += '<div class="alert alert-info mb-3">' +
                '<h6><i class="fa fa-user"></i> Your Rank #' + currentUserRank.rank + '</h6>' +
                '<div class="row">' +
                '<div class="col-md-3"><strong>Rank:</strong> ' + currentUserRank.rank + '</div>' +
                '<div class="col-md-3"><strong>Score:</strong> ' + currentUserRank.score + '</div>' +
                '<div class="col-md-3"><strong>Time:</strong> ' + formattedTime + '</div>' +
                '<div class="col-md-3"><strong>Total Attempts:</strong> ' + (currentUserRank.totalAttempts || 'N/A') + '</div>' +
                '</div>' +
                '</div>';
        }

        if (ranks && ranks.length > 0) {
            html += '<div class="table-responsive">' +
                '<table class="table table-striped">' +
                '<thead class="thead-dark">' +
                '<tr>' +
                '<th>Rank</th>' +
                '<th>Name</th>' +
                '<th>Score</th>' +
                '<th>Time</th>' +
                '</tr>' +
                '</thead>' +
                '<tbody>';

            ranks.forEach(function(rank) {
                const formattedTime = formatTimeFromSeconds(rank.userTime);
                html += '<tr>' +
                    '<td><span class="badge badge-primary">' + rank.rank + '</span></td>' +
                    '<td>' + rank.name + '</td>' +
                    '<td>' + rank.score + '</td>' +
                    '<td>' + formattedTime + '</td>' +
                    '</tr>';
            });

            html += '</tbody></table></div>';
        } else {
            html += '<div class="alert alert-warning">No rankings available for this test.</div>';
        }

        document.getElementById('ranksModalBody').innerHTML = html;
    }

    // Function to delete a test
    window.deleteTest = async function(mockTestId, testName) {
        if (!confirm('Are you sure you want to delete "' + testName + '"? This action cannot be undone.')) {
            return;
        }

        // Find and disable the delete button
        var deleteBtn = event.target.closest('button');
        var originalContent = deleteBtn.innerHTML;
        deleteBtn.disabled = true;
        deleteBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Deleting...';

        try {
            const response = await fetch('${createLink(controller: 'liveMockTests', action: 'deleteMockTest')}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'mockTestId=' + encodeURIComponent(mockTestId)
            });

            const data = await response.json();

            if (data.status === 'success') {
                alert('Test deleted successfully!');
                // Reload current tab to reflect changes
                loadTests(currentTab, currentPage[currentTab]);
            } else {
                alert('Error deleting test: ' + (data.message || 'Unknown error'));
                // Re-enable button on error
                deleteBtn.disabled = false;
                deleteBtn.innerHTML = originalContent;
            }
        } catch (error) {
            console.error('Error deleting test:', error);
            alert('Error deleting test. Please try again.');
            // Re-enable button on error
            deleteBtn.disabled = false;
            deleteBtn.innerHTML = originalContent;
        }
    };

    // Function to show error message
    function showError(contentId, message) {
        var contentElement = document.getElementById(contentId);
        contentElement.innerHTML = '<div class="no-tests">' +
            '<i class="fa fa-exclamation-triangle fa-3x" style="color: #dc3545; margin-bottom: 15px;"></i>' +
            '<h4>Error</h4>' +
            '<p>' + message + '</p>' +
            '<button class="btn btn-primary" onclick="loadTests(\'' + currentTab + '\', ' + currentPage[currentTab] + ')">' +
                '<i class="fa fa-refresh"></i> Retry' +
            '</button>' +
            '</div>';
    }

    // Utility function to format date
    function formatDate(date) {
        if (!date) return 'Not set';
        var options = {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        return date.toLocaleDateString('en-US', options);
    }

    // Helper function to format date (short version)
    function formatDateShort(dateString) {
        if (!dateString) return 'Not set';
        var date = new Date(dateString);
        var options = {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        };
        return date.toLocaleDateString('en-US', options);
    }

    // Helper function to format time only
    function formatTimeOnly(dateString) {
        if (!dateString) return '';
        var date = new Date(dateString);
        var options = {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        };
        return date.toLocaleTimeString('en-US', options);
    }
});
</script>

<g:render template="/${session['entryController']}/footer_new"></g:render>
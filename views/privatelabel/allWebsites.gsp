<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<section class="page-main-wrapper mdl-js mt-5 whitelabel-creation">
    <div class="container">
        <div class="d-flex justify-content-start align-items-center">
            <h4>All Websites</h4>
            <a href="/privatelabel/createNew" class="btn btn-primary btn-primary-modifier shadow-sm px-3 ml-4">Create New</a>
        </div>

        <div class="card card-modifier card-shadow border-0 mt-3 p-4 p-lg-5">
            <table class="table table-striped table-bordered">
                <thead class="bg-primary bg-primary-modifier text-white">
                    <tr>
                        <th>Site Id</th>
                        <th>Client Name</th>
                        <th>Theme Color</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody id="allWebsites">

                </tbody>
            </table>
        </div>

    </div>
</section>

<script>
    function getAllPrivatelabels() {
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="privatelabel" action="getAllPrivatelabels" onSuccess="displayPrivatelabels(data)" />
    }

    function displayPrivatelabels(data) {
        $('.loading-icon').addClass('hidden');
        var htmlStr = "";
        if(data.status == "OK") {
            var websites = data.privatelabels;
            for (let web of websites) {
                htmlStr += "<tr>" +
                    "<td>" + web.siteId + "</td>" +
                    "<td>" + web.clientName + "</td>" +
                    "<td><div class='d-flex align-items-center'><span style=\"width: 30px; height: 30px; display: inline-block; margin-right: 10px; border-radius: 3px; background-color: "+web.themeColor+"\"></span><p>" + web.themeColor + "</p></div></td>" +
                    "<td class='text-center'>" +
                    "<a href='/privatelabel/createNew?mode=edit&siteId="+web.siteId+"' class='btn btn-sm btn-outline-primary'>" +
                    "<i class='material-icons-round' style='font-size: 20px;'>mode_edit</i>" +
                    "</a>" +
                    "</td>" +
                    "</tr>";
            }
        } else {
            htmlStr += "<tr>" +
                "<td colspan='5' class='text-center'>No records found.</td>" +
                "</tr>";
        }
        document.getElementById("allWebsites").innerHTML= htmlStr;
    }

    getAllPrivatelabels();


</script>


<g:render template="/${session['entryController']}/footer_new"></g:render>
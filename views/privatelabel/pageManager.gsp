<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/privatelabel/navheader_new"></g:render>

<style>
    #pageStatus{
        border: 1px solid rgba(0,0,0,0.4);
        border-radius: 4px;
        width: 200px;
        padding: 5px;
        cursor: pointer;
    }
</style>

<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<section class="page-main-wrapper mdl-js mt-5 whitelabel-pages-creation">
    <div class="container">
        <div class="d-flex justify-content-start align-items-center">
            <h4 class="mb-0">Page Manager</h4>
            <a href="/privatelabel/customPageCreation?mode=create" class="btn btn-primary btn-primary-modifier shadow-sm px-3 ml-4 d-flex align-items-center"><i class="material-icons-round">add</i> Add New Page</a>
        </div>

        <div class="card card-modifier card-shadow border-0 mt-3">
            <table class="table table-striped table-bordered">
                <thead class="bg-primary bg-primary-modifier text-white">
                <tr class="text-center">
                    <th width="30%">Page Name</th>
                    <th width="25%">Action</th>
                    <th width="45%">Link</th>
                </tr>
                </thead>
                <tbody id="pagesList">
                </tbody>
            </table>
        </div>

    </div>
</section>

<g:render template="/privatelabel/footer_new"></g:render>

<script>
    function getAllPages(){
        $('.loading-icon').removeClass('hidden');
        var siteId = ${session['siteId']};
        siteId = parseInt(siteId);
        <g:remoteFunction controller="pages" action="getAllPagesList" params="'siteId='+siteId" onSuccess="updatePages(data)" />
    }
    getAllPages();
    function updatePages(data){
        var pages = data.pageList;
        var pageHtml = "";
        var publishedStatus = 'unpublished'
        if (pages.length>0){
            pages.map(item=>{
                if (item.status!=null && item.status!='null'){
                    publishedStatus = item.status
                }
                pageHtml+= "<tr class='text-center'>"+
                    "<td width='30%'>"+item.name+"</td>";
                if (item.status == 'unpublished' || item.status == null || item.status=='null'){
                    pageHtml+= "<td width='25%'><button id='pageStatus' data-status='publish' onclick=\"updateStatus('"+item.status+"',"+item.id+")\">Publish</button></td>";
                }else if (item.status == 'published'){
                    pageHtml+= "<td width='25%'><button id='pageStatus' data-status='unpublish' onclick=\"updateStatus('"+item.status+"',"+item.id+")\">Unpublish</button></td>";
                }

                pageHtml+="<td width='45%'>" +
                    "<a href='/${session['siteName']}/page/"+item.link+"?pageId="+item.id+"'>View Page</a>" +
                    "</td>"+
                    "</tr>";
            })
        }else {
            pageHtml +="<tr>" +
                    "<td</td>" +
                    "<td>No Pages Found</td>" +
                    "<td></td>" +
                "</tr>"
        }

        document.getElementById('pagesList').innerHTML = pageHtml;
        $('.loading-icon').addClass('hidden');
    }

    function updateStatus(statusVal,pageId){
        $('.loading-icon').removeClass('hidden')
        var status = statusVal;
        if (status==null || status=='null'){
            status = 'unpublished';
        }
        <g:remoteFunction controller="pages" action="publishUnpublishSite" params="'pageStatus='+status+'&pageId='+pageId" onSuccess="statusChanged(data)"/>
    }

    function statusChanged(data){
        $('.loading-icon').addClass('hidden')
        window.location.reload();
    }

</script>
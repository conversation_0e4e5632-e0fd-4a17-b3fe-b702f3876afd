<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/books/navheader_new"></g:render>
<asset:stylesheet href="groups/groupsStyle.css" async="true"/>
<style>
.notification{
    position: relative;
}
.notificationCount{
    position: absolute;
    left: 70%;
    top: 10%;
    background: red;
    width: 15px;
    height: 15px;
    text-align: center;
    border-radius: 50%;
    color: #fff;
    font-size: 10px;
}
.post-card{
    transition: all .2s ease;
}

.post-card:hover{
    transform: scale(1.05);

}
.backgrouncard {
    height: 71px;
    width: 90%;
    background-size: cover;
    background-repeat: no-repeat;
    margin:10px;
    border-radius:5px;
}
</style>
<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<section class="page-main-wrapper mdl-js pb-5 pt-0 db-main">
    <!-- General -->
    <div class="container pt-4 pt-md-5 mt-3 mt-md-0">

        <!-- Mobile Top Menus -->


        <div class="card card-modifier card-shadow border-0 db-common-info db-general col-12 py-4 px-2 px-md-4">
            <div class="row mx-0">

                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/intelligence/switchUser?demoUser=demoAdmin">
                        <asset:image src="/ws/icon-db-my-books.svg"></asset:image>
                        <p>Admin</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/intelligence/switchUser?demoUser=demoTeacher">
                        <asset:image src="/ws/icon-db-publishing-desk.svg"></asset:image>
                        <p>Teacher</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/intelligence/switchUser?demoUser=demoStudent">
                        <asset:image src="/ws/icon-db-current-affairs.svg"></asset:image>
                        <p>Student</p>
                    </a>
                </div>


            </div>
        </div>
    </div>






</section>

<g:render template="/books/footer_new"></g:render>



</body>
</html>

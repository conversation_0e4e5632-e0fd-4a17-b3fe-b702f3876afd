<div class="accordion-container">
    <p class="faqTitle">Frequently Asked Questions (FAQs)</p>
    <div id="accordion-items">
        <div class="accordion-item">
            <button class="accordion-header">
                <p>Are all the mock tests free here?</p><span class="icon">+</span>
            </button>
            <div class="accordion-content">
                <p>Yes, mock tests, daily current affairs are free on Wonderslate, Prepjoy app and website.</p>
            </div>
        </div>
        <div class="accordion-item">
            <button class="accordion-header">
                <p>Are all the online test series in English only?</p> <span class="icon">+</span>
            </button>
            <div class="accordion-content">
                <p>No, all the online mock tests are available in English as well as Hindi.</p>
            </div>
        </div>
        <div class="accordion-item">
            <button class="accordion-header">
                <p>How often should I take mock tests?</p> <span class="icon">+</span>
            </button>
            <div class="accordion-content">
                <p>Regular practice is key, but avoid overdoing it. Aim to take daily tests, with spaced-out practices earlier on. Adjust based on your progress and comfort level.</p>
            </div>
        </div>
        <div class="accordion-item">
            <button class="accordion-header">
                <p>How do I analyze my mock test results effectively?</p> <span class="icon">+</span>
            </button>
            <div class="accordion-content">
                <p>Don't just see your score. Identify your strengths and weaknesses (topic-wise and question types). Review incorrect answers thoroughly, understand the concepts, and learn from them. Utilize detailed analysis reports offered by Wonderslate.</p>
            </div>
        </div>
        <div class="accordion-item">
            <button class="accordion-header">
                <p>Can I review the questions and answers after the test?</p> <span class="icon">+</span>
            </button>
            <div class="accordion-content">
                <p>Yes, you can review the questions and answers immediately after the test.</p>
            </div>
        </div>
        <div class="accordion-item">
            <button class="accordion-header">
                <p>How can I improve my accuracy in mock tests?</p> <span class="icon">+</span>
            </button>
            <div class="accordion-content">
                <p>Focus on understanding concepts, not just memorizing answers. Practice different question formats, actively learn from explanations, and identify your knowledge gaps. Address these gaps by referring to study materials or seeking help from teachers/mentors.</p>
            </div>
        </div>
        <div class="accordion-item">
            <button class="accordion-header">
                <p>What are the benefits of taking mock tests?</p> <span class="icon">+</span>
            </button>
            <div class="accordion-content">
                <p>Mock tests offer numerous benefits like familiarizing yourself with the exam format, managing time effectively, identifying your strengths and weaknesses, and practicing test-taking strategies.</p>
            </div>
        </div>
    </div>
</div>
<div id="infoSec" style="display: none;margin-top: 2rem">
    <p class="faqTitle">About ${title}</p>
    <div id="desc"></div>
</div>
<div id="temp" style="display:none;">
</div>
<ul id="tempUL" style="display:none;">

</ul>
<script>
    const id = '${params.dailyTestId}';
    if (id !='' && id !=undefined && id !=null && (syllabus!="")){
        <g:remoteFunction controller="wsshop" action="getSyllabusDtl"  onSuccess='initializeInfo(data);'
    params="'syllabus='+syllabus"/>
    }else{
        openCloseAcc();
    }

    function initializeInfo(data){
        if (data.info.faq){
            document.getElementById('temp').innerHTML = data.info.faq;
            faqItems = document.querySelectorAll('#temp ul li');
            if(faqItems.length==0){
                let ulelms  = data.info.faq.split("</p>");
                for (let k=0;k<ulelms.length;k++){
                    document.getElementById('tempUL').innerHTML +="<li>"+ulelms[k].concat('</p>')+"</li>";
                }
                faqItems  = document.querySelectorAll('#tempUL li')
            }
            const accordionItems = document.getElementById('accordion-items');
            const infoSec = document.getElementById('infoSec');
            const desc = document.getElementById('desc');
            let faqHTML = "";
            faqItems.forEach(item=>{
                if (item.innerText!=""){
                    faqHTML+="<div class='accordion-item'>"+
                        "<button class='accordion-header'>";
                    if(item.querySelector("strong")){
                        faqHTML+="<p>"+item.querySelector("strong").innerHTML+"</p> <span class='icon'>+</span>";
                        faqHTML+="</button>";
                        item.querySelector("strong").innerHTML = "";
                    }
                    faqHTML+="<div class='accordion-content'>"+
                        "<p>"+item.innerText+"</p>"+
                        "</div>"+
                        "</div>";
                }
            })
            if (data.info.introduction !="" && data.info.introduction !=null && data.info.introduction !=undefined){
                desc.innerHTML = data.info.introduction.replaceAll("\\n","").replaceAll("\\t","");
                infoSec.style.display = 'block';
            }
            accordionItems.innerHTML = faqHTML;
        }
        openCloseAcc();
    }

    function openCloseAcc(){
        document.querySelectorAll('.accordion-header').forEach(button => {
            button.addEventListener('click', () => {
                const accordionContent = button.nextElementSibling;

                button.classList.toggle('active');

                if (button.classList.contains('active')) {
                    accordionContent.style.maxHeight = accordionContent.scrollHeight + 'px';
                } else {
                    accordionContent.style.maxHeight = 0;
                }

                document.querySelectorAll('.accordion-header').forEach(otherButton => {
                    if (otherButton !== button) {
                        otherButton.classList.remove('active');
                        otherButton.nextElementSibling.style.maxHeight = 0;
                    }
                });
            });
        });
    }
</script>
<div class="mockTest__examCards" id="examGroupCards">
</div>
<script>

    var examsTypes = JSON.parse("${dailyTestTypes}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'));
    const examGroupCards = document.getElementById('examGroupCards');
    let examCardHTML = "";
    examsTypes.forEach(exam=>{
        const borderColor = getRandomHexColor();
        examCardHTML += "<div class='mockTest__examCards-card examCard' data-examName='"+exam.examGroup+"' style='border-left-color: "+borderColor+"'>"+
                            "<div class='examCard__examName'>"+
                                "<div class='examCard__img'>"+
                                    "<img src='/mocktests/showImage?fileName="+exam.imageSrc+"' alt='"+exam.examGroup+"'>"+
                                "</div>"+
                                "<p>"+exam.examGroup+"</p>"+
                            "</div>"+
                            "<i class='fa-solid fa-chevron-right examCard__arrow'></i>"+
                        "</div>";
        <%if(testsList!=null){%>
        if(exam.examGroup.replaceAll(' ','-').toLowerCase()=="${examGroup}"){
            document.getElementById("examDescription").innerHTML = "<span class='faqTitle'> About "+exam.examGroup+"</span>" +
                "<br>"+decodeUnicode(exam.description.replaceAll("&#92;","\\").replaceAll("\\n","").replaceAll("\\t",""));
        }
        <%}%>

    })
    examGroupCards.innerHTML = examCardHTML;
    const examItems = document.querySelectorAll('.examCard');
    examItems.forEach(examItem=>{
        examItem.addEventListener('click',(e)=>{
            var link = "/"+examItem.getAttribute('data-examName').toLowerCase().replaceAll(' ','-')+"/mocktests";
            setTimeout(()=>{
                window.open(link, '_blank');
            })
        })
    })
    function getRandomHexColor() {
        return '#' + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0');
    }

    function decodeUnicode(str) {
        return str.replace(/\\u[\dA-Fa-f]{4}/g, match => {
            return String.fromCharCode(parseInt(match.substring(2), 16));
        });
    }
</script>

<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/books/navheader_new"></g:render>

<link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/lightgallery@2.0.0-beta.3/css/lightgallery.css'>
<link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/lightgallery@2.0.0-beta.3/css/lg-zoom.css'>
<link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/justifiedGallery@3.8.1/dist/css/justifiedGallery.css'>
<link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/lightgallery@2.0.0-beta.3/css/lg-thumbnail.css'>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
<link rel="stylesheet" href="/assets/prepJoy/prepjoyWebsites/instituteLeaderBoard.css">

<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>

<section class="page-main-wrapper mdl-js institute-homepage">
    <g:render template="/institute/instituteBanners"></g:render>

    <div class="container main-content mt-5">
    <%if("true".equals(institute.shopEnabled)){%>
        <div class="ebooks-categories d-flex flex-wrap justify-content-center justify-content-md-between align-items-center">
            <div class="text-center text-md-left">
                <h2>eBooks Store & Library</h2>
                <p>Use eBooks save trees <span class="tree_emoji">&#127795;</span></p>
            </div>
            <div class="d-flex flex-wrap justify-content-center align-items-center buttons-wrapper">
                <sec:ifLoggedIn>
                <a href="/wsLibrary/myLibrary?instituteId=${institute.id}" class="btn btn-lg btn-warning border-0 mx-3 mt-4 mt-md-0"><span>My Library</span></a>
                </sec:ifLoggedIn>
                <sec:ifNotLoggedIn>
                <a href="javascript:loginOpen();" class="btn btn-lg btn-warning border-0 mx-3 mt-4 mt-md-0"><span>Login</span></a>
                <a href="javascript:signupModal();" class="btn btn-lg btn-warning border-0 mx-3 mt-4 mt-md-0"><span>Sign-up</span></a>
                </sec:ifNotLoggedIn>
            </div>
        </div>

        <div class="exploring-ebooks mt-5">
            <h4 class="mb-3">Explore eBooks</h4>
            <div id="instituteGrades" class="pt-3 mb-5">

            </div>
            <div id="instituteCategories" class="row mb-5">

            </div>
        </div>

        <div class="videos-wrapper">
            <div class="d-flex flex-wrap align-items-center">
                <div class="col-md-6 mb-5">
                    <h4 class="mb-3">For Teachers</h4>
                    <iframe width="100%" height="295" src="https://www.youtube.com/embed/zM3l3pRkWbA?controls=0" title="Wonderslate For Teachers" allow="accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                </div>
                <div class="col-md-6 mb-5">
                    <h4 class="mb-3">For Students</h4>
                    <iframe width="100%" height="295" src="https://www.youtube.com/embed/yRAOFjaI71Y?controls=0" title="Wonderslate - Curriculum-based Smart Content" allow="accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                </div>
            </div>
        </div>


        <div class="d-flex flex-wrap justify-content-center align-items-center buttons-wrapper mb-5">
            <a href="javascript:downloadApp();" class="btn btn-lg btn-warning border-0 mx-3 mt-4 mt-md-0"><span>Download App</span></a>
            <a href="/ebooks" class="btn btn-lg btn-warning border-0 mx-3 mt-4 mt-md-0"><span>eBooks Store</span></a>
        </div>
    <%}%>

    </div>
    <%if("true".equals(institute.leaderBoardEnabled)){%>
    <section class="leaderBoard bg-white">
        <div class="container leaderBoard__title">
            <h2>Leaderboard</h2>
        </div>
        <div class="container leaderBoard__wrapper mt-2 mb-5">
            <div class="leaderBoard__wrapper-contents mt-3">
                <div class="leaderBoard__wrapper-contents__tabs mb-4">
                    <button class="btn inst__lbTab lbTab activeTab">Institute </button>
                    <button class="btn allInd__lbTab lbTab">All India </button>
                </div>
                <div class="leaderBoard__wrapper-contents__datesTab">
                    <button class="dailyBtn activeDate">Daily</button>
                    <button class="weeklyBtn">Weekly</button>
                    <button class="monthlyBtn">Monthly</button>
                </div>
                <div class="leaderBoard__wrapper-contents__list" >
                    <div class="leaderBoard__wrapper-contents__list-wrapper" >
                        <table>
                            <thead>
                                <tr>
                                    <th>Rank</th>
                                    <th>Name</th>
                                    <th style="text-align: end">Points</th>
                                </tr>
                            </thead>
                            <tbody id="leaderBoardList"></tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="leaderBoard__wrapper-navigations mt-3">
                <h5 class="mb-3">Do you want to join the Leaderboard?</h5>
                <div class="leaderBoard__wrapper-navigations__cards">
                    <div class="leaderBoard__wrapper-navigations__card mb-3">
                        <a href="/prepjoy/dailyTest" >
                            <div class="card__sec-1">
                                <h4>Daily Test</h4>
                                <p>Competitive Exams, General, Engineering, etc. </p>
                            </div>
                            <button><i class="fa-solid fa-chevron-right"></i></button>
                        </a>
                    </div>
                    <div class="leaderBoard__wrapper-navigations__card mb-3">
                        <sec:ifLoggedIn>
                            <a href="/prepjoy/quizAnalytics" class="leaderBoard__wrapper-navigations__card-link">
                        </sec:ifLoggedIn>
                        <sec:ifNotLoggedIn>
                            <a href="javascript:loginOpen();" >
                        </sec:ifNotLoggedIn>
                            <div class="card__sec-1">
                                <h4>Analytics</h4>
                                <p>All your quiz analytics in one place. </p>
                            </div>
                            <button><i class="fa-solid fa-chevron-right"></i></button>
                        </a>
                    </div>
                    <div class="leaderBoard__wrapper-navigations__card mb-3">
                        <sec:ifLoggedIn>
                            <a href="/books/myActivity" class="leaderBoard__wrapper-navigations__card-link">
                        </sec:ifLoggedIn>
                        <sec:ifNotLoggedIn>
                            <a href="javascript:loginOpen();" class="leaderBoard__wrapper-navigations__card-link">
                        </sec:ifNotLoggedIn>
                            <div class="card__sec-1">
                                <h4>Learning History</h4>
                                <p>All your learning history here.</p>
                            </div>
                            <button><i class="fa-solid fa-chevron-right"></i></button>
                        </a>
                    </div>
                    <div class="leaderBoard__wrapper-navigations__card mb-3">
                        <a href="/ebooks" class="leaderBoard__wrapper-navigations__card-link">
                            <div class="card__sec-1">
                                <h4>eBooks</h4>
                                <p>World's No 1 smart eBooks store.</p>
                            </div>
                            <button><i class="fa-solid fa-chevron-right"></i></button>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <%}%>
    <section class="dailyTests">
        <div class="container dailyTests__title">
            <h2>Popular Online Tests</h2>
        </div>
        <div class="dailyTests__list">
            <div class="container">
                <div class="dailyTests__list-cards" id="dailyTestsDisplay">

                </div>
            </div>
        </div>
    </section>

    <%if(institute.aboutUs != null && institute.aboutUs != '') {%>
    <div class="container-fluid bg-white">
        <div class="container ckeditor-content py-5">
            <div id="sectionOne" class="section-one">

            </div>
        </div>
    </div>
    <input value="${institute.aboutUs}" id="sectionOneHidden" hidden class="form-control hidden">
    <%}%>

    <%if(institute.contactDetails != null && institute.contactDetails != '') {%>
    <div class="container-fluid ">
        <div class="container ckeditor-content py-5">
            <div id="sectionTwo" class="section-two">

            </div>
        </div>
    </div>
    <input value="${institute.contactDetails}" id="sectionTwoHidden" hidden class="form-control hidden">
    <%}%>

    <%if(institute.section3 != null && institute.section3 != '') {%>
    <div class="container-fluid bg-white">
        <div class="container ckeditor-content py-5">
            <div id="sectionThree" class="section-three">

            </div>
        </div>
    </div>
    <input value="${institute.section3}" id="sectionThreeHidden" hidden class="form-control hidden">
    <%}%>

    <div class="container photo-gallery py-5">
        <h4 class="mb-3">Gallery</h4>
        <div class="gallery-container" id="photoGallery">

        </div>
    </div>

    <%if(institute.section4 != null && institute.section4 != '') {%>
    <div class="container-fluid">
        <div class="container ckeditor-content py-5">
            <div id="sectionFour" class="section-four">

            </div>
        </div>
    </div>
    <input value="${institute.section4}" id="sectionFourHidden" hidden class="form-control hidden">
    <%}%>

</section>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<g:render template="/books/footer_new"></g:render>
<script src='https://cdn.jsdelivr.net/npm/lightgallery@2.0.0-beta.3/lightgallery.umd.js'></script>
<script src='https://cdn.jsdelivr.net/npm/lightgallery@2.0.0-beta.3/plugins/zoom/lg-zoom.umd.js'></script>
<script src='https://cdn.jsdelivr.net/npm/justifiedGallery@3.8.1/dist/js/jquery.justifiedGallery.js'></script>
<script src='https://cdn.jsdelivr.net/npm/lightgallery@2.0.0-beta.3/plugins/thumbnail/lg-thumbnail.umd.js'></script>
<script>
    var showGrades = "";
    var showCategories = "";
    var instituteGrades = ["Class 1", "Class 2", "Class 3", "Class 4", "Class 5", "Class 6", "Class 7", "Class 8", "Class 9", "Class 10", "Class 11", "Class 12"];
    var instituteCategories = ["Olympiad", "Karnataka CET", "Medical Entrances", "Engineering Entrances", "Fiction"];



    // Showing Institute Categories
    for (var j = 0; j < instituteCategories.length; j++) {
        showCategories += "<div class='category-info col-md-4 col-lg-3'><a href='#' class='category-link'>"+instituteCategories[j]+"</a></div>";
    }
 //   document.getElementById("instituteCategories").innerHTML= showCategories;

    function downloadApp() {
        var userAgent = navigator.userAgent || navigator.vendor || window.opera;
        if (/iPad|iPhone|iPod/.test(userAgent)) {
            window.open('https://apps.apple.com/in/app/wonderslate/id1438381878', '_blank');
        } else {
            window.open('https://play.google.com/store/apps/details?id=com.wonderslate.wonderpublish&hl=en&gl=US', '_blank');
        }
    }

    function getGalleryImages(instituteId) {
        $(".loading-icon").removeClass("hidden");
        <g:remoteFunction controller="institute" action="getInstituteGalleryImage" params="'instituteId='+instituteId" onSuccess="showGalleryImages(data);"/>
    }

    function showGalleryImages(data) {
        if(data.status=="OK") {
            var galleryImages = data.imageGalleryList.reverse();
            $.each(galleryImages, function (i, v) {
                var item = v;
                var htmlStr = '';
                var galleryImageUrl = "/institute/showInstituteGalleryImage?instituteId="+instituteId+"&id=" + v.id + "&fileName=" + v.imageName;

                htmlStr += "<a class='gallery-item' data-src='"+galleryImageUrl+"'>" +
                    "<img class='img-responsive' src='"+galleryImageUrl+"'>" +
                    "</a>";

                $('#photoGallery').append(htmlStr);
            });
            $('#photoGallery').justifiedGallery({
                captions: false,
                //lastRow: "hide",
                rowHeight: 180,
                margins: 5
            }).on("jg.complete", function () {
                window.lightGallery(document.getElementById("photoGallery"), {
                    autoplayFirstVideo: false,
                    pager: false,
                    galleryId: "nature",
                    download: false,
                    plugins: [lgZoom, lgThumbnail],
                    mobileSettings: {
                        controls: false,
                        //showCloseIcon: false,
                        download: false,
                        rotate: false
                    }
                });
            });
        } else if(data.status=="Nothing Present") {
            $(".photo-gallery").hide();
        }
    }

    getGalleryImages(instituteId);

    $(document).ready(function() {
        if($('#sectionOneHidden').val()!="" && $('#sectionOneHidden').val()!=null) {
            document.getElementById("sectionOne").innerHTML = $('#sectionOneHidden').val();
        }
        if($('#sectionTwoHidden').val()!="" && $('#sectionTwoHidden').val()!=null) {
            document.getElementById("sectionTwo").innerHTML = $('#sectionTwoHidden').val();
        }
        if($('#sectionThreeHidden').val()!="" && $('#sectionThreeHidden').val()!=null) {
            document.getElementById("sectionThree").innerHTML = $('#sectionThreeHidden').val();
        }
        if($('#sectionFourHidden').val()!="" && $('#sectionFourHidden').val()!=null) {
            document.getElementById("sectionFour").innerHTML = $('#sectionFourHidden').val();
        }
    });
    <%if(session.getAttribute('instituteUrlName')!=null){%>
    document.cookie = "instituteUrlName" + "=" + "${session.getAttribute('instituteUrlName')}" + ";path=/";
    <%}%>

    <%if("true".equals(institute.shopEnabled)&&institute.level!=null&&institute.syllabus!=null&&!"".equals(institute.syllabus)&&(institute.syllabus.indexOf(',')==-1)){%>
    var level = encodeURIComponent('${institute.level}'.replace('&amp;','&'));
    var syllabus = encodeURIComponent('${institute.syllabus}'.replace('&amp;','&'));

    <g:remoteFunction controller="institute" action="getGradesList" params="'level='+level+'&syllabus='+syllabus" onSuccess="displayGrades(data);"/>

    function displayGrades(data) {
        var level = encodeURIComponent('${institute.level}'.replace('&amp;', '&'));
        level = replaceAll(decodeURI(level), ' ', '-');
        var syllabus = encodeURIComponent('${institute.syllabus}'.replace('&amp;', '&'));
        syllabus = replaceAll(decodeURI(syllabus), ' ', '-');
        var preUniversity = '${institute.preUniversity}';

        var grades = JSON.parse(data.grades);
        var classPrefix = "";
        if ("School" == level) classPrefix = "Class ";
        for (var i = 0; i < grades.length; i++) {
            if("School"==level&&"true"==preUniversity&&grades[i].grade<11) continue;
            showGrades += "<a href='/store?source=institute&id=${institute.id}&level=" + level + "&syllabus=" + syllabus + "&grade=" + replaceAll(grades[i].grade, ' ', '-') + "' class='grade-link' target='_blank'>" + classPrefix + grades[i].grade + "</a>";
        }

        //add additional things for schools
        if("School"==level&&"true"!=preUniversity){
            showGrades += "<a href='/store?source=institute&id=${institute.id}&level=" + level + "&syllabus=NTSE' class='grade-link' target='_blank'>NTSE</a>";
            showGrades += "<a href='/store?source=institute&id=${institute.id}&level=" + level + "&syllabus=Olympiad' class='grade-link' target='_blank'>Olympiad</a>";

        }
        showGrades += "<a href='/store?source=institute&id=${institute.id}&level=General' class='grade-link' target='_blank'>General</a>";
        showGrades += "<a href='/store?source=institute&id=${institute.id}&level=Engineering-Entrances' class='grade-link' target='_blank'>Engineering Entrances</a>";
        showGrades += "<a href='/store?source=institute&id=${institute.id}&level=Medical-Entrances' class='grade-link' target='_blank'>Medical Entrances</a>";
        showGrades += "<a href='/store?source=institute&id=${institute.id}&level=Competitive-Exams' class='grade-link' target='_blank'>Competitive Exams</a>";


        document.getElementById("instituteGrades").innerHTML = showGrades;

    }
   <%}else if("true".equals(institute.shopEnabled)&&institute.level!=null){%>
    var level = encodeURIComponent('${institute.level}'.replace('&amp;','&'));
    <g:remoteFunction controller="institute" action="getSyllabusList" params="'level='+level" onSuccess="displaySyllabus(data);"/>

    function displaySyllabus(data){
        var syllabus = JSON.parse(data.syllabus);
        var level = encodeURIComponent('${institute.level}'.replace('&amp;','&'));
        level = replaceAll(decodeURI(level),' ','-');
        var skip=false;
        for (var i = 0; i < syllabus.length; i++) {
            skip=false;
           <% if(institute.syllabus!=null&&!"".equals(institute.syllabus)&&(institute.syllabus.indexOf(',')>-1)){%>
            var instSyllabus = '${institute.syllabus}';
                if(instSyllabus.indexOf(syllabus[i].syllabus)==-1) skip=true
           <% }%>
            if(!skip) showGrades += "<a href='/store?source=institute&id=${institute.id}&level="+level+"&syllabus="+replaceAll(syllabus[i].syllabus,' ','-')+"' class='grade-link' target='_blank'>"+syllabus[i].syllabus+"</a>";
        }
        showGrades += "<a href='/store?source=institute&id=${institute.id}&level=General' class='grade-link' target='_blank'>General</a>";
        showGrades += "<a href='/store?source=institute&id=${institute.id}&level=Competitive-Exams' class='grade-link' target='_blank'>Competitive Exams</a>";

        document.getElementById("instituteGrades").innerHTML= showGrades;

    }
    <%}%>
    <%if("true".equals(institute.leaderBoardEnabled)){%>
    //LEADERBOARD FUNCTIONALITY
    var currentDate = new Date().toISOString().split("T")[0];
    var instituteId = "<%= session["instituteId"] %>";
    var instLBTab = document.querySelector('.inst__lbTab');
    var allIndLBTab = document.querySelector('.allInd__lbTab');
    var dailyBtn = document.querySelector('.dailyBtn');
    var weeklyBtn = document.querySelector('.weeklyBtn');
    var monthlyBtn = document.querySelector('.monthlyBtn');
    var leaderBoardList = document.getElementById('leaderBoardList');
    var institutionRanks="";
    var allIndiaRank="";
    var instituteTab=true;

    function getDailyRank(){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="prepjoy" action="getPrepjoyDailyRanksInstitute" params="'rankDate='+currentDate+'&instituteId='+instituteId" onSuccess="rankUI(data)" />
    }
    function getWeeklyRank(){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="prepjoy" action="getPrepjoyWeeklyRanksInstitute" params="'rankDate='+currentDate+'&instituteId='+instituteId" onSuccess="rankUI(data)" />
    }
    function getMonthlyRank(){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="prepjoy" action="getPrepjoyMonthlyRanksInstitute" params="'rankDate='+currentDate+'&instituteId='+instituteId" onSuccess="rankUI(data)" />
    }

    getDailyRank();

    dailyBtn.addEventListener('click',function (){
        dailyBtn.classList.add('activeDate');
        weeklyBtn.classList.remove('activeDate');
        monthlyBtn.classList.remove('activeDate');
        instituteTab = instLBTab.classList.contains('activeTab');
        getDailyRank();
    })
    weeklyBtn.addEventListener('click',function (){
        weeklyBtn.classList.add('activeDate');
        monthlyBtn.classList.remove('activeDate');
        dailyBtn.classList.remove('activeDate');
        instituteTab = instLBTab.classList.contains('activeTab');
        getWeeklyRank();
    })
    monthlyBtn.addEventListener('click',function (){
        monthlyBtn.classList.add('activeDate');
        dailyBtn.classList.remove('activeDate');
        weeklyBtn.classList.remove('activeDate');
        instituteTab = instLBTab.classList.contains('activeTab');
        getMonthlyRank();
    })

    instLBTab.addEventListener('click',function (){
        instLBTab.classList.add('activeTab');
        allIndLBTab.classList.remove('activeTab');
        updateRankUI(institutionRanks)
    });

    allIndLBTab.addEventListener('click',function (){
        instLBTab.classList.remove('activeTab');
        allIndLBTab.classList.add('activeTab');
        updateRankUI(allIndiaRank)
    })

    function rankUI(data){
        $('.loading-icon').addClass('hidden');

        if (data.institutionranks!='No Ranks' ){
            institutionRanks = JSON.parse(data.institutionranks);
        }else{
            institutionRanks="";
        }
        if (data.mainranks !="No Ranks"){
            allIndiaRank = JSON.parse(data.mainranks);
        }else{
            allIndiaRank="";
        }
        var showRank="";
        instituteTab ? showRank = institutionRanks:showRank = allIndiaRank;
        updateRankUI(showRank);
    }

    function updateRankUI(showRank){
        var lbHtml = "";

        if(showRank !=""){
            for(var i=0;i<showRank.length;i++){
                lbHtml +="<tr>"+
                    "<td style='padding-left:18px '><strong>"+showRank[i].rank+"</strong></td>"+
                    "<td class='user-info'>"+
                        "<div class='lb-userImg'>";
                        if(showRank[i].profilePic !=null && showRank[i].profilePic !="" && showRank[i].profilePic != undefined){
                            lbHtml +="<img src='/funlearn/showProfileImage?id="+showRank[i].userId+"&fileName="+showRank[i].profilePic+"&type=user&imgType=passport'/>";
                        }else {
                            lbHtml += "<img src='${assetPath(src:'landingpageImages/img_avatar3.png')}'/>";
                        }
                lbHtml +="</div>"+
                    "<div>"+
                        "<p class='name'>"+showRank[i].name+"</p>";
                        if (showRank[i].state!="" && showRank[i].state!=undefined){
                            lbHtml +="<p class='place' >"+showRank[i].state+"</p>";
                        }
                lbHtml +="</div>"+
                    "</td>"+
                    "<td style='text-align: end;padding-right:25px '><strong>"+showRank[i].userPoints+"</strong></td>"+
                "</tr>";
            }
        }else{
            lbHtml +="<tr>"+
                    "<td></td>"+
                    "<td>No users found</td>" +
                "</tr>";
        }

        leaderBoardList.innerHTML = lbHtml;
    }
    <%}%>
    function getDailyTestTypes(){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="prepjoy" action="getDailyTestTypesForSite" params="'siteId='+siteId" onSuccess="displayDailyTests(data)" />
    }
    getDailyTestTypes();



    function displayDailyTests(data) {

        var examGroup = JSON.parse(data.dailyTestTypes);

        var htmlStr = "";
        var exam;
        for (var e=0;e<examGroup.length;e++) {
            exam = replaceAll(examGroup[e].examGroup,' - ',' ');
            exam = replaceAll(exam,'-','');
            exam = replaceAll(exam,' ','-');
            exam = exam.toLowerCase();
           // htmlStr += "<a href='/"+exam+"/online-test' target='blank'>"+examGroup[e].examGroup+"</a>&nbsp;&nbsp;";
            htmlStr +=" <a href='/"+exam+"/online-test' target='blank' class='dailyTests__list-cards__card'>\n" + examGroup[e].examGroup +
            "                        <button><i class='fa-solid fa-chevron-right'></i></button>\n" +
            "                    </a>";
        }
        document.getElementById("dailyTestsDisplay").innerHTML=htmlStr;

    }



</script>

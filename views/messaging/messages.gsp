<g:render template="/funlearn/navheader"></g:render>
<asset:stylesheet href="speechbubbles.css"/>
<asset:stylesheet href="messages.css"/>
<div class="container fill">
    <div class="row chat-wrap">

        <!-- Contacts & Conversations -->
        <div class="col-sm-4 panel-wrap lightishblue" id="panelwrap">

            <!-- Overlay Menu / Contacts -->
            <div class="col-sm-12 section-wrap collapse" id="contacts">

                <!--Header-->
                <div class="row header-wrap">
                    <div class="chat-header col-sm-12">
                        <h4>Select a Contact</h4>
                        <div class="header-button">
                            <a class="btn pull-right" href="javascript:showConversationList();">
                                <i class="fa fa-close"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!--Contacts-->
                <div class="row content-wrap" id="contactList">


                </div>

            </div>

            <!--Left Menu / Conversation List-->
            <div class="col-sm-12 section-wrap" id="conversation">

                <!--Header-->
                <div class="row header-wrap">
                    <div class="chat-header col-sm-12">
                        <h4 id="username"></h4>
                        <div class="header-button">
                            <a class="btn pull-right" href="javascript:showContactList();">
                                <i class="fa fa-pencil-square-o fa-lg"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!--Conversations-->
                <div class="row content-wrap" id="conversationList">
                    

                </div>

            </div>

        </div>

        <!-- Messages & Info -->
        <div class="col-sm-8 panel-wrap" id="messageswrap">

            <!--Main Content / Message List-->
            <div class="col-sm-9 section-wrap" id="MessagesInfo" >

                <!--Header-->
                <div class="row header-wrap">
                    <div class="chat-header col-md-12 col-sm-12">
                        <div class="row"><br>
                            <div class="col-md-4 col-xs-6" id="showConversation"></div>
                            <div class="col-md-6 col-xs-6" id="chatName"></div>
                        </div>
                    </div>
                </div>

                <!--Messages-->
                <div class="row content-wrap messages" id="messageswindow">
                    <div class='msg'>
                    <div class='media-body'>

                    </div>
                    </div>
                </div>

                <!--Message box & Send Button-->

                <div class="row send-wrap">
                    <div class="send-message" style="display: none" id="send-message">

                        <div class="message-text">
                            <textarea class="no-resize-bar form-control" rows="2" placeholder="Write a message..." name="message" id="message"></textarea>
                        </div>

                        <div class="send-button" id="sendbutton">
                            <a class="btn" href="javascript:submitMessage()">Send <i class="fa fa-send"></i></a>
                        </div>
                    </div>
                </div>


            </div>

            <!--Sliding Menu / Conversation Members-->
            <div class="col-sm-3 section-wrap" id="Members" style="display: none">

                <!--Header-->
                <div class="row header-wrap">
                    <div class="chat-header col-sm-12">
                        <div><h4>Conversation Info</h4></div>
                        <div class="header-button">
                            <a class="btn pull-right info-btn">
                                <i class="fa fa-close"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!--Members-->
                <div class="row content-wrap">
                    <div class="contact">
                        <div class="media-body">
                            <h5 class="media-heading"></h5>
                            <small class="pull-left time"></small>
                        </div>
                    </div>

                </div>

            </div>

        </div>
    </div>
</div>

<g:render template="/funlearn/footer"></g:render>
<asset:javascript src="moment.min.js"/>
<script>
    var id;
    var messagetype="message";
    var globalUserType;
    var lastMesssage;
    var lastUserType;
    var selectedContactName;
    var selectedContactId;
    /!* Slide Members Info *!/
    $('.info-btn').on('click', function () {
        $("#MessagesInfo").toggleClass('col-sm-12 col-sm-9');
    });
    $("#MessagesInfo").toggleClass('col-sm-12 col-sm-9');


    function showConversationList(){
        $('#contacts').hide(500);
        $('#conversation').show(500);
    }

    function showContactList(){
        $('#conversation').hide(500);
        $('#contacts').show(500);
        getCurrentFriends();
    }


    function getCurrentFriends(){
        <g:remoteFunction controller="friends" action="currentFriends"  onSuccess='displayCurrentFriends(data);'
                />
    }

    function getCurrentGroups(){
        <g:remoteFunction controller="friends" action="getAllGroupsForUser"  onSuccess='displayCurrentGroups(data);'
                />
    }

    function displayCurrentFriends(data){
        var friends = data.results;
        var imageStr;
        var htmlStr="";
        var friendName="";
        if(friends.length ==0)  htmlStr="<div class='row'><div class=' col-md-12 '><b><span class='smallerText'>Learning with friends is fun. Start adding friends and build your group</span></b></div></div>";

        for(var i = 0; i < friends.length; ++i) {
            if("null"==""+friends[i].profilepic) imageStr="<a href='#'> <i class='fa fa-user fa-3x'></i></a>";
            else imageStr = "<img src='/funlearn/showProfileImage?id="+friends[i].friendId+"&fileName="+friends[i].profilepic+"&type=user&imgType=icon' class='img-circle' width='40'>";
            friendName = friends[i].name;
            htmlStr+="<div class='row contactitem' onclick='javascript:contactSelectedFromContacts("+friends[i].friendId+",\""+escape(friendName)+"\",\""+friends[i].profilepic+"\",\"user\")'>"+
                    "<div class='col-md-3 col-md-offset-1 vcenter'>"+imageStr+"</div>"+
                    "<div class='col-md-8 vcenter'><b>"+friends[i].name+"</b></div>"+
                    "<hr class='myhrline'>"+
                    "</div>";

        }
        document.getElementById('contactList').innerHTML= htmlStr;
        getCurrentGroups();
    }
    function displayCurrentGroups(data){
        var groups = data.results;
        var imageStr;
        var htmlStr="";
        var groupName="";
        if(groups.length>0)  htmlStr="<div class='row'><div class=' col-md-12 text-center orange '><b>Groups</b></div><hr class='myhrline'></div>";

        for(var i = 0; i < groups.length; ++i) {
            if("null"==""+groups[i].profilepic) imageStr="<a href='#'> <i class='fa fa-user fa-3x'></i></a>";
            else imageStr = "<img src='/funlearn/showProfileImage?id="+groups[i].groupId+"&fileName="+groups[i].profilepic+"&type=group&imgType=icon' class='img-circle' width='40'>";
            groupName = groups[i].name;
            htmlStr+="<div class='row contactitem' onclick='javascript:contactSelectedFromContacts("+groups[i].groupId+",\""+escape(groupName)+"\",\""+groups[i].profilepic+"\",\"group\")'>"+
                    "<div class='col-md-3 col-md-offset-1 vcenter'>"+imageStr+"</div>"+
                    "<div class='col-md-8 vcenter'><b>"+groups[i].name+"</b></div>"+
                    "<hr class='myhrline'>"+
                    "</div>";

        }
        document.getElementById('contactList').innerHTML=document.getElementById('contactList').innerHTML+htmlStr;
    }

    function contactSelectedFromContacts(friendId,friendName,profilepic,usertype){
        showConversationList();
        if (document.getElementById(usertype+'contactName' + friendId) == null) {
            var imageStr;
            if("group"==usertype){
                if ("null" == "" + profilepic) imageStr = "<a href='#'> <i class='fa fa-group fa-3x'></i></a>";
                else imageStr = "<img src='/funlearn/showProfileImage?id=" + friendId + "&fileName=" + profilepic + "&type=group&imgType=icon' class='img-circle' width='40'>";
            }
            else {
                if ("null" == "" + profilepic) imageStr = "<a href='#'> <i class='fa fa-user fa-3x'></i></a>";
                else imageStr = "<img src='/funlearn/showProfileImage?id=" + friendId + "&fileName=" + profilepic + "&type=user&imgType=icon' class='img-circle' width='40'>";
            }
           var htmlStr ="<div class='conversation btn' id='"+usertype+"messager"+friendId+"' onclick='javascript:contactSelected("+friendId+",\""+escape(friendName)+"\",\""+profilepic+"\",\""+usertype+"\")'>"+
                    "<div class='media-body'>"+
                    " <h5 class='media-heading' id='"+usertype+"contactName"+friendId+"'>"+imageStr+"  "+unescape(friendName)+"</h5>"+
                    "<small class='pull-right time' id='"+usertype+"lastmessagetime"+friendId+"'></small>"+
                    "</div> </div>";
            document.getElementById('conversationList').innerHTML= htmlStr+document.getElementById('conversationList').innerHTML;
        } else {
        }
        contactSelected(friendId,friendName,profilepic,usertype);

    }
    function contactSelected(friendId,friendName,profilepic,usertype){

        if (document.getElementById(lastUserType+'messager' + id) != null){
            document.getElementById(lastUserType+"messager"+ id).className = "conversation btn";
        }
        lastUserType = usertype;

        document.getElementById(usertype+"messager"+ friendId).className = "conversation btn converstationbg";
        id=friendId;
        globalUserType = usertype;
        if("group"==usertype){
            if ("null" == "" + profilepic) imageStr = "<a href='#'> <i class='fa fa-group fa-2x'></i></a>";
            else imageStr = "<img src='/funlearn/showProfileImage?id=" + friendId + "&fileName=" + profilepic + "&type=group&imgType=icon' class='img-circle' width='40'>";
        }
        else {
            if ("null" == "" + profilepic) imageStr = "<a href='#'> <i class='fa fa-user fa-2x'></i></a>";
            else imageStr = "<img src='/funlearn/showProfileImage?id=" + friendId + "&fileName=" + profilepic + "&type=user&imgType=icon' class='img-circle' width='40'>";
        }

        document.getElementById('chatName').innerHTML= "<h4>"+imageStr+" "+unescape(friendName)+"</h4>";
        $('#messageswrap').show();
        if(isMobile()){
            $('#panelwrap').hide(500);
            document.getElementById('showConversation').innerHTML="<a href='javascript:showConversationPanel()'>Messages</a>";
        } else {
          //  $('#messageswrap').show();
        }
        $('#send-message').show();
        getMessages(friendId,usertype);
    }

    function showConversationPanel(){
        $('#messageswrap').hide(500);
        $('#panelwrap').show(500);
    }
	
	var emoticonsRanges = [
	'\ud83c[\udf00-\udfff]', // U+1F300 to U+1F3FF
	'\ud83d[\udc00-\ude4f]', // U+1F400 to U+1F64F
	'\ud83d[\ude80-\udeff]'  // U+1F680 to U+1F6FF
	];
	
    function submitMessage(){
        if($("#message")){			
			lastMesssage=document.getElementById("message").value.replace(new RegExp(emoticonsRanges.join('|'), 'g'),'<-Emoticon Disabled->');
			lastMesssage=unescape(decodeURIComponent((escape(encodeURIComponent(lastMesssage)).replace(new RegExp('\\%25[a-f0-9A-F]+\\%25[a-f0-9A-F]+\\%25[a-f0-9A-F]+','g'),'<-Emoticon Disabled->'))));
            document.getElementById("message").readOnly = true;
            document.getElementById("sendbutton").innerHTML="<span class='btn'>Send <i class='fa fa-send'></i></span>";
            <g:remoteFunction controller="messaging" action="submitMessage"  onSuccess='messageSuccess(data);'
                params="'id='+id+'&messagetype='+messagetype+'&usertype='+globalUserType+'&message='+lastMesssage"/>;

            document.getElementById("message").value='';
        }
    }

    function displayMessageLine(date,sender,message,name,receiver,msgid){
        var htmlStr="";
        htmlStr =" <div class='msg'>" +
                " <div class='media-body'>" +
                " <small class='pull-right time'><i class='fa fa-clock-o'></i>"+ getLocalDate(date.toString())+"</small>" +
                "<h5 class='media-heading'>"+name+"</h5>" +
                "<small class='bubble "+sender+" '>"+message+"&nbsp;&nbsp;<a href='javascript:deleteMsg("+msgid+",\""+sender+"\","+receiver+")'><i class='fa fa-trash-o'></i></a></small>" +
                "</div>" +
                "</div>";
				
        return htmlStr;
    }
	
    function messageSuccess(data){
        var element = document.getElementById("messageswindow");
        if("OK"==data.status){
            var messages = data.results;
            var date;
            var htmlStr="";
            var name="";
            for (var i = 0; i < messages.length; ++i) {
                date = new Date(moment(messages[i].messageDate));
                if("group"==lastUserType) name=messages[i].name;
                else name="";
                htmlStr +=displayMessageLine(date,messages[i].sender,messages[i].message,name,messages[i].receiver,messages[i].msgId);	
            }

            element.innerHTML = element.innerHTML+ htmlStr;
        }

        date = new Date();
        htmlStr = displayMessageLine(date,"you",lastMesssage,"",messages[i].receiver,messages[i].msgId);

        element.innerHTML = element.innerHTML+htmlStr;
        element.scrollTop = element.scrollHeight;
        lastMesssage="";
        document.getElementById("message").readOnly = false;
        document.getElementById("sendbutton").innerHTML="<a class='btn' href='javascript:submitMessage()'>Send <i class='fa fa-send'></i></a>";
    }
    
	function isMobile(){
        var isMobile = false; //initiate as false
		// device detection
        if(/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|ipad|iris|kindle|Android|Silk|lge |maemo|midp|mmp|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i.test(navigator.userAgent)
                || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(navigator.userAgent.substr(0,4))) isMobile = true;
        return isMobile;
    }

    $("#message").keyup(function(event){
        if(event.keyCode == 13){
            submitMessage();
        }
    });

    function getLatestMessageUsersList(){
        <g:remoteFunction controller="messaging" action="getLatestMessageUsersList"  onSuccess='displayMessageUsersList(data);'
             params="'messagetype='+messagetype"  />
    }

    function displayMessageUsersList(data){
        var htmlStr="";
        if("OK"==data.status){
            var users = data.results;
            var imageStr;
            for(var i = 0; i < users.length; ++i) {
                if("null"==""+users[i].profilepic) imageStr="<a href='#'> <i class='fa fa-"+users[i].usertype+" fa-3x'></i></a>";
                else imageStr = "<img src='/funlearn/showProfileImage?id="+users[i].messagerId+"&fileName="+users[i].profilepic+"&type="+users[i].usertype+"&imgType=icon' class='img-circle' width='40'>";

               htmlStr +="<div class='conversation btn' id='"+users[i].usertype+"messager"+users[i].messagerId+"' onclick='javascript:contactSelected("+users[i].messagerId+",\""+escape(users[i].name)+"\",\""+users[i].profilepic+"\",\""+users[i].usertype+"\")'>"+
                        "<div class='media-body'>"+
                       " <h5 class='media-heading' id='"+users[i].usertype+"contactName"+users[i].messagerId+"'>"+imageStr+"  "+users[i].name+"</h5>"+
                "<small class='pull-right time' id='"+users[i].usertype+"lastmessagetime"+users[i].messagerId+"'></small>"+
                "</div> </div>";
            }
			
            document.getElementById('conversationList').innerHTML= htmlStr;
            getLatestUnreadMessageCount();
        } else {
            <%if(friendId==null){%>showContactList();<%}%>
        }
		
        <%if(friendId!=null){%>
        contactSelectedFromContacts('${friendId}','${friendNm}','${profilepic}','${usertype}');
        <%}%>
    }

    function getLatestUnreadMessageCount(){
        <g:remoteFunction controller="messaging" action="getLatestUnreadMessageCount"  onSuccess='displayUnReadMessageCount(data);'
             params="'messagetype='+messagetype"  />
    }

    function displayUnReadMessageCount(data){
        var htmlStr="";
        if("OK"==data.status){
            var messagesCount = data.results;
            var date;
            for(var i = 0; i < messagesCount.length; ++i) {
                //check to see if the id is not present in the list
                if (document.getElementById( messagesCount[i].usertype+'contactName' + messagesCount[i].fromUserId) != null) {
                    if (document.getElementById(messagesCount[i].usertype+'notificationCount' + messagesCount[i].fromUserId) != null) {
                       var element = document.getElementById(messagesCount[i].usertype+'contactName' + messagesCount[i].fromUserId);
                        var notificationCount = document.getElementById(messagesCount[i].usertype+'notificationCount' + messagesCount[i].fromUserId);
                        element.removeChild(notificationCount);
                    }
                    document.getElementById(messagesCount[i].usertype+'contactName' + messagesCount[i].fromUserId).innerHTML = document.getElementById(messagesCount[i].usertype+'contactName' + messagesCount[i].fromUserId).innerHTML + " " + "<span class='badge' style='background-color: red' id='"+messagesCount[i].usertype+"notificationCount"+messagesCount[i].fromUserId+"'>" + messagesCount[i].unreadCount + "</span>";
                    date = new Date(moment(messagesCount[i].lastMessageDate));
                    document.getElementById(messagesCount[i].usertype+'lastmessagetime' + messagesCount[i].fromUserId).innerHTML = "Last message "+getLocalDate(date.toString());

                    //if the  message window is active for this user
                    if(selectedContactId==messagesCount[i].fromUserId){
                        getUnreadMessagesForView();
                    }
                }
            }
        }
    }

    function getMessages(friendId,usertype){
		console.log(friendId)
        selectedContactId = friendId;
        <g:remoteFunction controller="messaging" action="getMessages"  onSuccess='displayMessages(data,false);'
            params="'id='+friendId+'&messagetype='+messagetype+'&usertype='+usertype"/>
    }

    function displayMessages(data,append){
        var element = document.getElementById("messageswindow");
        if("OK"==data.status) {
            var messages = data.results;
            var date;
            var htmlStr="";
            var name="";
            for (var i = 0; i < messages.length; ++i) {
                date = new Date(moment(messages[i].messageDate));
                if("group"==lastUserType) name=messages[i].name;
                else name="";
                htmlStr += displayMessageLine(date,messages[i].sender,messages[i].message,name,messages[i].receiver,messages[i].msgId);
            }

            if(append) element.innerHTML += htmlStr;
            else element.innerHTML = htmlStr;
			
            element.scrollTop = element.scrollHeight;
            if (document.getElementById(lastUserType+'notificationCount' + selectedContactId) != null) {
                element = document.getElementById(lastUserType+'contactName' + selectedContactId);
                var notificationCount = document.getElementById(lastUserType+'notificationCount' + selectedContactId);
                element.removeChild(notificationCount);
            }
        } else {
           element.innerHTML="";
        }
		
        getTotalUnreadMessageCount();
    }

    $( "#messageswindow" ).scroll(function() {
        if ($("#messageswindow").scrollTop() == 0){
           // alert('0 man');
        }
    });

    function getUnreadMessagesForView(){
        <g:remoteFunction controller="messaging" action="getUnreadMessagesForView"  onSuccess='displayMessages(data,true);'
                params="'id='+selectedContactId+'&messagetype='+messagetype+'&usertype='+lastUserType"/>;
    }

    getLatestMessageUsersList();
    setInterval(getLatestUnreadMessageCount, 20000);
	
	if(isMobile()) {
        $('#footerid').hide();
    }
	
	function deleteMsg(msgId,user,friendId) {
        <g:remoteFunction controller="messaging" action="deleteMsg" onSuccess='getMessages(friendId,lastUserType);' params="'msgId='+msgId+'&user='+user" />;	
	}
</script>
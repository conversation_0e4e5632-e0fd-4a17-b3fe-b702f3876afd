
<style>
body {
    background-color: #f5f7fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

h1, h3 {
    color: #343a40;
}

.card {
    border: none;
    border-radius: 10px;
    background: linear-gradient(135deg, #e8f0fe, #ffffff);
}

.card .card-title {
    font-size: 1.1rem;
}

.btn-primary {
    background-color: #4a90e2;
    border-color: #4a90e2;
}

.btn-primary:hover {
    background-color: #357ab8;
    border-color: #357ab8;
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #5a6268;
}

.table-hover tbody tr:hover {
    background-color: #eef1f5;
}

.fa-folder {
    color: #f0ad4e;
}

.fa-file-pdf {
    color: #d9534f;
}

.fa-file-word {
    color: #0275d8;
}

/* Additional styles for AI theme */


.ai-assistant-toggle:hover {
    background-color: #357ab8;
}


#copilot1 {
    width: 85%;
    margin: 0 auto;
}
</style>

<div class="mx-0 my-3 px-3 col-12 col-md-10 mx-auto fadein-animated" id="publishersList" style="display: none">
    <div class="row">
        <h5 class="col-12">eBooks Procurement</h5>
    </div>
    <div class="row">
        <span class="col-12">We work with more than 200 publishers to procure eBooks as per your institution needs. Please contact us for the catalogue and pricing.</span>
    </div><br>
    <div class="row">
        <span class="col-12">Some of our prominent publishing partners.</span> </div><br>
    <div class="row" id="publishersTable">

    </div>

</div>

<script>
    function getPublishersList(){
        //loader
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="libraryBooks" action="getWonderslatePublishers"  onSuccess='publishersReceived(data);'/>
    }

    function publishersReceived(data)
    {
        var publishersList = JSON.parse(data.wonderslatePublishers);
        var publishersTable = document.getElementById('publishersTable');
        var publishersTableHTML = '<table class="table table-hover"><thead><tr><th><b>Publisher</b></th></tr></thead><tbody>';
        for(var i=0; i<publishersList.length; i++)
        {
            publishersTableHTML += '<tr><td>'+publishersList[i].name+'</td></tr>';
        }
        publishersTableHTML += '</tbody></table>';
        publishersTable.innerHTML = publishersTableHTML;
        $('#publishersList').show();
        $('.loading-icon').addClass('hidden');

    }

</script>



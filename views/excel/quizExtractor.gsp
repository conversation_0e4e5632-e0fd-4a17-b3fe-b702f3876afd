<!DOCTYPE html>
<html>
<head>
    <title>Multiple Choice Questions</title>
    <style>
    table {
        border-collapse: collapse;
        width: 100%;
    }
    table, th, td {
        border: 1px solid black;
    }
    th, td {
        padding: 8px;
        text-align: left;
    }
    th {
        background-color: #f2f2f2;
    }
    </style>
</head>
<body>
<textarea id="questionsTextArea" rows="10" cols="50" placeholder="Paste your questions here..."></textarea>
<button onclick="formatQuestions()">Format Questions</button>
<button onclick="exportToExcel()">Export to Excel</button>
<div id="formattedQuestions"></div>

<script src="https://unpkg.com/xlsx/dist/xlsx.full.min.js"></script>

<script>
    const formattedQuestionsArray = [];

    function formatQuestions() {
        const questionsTextArea = document.getElementById("questionsTextArea");
        const formattedQuestions = document.getElementById("formattedQuestions");

        const inputText = questionsTextArea.value;
        const questionRegex = /(\d+)\.\s(.*?)(\(A\)) (.*?)(\(B\)) (.*?)(\(C\)) (.*?)(\(D\)) (.*?) (Ans. :) \(([A-D])\)(.+?)(?=\d+\.\s|$)/g;

        let match;
        formattedQuestionsArray.length = 0; // Clear the array before formatting

        while ((match = questionRegex.exec(inputText)) !== null) {
            const questionNumber = match[1];
            const questionText = match[2];
            const options = [`${match[4]}`, ` ${match[6]}`, `${match[8]}`, ` ${match[10]}`];
            const correctAnswer = match[12];
            const answerExplanation = match[13];

            formattedQuestionsArray.push({
                questionNumber,
                questionText,
                options,
                correctAnswer,
                answerExplanation,
            });
        }

        if (formattedQuestionsArray.length > 0) {
            // Display the formatted questions in a table
            let tableHtml = '<table>';
            tableHtml += '<tr><th>Question</th><th>Option A</th><th>Option B</th><th>Option C</th><th>Option D</th><th>Answer</th><th>Answer Explanation</th></tr>';
            formattedQuestionsArray.forEach((formattedQuestion) => {
                tableHtml += `<tr>
                        <td>${formattedQuestion.questionText}</td>
                        <td>${formattedQuestion.options[0]}</td>
                        <td>${formattedQuestion.options[1]}</td>
                        <td>${formattedQuestion.options[2]}</td>
                        <td>${formattedQuestion.options[3]}</td>
                        <td>${formattedQuestion.correctAnswer}</td>
                        <td>${formattedQuestion.answerExplanation}</td>
                    </tr>`;
            });
            tableHtml += '</table>';
            formattedQuestions.innerHTML = tableHtml;
        } else {
            formattedQuestions.innerHTML = "No questions found in the input text.";
        }
    }

    function exportToExcel() {
        if (formattedQuestionsArray.length > 0) {
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.aoa_to_sheet([
                ["Question", "option1", "option2", "option3", "option4", "correctAnswer", "answerDescription"]
            ]);

            formattedQuestionsArray.forEach((formattedQuestion) => {
                XLSX.utils.sheet_add_aoa(ws, [
                    [formattedQuestion.questionText, ...formattedQuestion.options, formattedQuestion.correctAnswer, formattedQuestion.answerExplanation]
                ], { origin: -1 });
            });

            XLSX.utils.book_append_sheet(wb, ws, "Questions");
            XLSX.writeFile(wb, "formatted_questions.xlsx");
        }
    }
</script>
</body>
</html>

<div class="pdfViewer" style="flex: 70%;">
    <% if(enableCompiler) {%>
    <div class="pdfOpts" style="justify-content: space-between">
        <button class="compiler_open-modal-btn">Open Compiler</button>
    <% } else {%>
        <div class="pdfOpts" style="justify-content: flex-end">
        <%}%>
        <span class="fullScreenBtn" style="align-items: center;gap: 8px" data-chat="open"><i class="fa-solid fa-expand"></i> Full Screen</span>
    </div>
    <%if(showNotificaiton == "true" || showNotificaiton=="Yes" ||showNotificaiton=="yes" && showNotificaiton != null){%>
        <iframe id="bookGPTReader" style="height:96% !important " src=""></iframe>
        <p class="noticeMsg"><i class="fa-solid fa-circle-info" style="margin-right: 5px;"></i>This is not part of iBookGPT. This material is provided for the convenience of the user for ready reference.</p>
    <%}else{%>
        <iframe id="bookGPTReader" src=""></iframe>
    <%}%>
</div>

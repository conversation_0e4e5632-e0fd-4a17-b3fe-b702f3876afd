<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>


<script>
    var loggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>


<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid publish-management" style="min-height: calc(100vh - 160px);">
    <div class="row">
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="errormsg" class="alert alert-danger has-error" role="alert" style="display: none; background: none;"></div>
            <div id="successmsg" class="mb-3 mx-2 text-success" style="display: none">Added Successfully!</div>
            <label for="bookId" style="display: block;">Add book</label>
            <div class="dflex_pub flex-wrap">
                <input type="text" class="form-control" name="bookId" id="bookId"  placeholder="Book id"></input><br>
                <input type="text" class="form-control" name="emailId" id="emailId"  placeholder="User email id or mobile"></input><br>
                <input type="text" class="form-control" name="paymentId" id="paymentId"  placeholder="Payment Id"></input><br>
                <input type="text" class="form-control" name="discountId" id="discountId"  placeholder="Discount Id"></input><br>
                <% if(session["userdetails"].publisherId==null&&"1".equals(""+session["siteId"])) {%>
                    <g:select id="siteId" class="form-control" optionKey="id" optionValue="clientName"
                              value="" name="siteId" from="${sitesList}" noSelection="['':'Select Site']"/>
                <%  } else{%>
                <input type="hidden" name="siteId" id="siteId" value="null">
                <%}%>
                <select name="bookType" id="bookType" class="form-control">
                    <option value="eBook">eBook</option>
                    <option value="testSeries">Online test series</option>
                    <option value="bookGPT">iBookGPT</option>
                </select>
                <button class="btn btn-primary form-control" style="margin: 5px" onclick="addBook();">Add Book</button><br>

            </div>
        </div>
    </div>
</div>


<g:render template="/${session['entryController']}/footer_new"></g:render>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>

<script>

    function addBook() {

        if (document.getElementById("bookId").value === "") {
            document.getElementById("errormsg").innerHTML = "Please enter the book id";
            $("#errormsg").show();
        } else if (document.getElementById("emailId").value === "") {
            document.getElementById("errormsg").innerHTML = "Please enter the email id of the user";
            $("#errormsg").show();
        } else {
            $('.loading-icon').removeClass('hidden');
            $("#errormsg").hide();
            var emailId = document.getElementById("emailId").value;
            var bookId = document.getElementById("bookId").value;
            var paymentId = document.getElementById("paymentId").value;
            var siteId = document.getElementById("siteId").value;
            var discountId = document.getElementById("discountId").value;
            var bookType = document.getElementById("bookType").value;
            <g:remoteFunction controller="admin" action="addBookToUser" params="'emailId='+emailId+'&bookId='+bookId+'&paymentId='+paymentId+'&discountId='+discountId+'&siteId='+siteId+'&bookType='+bookType" onSuccess = "uploadedBook(data);"/>

        }
    }

    function uploadedBook(data){
        $("#errormsg").hide();
        $("#successmsg").hide();
        $('.loading-icon').addClass('hidden');
        if(data.status=="error"){
            document.getElementById("errormsg").innerHTML="Error adding the book."
            $("#errormsg").show();
        }
        else{
            document.getElementById("emailId").value="";
            document.getElementById("bookId").value="";
            document.getElementById("paymentId").value="";
            $("#successmsg").show();
        }
    }

</script>
<div class="chatViewer">
    <div id="gpt_loader" class="gpt_loader">
        <div class="spinner"></div>
        <div class="introText">
            <h3 id="loaderSub" style="margin-top: 12px;">
                Preparing your <span class="logo">Book<span class="logoHighlight">GPT</span>
                <img src="/assets/resource/glitter.svg" class="glitter-icon"/>
            </span>
            </h3>
        </div>
    </div>
    <div class="conversation">
        <div class="introText">
            <h3>
                <span class="logo">Book<span class="logoHighlight">GPT</span>
                <img src="/assets/resource/glitter.svg" class="glitter-icon"/>
                </span>
            </h3>
        </div>
        <div class="chatOptions" style="display:none;">
            <p class="introTextWord">
                <span class="logo">Start here</span>
                </span>
            </p>
            <div class="defaultOptions"></div>
        </div>
        <div style="position: sticky;top: 0;width: 100%;background: #fff;padding: 10px;z-index: 999">
            <select name="resTypeSelector" id="resTypeSelector" onchange="updateResType(this)"></select>
        </div>
        <div class="messages">
            <div id="messages" style="display:flex;flex-direction: column">

            </div>
            <div class="is-typing" style="display:none;">
                <div class="jump1"></div>
                <div class="jump2"></div>
                <div class="jump3"></div>
            </div>
        </div>

        <div class="chat" id="customChatInput" style="display: flex;align-items: center">
            <textarea type="text" id="chatInput" class="chatInputField" placeholder="Ask any question about the chapter..." style="height: 200px !important;"></textarea>
            <div class="chatInputOptions">
                <button class="sendIcon" id="sendBtn">
                    <i class="fa-solid fa-paper-plane"></i>
                </button>
            </div>
        </div>

    </div>

</div>
<script src="https://cdnjs.cloudflare.com/ajax/libs/marked/12.0.2/marked.min.js"  referrerpolicy="no-referrer"></script>
<script>

    const resTypeSelector = document.getElementById('resTypeSelector')
    const messages = document.getElementById('messages')
    const introText = document.querySelector('.introText')
    const defaultOptions = document.querySelector('.defaultOptions')
    const isTyping = document.querySelector('.is-typing')
    const conversation = document.querySelector('.conversation');
    let cardClicked = false;
    const responseArray = []
    let responseId = 0;
    let resGobalType = ""
    let promptLabel = ""

    let username =''
    <%if(session["userdetails"]!=null){%>
    username = '${session["userdetails"].username}'
    username = username.replace("&#64;",'@')
    <%}%>

    const basePromptArray = '${basePromptArray}'
    const bookLevelPrompt = '${bookLevelPrompt}'
    const basePrompts = JSON.parse(basePromptArray.replace(/&quot;/g,'"').replace(/&#92;/g, '\\').replace(/&#39;/g, "'").replace('&lt;','<').replace('&gt;','>'))

    let basePromptsHTML = "<option value=''>Select Label</option>"

    basePrompts.forEach((prompt,index)=>{
        basePromptsHTML +="<option value='"+prompt.promptType+"' data-index='"+index+"'>"+prompt.promptLabel+"</option>"
    })
    resTypeSelector.innerHTML = basePromptsHTML

    async function checkAndStorePDF(){
        const checkPDF = await fetch(GPT_API_URL+'/checkPDFExists?namespace='+namespace,{
            method:"get"
        })
        const isPDFExist = await checkPDF.json()

        let resVal = isPDFExist
        if(!resVal.isExist){
            const res = await fetch("/funlearn/getPdfFile?resId="+gptResId+"&encryptedKey="+encryptedKey)
            const resData = await res.arrayBuffer()
            let pdfBlob = new Blob([resData], { type: 'application/pdf' });
            let formData = new FormData();
            formData.append('file', pdfBlob, namespace+".pdf");
            const pdfres = await fetch(GPT_API_URL+"/processPDFVector",{
                method:"post",
                body: formData
            })
            if(!pdfres.ok){
                console.log("Error in processing PDF")
                return
            }
            resVal = await pdfres.json()
        }
        gpt_loader.style.display='none';
    }
    checkAndStorePDF()

    let optionsHTML = ""
    let customInputHTML = ""

    customInputHTML += "<input type='text' id='chatInput' class='chatInputField' placeholder='Ask any question about the chapter...'>"+
                        "<div class='chatInputOptions'>"+
                        "<div>"+
                        "<p class='upgradeText'>Upgrade to <span>Book<span>Plus</span><img src='/assets/resource/glitter.svg'/></span></p>"+
                        "</div>"+
                            "<button class='sendIcon' id='sendBtn'><i class='fa-solid fa-paper-plane'></i></button>"+
                        "</div>";

    defaultOptions.innerHTML = optionsHTML;

    const defaultOptionCards = document.querySelectorAll('.defaultOptionCard');
    const customChatInput = document.getElementById('customChatInput')
    updateCustomInputField()



    function updateCustomInputField(){
        const chatInput = document.getElementById('chatInput')
        const sendBtn = document.getElementById('sendBtn')
        if(sendBtn){
            sendBtn.addEventListener('click',(e)=>{
                ask(chatInput.value,chatInput.value,resGobalType)
            })
        }
        if(chatInput){
            chatInput.addEventListener('keydown',(e)=>{
                if(e.key === 'Enter' && !event.shiftKey){
                    event.preventDefault();
                    ask(chatInput.value,chatInput.value,resGobalType)
                }
            })
        }
    }

    function updateResType(selectElement) {
        const selectedOption =selectElement.options[selectElement.selectedIndex]
        resGobalType = selectElement.value
        const index = selectedOption.getAttribute("data-index")
        const chatInput = document.getElementById('chatInput')
        if(index && index!==''){
            chatInput.value = bookLevelPrompt + ' '+basePrompts[parseInt(index)].basePrompt
            promptLabel = basePrompts[parseInt(index)].promptLabel
        }
    }
    updateResType(resTypeSelector)

    async function ask(shortQuery,query,resType){
        conversation.removeAttribute('style')
        introText.style.display ='none'
        showUserMessage(shortQuery)
        isTyping.style.display='flex'
        isTyping.scrollIntoView()
        const response = await fetch(GPT_API_URL+'/retrieveDataAdmin',{
            method:"POST",
            body:JSON.stringify({
                namespace:namespace,
                query:query.trim(),
                resType:resType,
                username:username
            }),
            headers:{
                "Content-Type":"application/json"
            }
        })

        const answer = await response.json()
        answer.promptLabel = promptLabel
        answer.resType = resType
        showAnswer(answer)
        storeQueryAndResponseAdmin(shortQuery,answer)
    }
    function showUserMessage(question){
        let userMessageHTML = ""
        userMessageHTML +=
            "<div>"+
                "<div class='message userMessage'>"+question+"</div>" +
            "</div>";
        messages.innerHTML += userMessageHTML;
    }

    function showAnswer(answer){
        responseId++
        answer.responseId = responseId
        responseArray.push(answer)
        if(answer.resType.includes("mcq") || answer.resType.includes("mcqs")){
            mcqResponseHandler(answer)
        }else if(answer.resType.includes("qna") || answer.resType.includes("qanda")|| answer.resType.includes("question & answer")){
            qnaResponseHandler(answer)
        }else if(answer.resType.includes("flashcards") || answer.resType.includes("flashcard")){
            flashcardsResponseHandler(answer)
        }else{
            textResponseHandler(answer)
        }

    }

    function textResponseHandler(answer){
        const parsedContent = answer.answer;
        let botMessageHTML = ""
        isTyping.style.display='none'
        botMessageHTML +=
            "<div>"+
            "<div class='message botMessage' style='width: 100%;'><pre id='preid_"+responseId+"'>"+parsedContent+"</pre></div>"+
      //      "<div>" +
       //     "<button style='padding: 4px' id='responseId_"+responseId+"' data-responseId='"+responseId+"' onclick='storeQueryAndResponse(this)'>Update Response</button>" +
       //     "</div>"+
            "</div>";
        messages.innerHTML += botMessageHTML
        conversation.scrollTop = conversation.scrollHeight
    }

    function mcqResponseHandler(answer){
        let resultContent = answer.answer;
        let parsedContent
        let questionHTML = ''
        let qCount = 0
        let submitQuestionArray = []
        for(var p=0;p<resultContent.length;p++){
            parsedContent = resultContent[p]
            try {
                parsedContent = JSON.parse(parsedContent.replace(/\n\s*/g, ''))

            let questions
            if(parsedContent.questions) {
                questions = parsedContent.questions
            }else{
                questions = parsedContent
            }

            for(var q=0;q<questions.length;q++){
                qCount++
                questionHTML+= "Q"+qCount+". "+questions[q].question+"\n"
                questionHTML+= "A. "+questions[q].op1+"\n"
                questionHTML+= "B. "+questions[q].op2+"\n"
                questionHTML+= "C. "+questions[q].op3+"\n"
                questionHTML+= "D. "+questions[q].op4+"\n"
                const ans = checkAnswerFormat(questions[q])
                questions[q].answer = ans.answer
                questionHTML+= "Answer: "+questions[q].answer+"\n"
                questionHTML+= "Difficulty: "+questions[q].difficulty+"\n"
                questionHTML+= "Explanation: "+questions[q].explanation+"\n\n"

                questions[q].question = questions[q].question+"\n"
                submitQuestionArray.push(questions[q])
            }
            }catch (e) {
                console.log(e)
            }
        }

        let botMessageHTML = ""
        isTyping.style.display='none'
        botMessageHTML +=
            "<div>"+
            "<div class='message botMessage' style='width: 100%;'><pre id='preid_"+responseId+"'>"+questionHTML+"</pre></div>"+
        //    "<div>" +
        //    "<button style='padding: 4px' id='responseId_"+responseId+"' data-responseId='"+responseId+"' onclick='storeQueryAndResponse(this)'>Update Response</button>" +
        //    "</div>"+
            "</div>";
        messages.innerHTML += botMessageHTML
        conversation.scrollTop = conversation.scrollHeight
        const qna = {
            questions:submitQuestionArray
        }
        const responseObj = getObjectByResponseId(responseArray, Number(responseId))

        responseObj.answer = JSON.stringify(qna)
    }

    function checkAnswerFormat(question){
        if (
            question.answer == question.op1 &&
            !question.answer.startsWith('op')
        ) {
            question.answer = 'op1'
        } else if (
            question.answer == question.op2 &&
            !question.answer.startsWith('op')
        ) {
            question.answer = 'op2'
        } else if (
            question.answer == question.op3 &&
            !question.answer.startsWith('op')
        ) {
            question.answer = 'op3'
        } else if (
            question.answer == question.op4 &&
            !question.answer.startsWith('op')
        ) {
            question.answer = 'op4'
        }
        return question
    }

    function qnaResponseHandler(answer){
        let resultContent = answer.answer;
        let parsedContent
        let questionHTML = ''
        let qCount = 0
        let submitQuestionArray = []
        for(var p=0;p<resultContent.length;p++){
            parsedContent = resultContent[p]
            try {
                parsedContent = JSON.parse(parsedContent.replace(/\n\s*/g, ''))

            let questions
            if(parsedContent.questions) {
                questions = parsedContent.questions
            }else{
                questions = parsedContent
            }

            for(var q=0;q<questions.length;q++){
                qCount++
                questionHTML+= "Q"+qCount+". "+questions[q].question+"\n"
                questionHTML+= "Answer: "+questions[q].answer+"\n"
                questionHTML+= "Difficulty: "+questions[q].difficulty+"\n"
                questions[q].question = questions[q].question+"\n"
                submitQuestionArray.push(questions[q])
            }
            }catch (e) {
                console.log(e)
            }
        }

        let botMessageHTML = ""
        isTyping.style.display='none'
        botMessageHTML +=
            "<div>"+
            "<div class='message botMessage' style='width: 100%;'><pre id='preid_"+responseId+"'>"+questionHTML+"</pre></div>"+
      //      "<div>" +
     //       "<button style='padding: 4px' id='responseId_"+responseId+"' data-responseId='"+responseId+"' onclick='storeQueryAndResponse(this)'>Update Response</button>" +
     //       "</div>"+
            "</div>";
        messages.innerHTML += botMessageHTML
        conversation.scrollTop = conversation.scrollHeight
        const qna = {
            qna:submitQuestionArray
        }
        const responseObj = getObjectByResponseId(responseArray, Number(responseId))

        responseObj.answer = JSON.stringify(qna)
    }

    function flashcardsResponseHandler(answer){
        let resultContent = answer.answer;
        let parsedContent
        let questionHTML = ''
        let qCount = 0
        let submitQuestionArray = []
         for(var p=0;p<resultContent.length;p++){
            parsedContent = resultContent[p]
             try {
                parsedContent = JSON.parse(parsedContent.replace(/\n\s*/g, ''))

             let flashcards
             if(parsedContent.flashcards) {
                 flashcards = parsedContent.flashcards
             }else{
                 flashcards = parsedContent
             }
            for(var q=0;q<flashcards.length;q++){
                qCount++
                questionHTML+= "Front: "+flashcards[q].front+"\n"
                questionHTML+= "Back: "+flashcards[q].back+"\n"
                submitQuestionArray.push(flashcards[q])
            }
             }catch (e) {
                 console.log(e)
             }
         }
        let botMessageHTML = ""
        isTyping.style.display='none'
        botMessageHTML +=
            "<div>"+
            "<div class='message botMessage' style='width: 100%;'><pre id='preid_"+responseId+"'>"+questionHTML+"</pre></div>"+
         //   "<div>" +
          //  "<button style='padding: 4px' id='responseId_"+responseId+"' data-responseId='"+responseId+"' onclick='storeQueryAndResponse(this)'>Update Response</button>" +
          //  "</div>"+
            "</div>";
        messages.innerHTML += botMessageHTML
        conversation.scrollTop = conversation.scrollHeight
        const qna = {
            flashcards:submitQuestionArray
        }
        const responseObj = getObjectByResponseId(responseArray, Number(responseId))

        responseObj.answer = JSON.stringify(qna)
    }
    async function storeQueryAndResponse(target){
        const responseId = target.getAttribute('data-responseId')
        const responseObj = getObjectByResponseId(responseArray, Number(responseId))
        responseObj.resType = resGobalType;
        const paramObj = {
            answer:responseObj.answer,
            resId:gptResId,
            promptType:responseObj.resType,
            prompt:responseObj.query,
            promptLabel:responseObj.promptLabel
        }

        showAppLoader(false)
        const storeGPTResponse = await fetch("/prompt/createGPTResource",{
            method:"POST",
            body:JSON.stringify(paramObj),
            headers:{
                "Content-Type":"application/json"
            }
        })
        hideAppLoader()
        if(!storeGPTResponse.ok){
            alert("Something went wrong")
        }
        const response = await storeGPTResponse.json()

        if(response.status==="OK" && response.resId){
            alert("Response updated successfully.")
        }
    }
    function getObjectByResponseId(array, responseId) {
        return array.find(obj => obj.responseId === responseId);
    }
    function auto_grow(element) {
        element.style.height = "5px";
        element.style.height = (element.scrollHeight) + "px";
    }
    function auto_shrink(element){
        element.style.height = "20px";
    }

    async function storeQueryAndResponseAdmin(query,answer){
        const logUserQuery = {
            username:username,
            response:answer.answer,
            resId:gptResId,
            readingMaterialResId:null,
            promptType:"adminInput"
        }
            logUserQuery.userPrompt = query
            logUserQuery.systemPrompt = null
            logUserQuery.readingMaterialResId = gptResId

        try{
            const storeUserQuery = await fetch('/gptLog/save ',{
                method:"POST",
                body:JSON.stringify(logUserQuery),
                headers:{
                    "Content-Type":"application/json"
                }
            })
            const storedRes = await storeUserQuery.json()

        }catch (err){
            console.log(err)
        }
    }
</script>

<!doctype html>
<%if("true".equals(session["commonWhiteLabel"])){%>
<link rel="icon"  href="/privatelabel/showPrivatelabelImage?siteId=${session["siteId"]}&fileName=${session["favicon"]}" type="image/x-icon">
<%}else{%>
<link rel="icon"  href="${assetPath(src: "favicons/${session['entryController']}.png")}" type="image/x-icon">
<%}%>

<style>
.bookDetails__container{
    width: calc(100%  - 20%);
    margin: 0 auto;
}
#relatedBooks{
    display: flex;
    margin-left: 0 !important;
    margin-right: 0 !important;
}
div#relatedBooks>div {
    padding: 0px 5px;
}
.relatedTitle{
    margin-left: 10px;
}
.card-wrapper .buy{
    width: 100%;
}
.card-wrapper .buy .btn-book-buy{
    display: block;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px){
    .bookDetails__container{
        width: calc(100%  - 2%);
        margin: 0 auto;
    }
    .popular_searches .popular_search_lists {
        margin-left: 0% !important;
    }
    .relatedTitle{
        margin-left: 35px;
    }
}

</style>

<div class="mdl-layout__container_ebook">
    <div class="loading-icon hidden">
        <div class="loader-wrapper">
            <div class="loader">Loading</div>
        </div>
    </div>
<%@ page import="com.wonderslate.data.ChaptersMst" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<g:render template="/funlearn/topicinclude"></g:render>
<asset:stylesheet href="wonderslate/chapter.css"/>
<asset:stylesheet href="wonderslate/wsresource.css"/>
<%if("true".equals(mainResourcesPage)){%>
<asset:stylesheet href="wonderslate/ws_whitelabels.css"/>
<%}%>
<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>
<script src="https://apis.google.com/js/api.js"></script>
<link href="https://vjs.zencdn.net/7.0.3/video-js.css" rel="stylesheet">

<div class="resourcePageShimmer">
    <div class="resourcePageShimmer_sideBar">
        <div class="card">
            <div class="shimmerBGWrp">
                <div class="shimmerBG contentBtn"></div>
                <div class="shimmerBG contentBtn"></div>
            </div>
            <div class="p-32">
                <div class="shimmerBG content-line"></div>
                <div class="shimmerBG content-line"></div>
                <div class="shimmerBG content-line"></div>
                <div class="shimmerBG content-line"></div>
            </div>
        </div>
    </div>
    <div class="resourcePageShimmer_main">
        <div class="card">
            <div class="shimmerBGWrp">
                <div class="shimmerBG contentBtn"></div>
                <div class="shimmerBG contentBtn"></div>
                <div class="shimmerBG contentBtn"></div>
                <div class="shimmerBG contentBtn"></div>
            </div>
            <div class="p-32">
                <div class="shimmerBG title-line"></div>
                <div class="shimmerBG title-line end"></div>
                <div class="shimmerBG title-line end"></div>
                <div class="shimmerBG title-line end"></div>
                <div class="shimmerBG title-line end"></div>
            </div>
        </div>
    </div>
</div>

<%if(!"1".equals(""+session["siteId"])){%>
<div class="d-lg-none ml-3 mt-3">
    <i class='material-icons wl-back-button' onclick='javascript:mobileBackToChapters();' style='cursor: pointer;'>keyboard_backspace</i>
</div>
<%}%>
<ul class="nav nav-tabs d-none d-lg-flex justify-content-start justify-content-lg-center  add-tabs">
    <!-- Raised button with ripple -->

    <div class="icon material-icons d-none d-lg-block" id='homeback' onclick="bckBtn()">keyboard_backspace</div>
    <div class="mdl-tooltip" data-mdl-for="homeback">Back</div>
    <li class="active topBarItem">
        <a href="#all" data-toggle="tab" class="d-none mdl-layout__tab mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect">
            <span>All</span>
        </a>
    </li>
    <%if("true".equals(hasQuiz)){%>
    <sec:ifLoggedIn>
        <li class="topBarItem">
            <a href="#create-test" data-toggle="tab"  class="mdl-button mdl-layout__tab mdl-button-modifier mdl-js-button mdl-js-ripple-effect ">
                <img src="${assetPath(src: 'resource/createtest.svg')}" class="mr-2">
                <span>Create Test</span>
            </a>
        </li>
    </sec:ifLoggedIn>
    <sec:ifNotLoggedIn>
        <li class="topBarItem">
            <a href="javascript:loginOpen()" class="mdl-button mdl-layout__tab mdl-button-modifier mdl-js-button mdl-js-ripple-effect ">
                <img src="${assetPath(src: 'resource/createtest.svg')}" class="mr-2">
                <span>Create Test</span>
            </a>
        </li>
    </sec:ifNotLoggedIn>
    <%}%>
    <sec:ifLoggedIn>
        <li class="topBarItem">
            <a href="#chapterNotes" data-toggle="tab"  class="mdl-button mdl-layout__tab mdl-button-modifier mdl-js-button mdl-js-ripple-effect">
                <img src="${assetPath(src: 'resource/notes.svg')}" class="mr-2" >
                <span>Highlights</span>
            </a>
        </li>
        <li class="d-none topBarItem">
            <a href="#allnotes-content" data-toggle="tab"  class="mdl-button mdl-layout__tab mdl-button-modifier mdl-js-button mdl-js-ripple-effect">
                <img src="${assetPath(src: 'resource/notes.svg')}" class="mr-2" >
                <span>all notes</span>
            </a>
        </li>
        <li class="topBarItem">
            <a href="#favMcqs" data-toggle="tab"  class="mdl-button mdl-layout__tab mdl-button-modifier mdl-js-button">
                <img src="${assetPath(src: 'resource/fav-icon.svg')}" class="mr-2" >
                <span>Favourite MCQs</span>
            </a>
        </li>
    </sec:ifLoggedIn>
    <sec:ifNotLoggedIn>
        <li class="topBarItem">
            <a href="javascript:loginOpen()"  class="mdl-button mdl-layout__tab mdl-button-modifier mdl-js-button mdl-js-ripple-effect">
                <img src="${assetPath(src: 'resource/notes.svg')}" class="mr-2" >
                <span>Highlights</span>
            </a>
        </li>
        <li class="d-none topBarItem">
            <a href="javascript:loginOpen()"  class="mdl-button mdl-layout__tab mdl-button-modifier mdl-js-button mdl-js-ripple-effect">
                <img src="${assetPath(src: 'resource/notes.svg')}" class="mr-2" >
                <span>all notes</span>
            </a>
        </li>
    </sec:ifNotLoggedIn>
    <button id="add-menu-lower-right" class="topBarItem mdl-button mdl-layout__tab mdl-button-modifier mdl-js-button mdl-js-ripple-effect" style="font-size: 10px;
    color: rgba(68, 68, 68, 0.85);">
        <img src="${assetPath(src: 'resource/add.svg')}" class="mr-2">Add
    </button>

    <ul class="mdl-menu mdl-menu--bottom-right mdl-js-menu mdl-js-ripple-effect"
        for="add-menu-lower-right">
        <sec:ifLoggedIn>
            <li class="mdl-menu__item" onclick="javascript:openVideos();">Add  Videos</li>
            <li class="mdl-menu__item" onclick="javascript:openSolution();">Add Web Link</li>
            <li class="mdl-menu__item" onclick="javascript:createNewSet();">Add Flashcard</li>
            <li class="mdl-menu__item" onclick="javascript:addNotesfromResource();">Add Notes</li>
        </sec:ifLoggedIn>
        <sec:ifNotLoggedIn>
            <li class="mdl-menu__item" onclick="javascript:loginOpenWithFunction('openVideos','Please login to add video');">Add  Videos</li>
            <li class="mdl-menu__item" onclick="javascript:loginOpenWithFunction('openSolution','Please login to add web link');">Add Web Link</li>
            <li class="mdl-menu__item" onclick="javascript:loginOpenWithFunction('createNewSet','Please login to flashcards');">Add Flashcards</li>
        </sec:ifNotLoggedIn>
    </ul>
</ul>
<div class="modal fade" id="successModal">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header">

            </div>

            <!-- Modal body -->
            <div class="modal-body">
                <i class="material-icons">check_circle_outline</i>
                <p>The set <span id="setName"></span> is <a id="msg-updated">created.</a></p>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" onclick="javascript:backToAll();" class="btn close-btn">Close</button>
            </div>

        </div>
    </div>
</div>

<main class="mdl-layout__content container-fluid pl-4 pr-4">
    <div class="page-content">
        <div class="row">
            <div class="col-12 col-lg-3 position-sticky side-chapters d-none">
                <g:render template="/resources/resourceChapterList"></g:render>
            </div>
            <div class="col-12 col-lg-9 resource-contents d-none">
                <g:render template="/resources/resourceContents"></g:render>
            </div>
        </div>

    </div>
</main>
<section class="related-book-wrapper">
    <div class="books-list">
        <g:render template="/resources/relatedBooks"></g:render>
    </div>
</section>

<div class="modal fade" data-backdrop="static" data-keyboard="false" id="networkCheckModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md modal-dialog-centered text-center">
        <div class="modal-content">
            <div class="modal-body">
                <div class="col-md-12 text-right">
                    <button type="button" class="btn btn-primary btn-sm" data-dismiss="modal"  aria-label="Close" id="videoClose"> <span aria-hidden="true">&times;</span></button>
                </div>
                <div>
                    <lottie-player src="https://assets1.lottiefiles.com/packages/lf20_6s2xGI.json"  background="transparent"  speed="1"  style="height: 300px;position: relative !important;"  loop  autoplay></lottie-player>
                    <h5 class="text-danger">No Internet Connection.</h5>
                </div>
            </div>
        </div>
    </div>
</div>
<%if(previewMode||!hasBookAccess){%>
<g:render template="/wonderpublish/buyOrAdd"></g:render>
<script>
    $('#prevBtn,#nextBtn').hide();
</script>
<%}%>


<script>
    var bookLang = "${book.language}" ;
    var bookContentCreatedBy = "${book.createdBy}";


    <%if(book!=null&&book.tags!=null){%>
    bookTags="${book.tags}";
    <%}%>
    var loggedInUser = false;


</script>

<sec:ifLoggedIn>
    <script>
        loggedInUser = true;
    </script>
</sec:ifLoggedIn>

<asset:javascript src="clock.js"/>
<asset:javascript src="moment.min.js"/>
<script src="https://cdn.ckeditor.com/4.7.1/full-all/ckeditor.js"></script>
<script>
    var receivedResId="${resId}";
    var previousChapterId=${topicId};
    var folderId = -1;
    lastReadTopicId = ${lastReadTopicId};
    $('#zoom-section').hide();
    var chapterHead='';
    var paramResId = ${resId};
    var resViewedFrom = "${params.viewedFrom}";
    var allTabMode = true;
    var chapterIdForPDF;
    var online=true;
    var chapterResponse;
</script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" integrity="sha384-nB0miv6/jRmo5UMMR1wu3Gz6NLsoTkbqJghGIsx//Rlm+ZU03BU6SQNC66uf4l5+" crossorigin="anonymous">
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" integrity="sha384-7zkQWkzuo3B5mTepMUcHkMB5jZaolc2xDwL6VFqjFALcbeS9Ggm/Yr2r3Dy4lfFg" crossorigin="anonymous"></script>
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk" crossorigin="anonymous"></script>

<script type="text/x-mathjax-config">
  MathJax.Hub.Config({tex2jax: {inlineMath: [['$','$'], ['\\(','\\)']]}});
</script>
<g:render template="/resources/allSection"></g:render>
<g:render template="/resources/readSection"></g:render>
<g:render template="/resources/notesHighlightSection"></g:render>
<g:render template="/resources/videoSection"></g:render>
<g:render template="/resources/weblinksSection"></g:render>
<g:render template="/resources/revision"></g:render>
<g:render template="/resources/flashcardMatch"></g:render>
<g:render template="/resources/bookTestSection"></g:render>
<g:render template="/resources/readerView"></g:render>
<g:render template="/resources/qaSection"></g:render>

<script>

    if(tempChapterId!=null && tempChapterId!="" &&  nextExam=="true") {
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="funlearn" action="chapterDetails" params="'topicId='+tempChapterId+'&mode=chapter&fromWeb=true'" onSuccess = "chapterDetailsRecieved(data);"/>
    }else {
        var tempTopicId = JSON.parse(localStorage.getItem('lastReadPDF'));
        var getChapterID = ${topicId};
        if (tempTopicId!=null && tempTopicId != "" && tempTopicId !=undefined){
            $('.loading-icon').removeClass('hidden');
            if((tempTopicId.pdfOpen=='true' || tempTopicId.pdfOpen==true) && tempTopicId.bookId==${params.bookId}){
                getChapterID = tempTopicId.chapterId;
            }
        }
        if ('${params.pubDesk}' == 'true' || '${params.pubDesk}' == true){
            getChapterID = '${params.chapterId}'
        }
        <g:remoteFunction controller="funlearn" action="chapterDetails" params="'topicId='+getChapterID+'&mode=chapter&fromWeb=true'" onSuccess = "chapterDetailsRecieved(data);"/>
    }


    if(tempChapterId!=null && tempChapterId!="" &&  nextExam=="true") {
        $("#chapterName" + tempChapterId).addClass('orangeText');
    }else{
        $("#chapterName" + previousChapterId).addClass('orangeText');
    }
    var noteschapterId=previousChapterId;
    function backToChapters() {
        $('.chapterList').show();
        $('.backChapters').addClass('d-none');
        $('.actionMenu').addClass('d-none');
        $('.read-content').addClass('d-none');
        $('#chapter-actions').addClass('d-none').removeClass('d-flex');
        $('.mobChapname').addClass('d-none').removeClass('d-flex');
        $('.subMenu .nav-tabs').show();
        $('.mobile-footer-resource').removeClass('d-flex').addClass('d-none');
    }

</script>
<asset:javascript src="zoom.js"/>
<script>
    var urlBookId= "${book.id}";
    document.onkeydown = function(e) {
        if(event.keyCode == 123) {
            return false;
        }
        if(e.ctrlKey && e.shiftKey && e.keyCode == 'I'.charCodeAt(0)){
            return false;
        }
        if(e.ctrlKey && e.shiftKey && e.keyCode == 'J'.charCodeAt(0)){
            return false;
        }
        if(e.ctrlKey && e.keyCode == 'U'.charCodeAt(0)){
            return false;
        }
    }



    function closeAddLink(){
        $('#addWeburl').modal('hide');
        if(allTabMode){
            $('#chapter-details-tabs a[href="#all"]').tab('show');
        }
    }
    function closeAddVideo(){
        $('#addVideo').modal('hide');
        if(allTabMode){
            $('#chapter-details-tabs a[href="#all"]').tab('show');
        }
    }
    function openSolution() {
        $('#addWeburl').modal('show');
    }

    function openFavMcq(){
        window.location.href = '/usermanagement/favouriteMcqs?linkSource=resourcesPage';
    }

</script>

<script>



    function viewChapterNotes() {
        var chapterId=noteschapterId;
        <g:remoteFunction controller="wonderpublish" action="getAnnotationsForFullBook"  onSuccess='renderChapterNotes(data);' params="'&chapterId='+chapterId+''" />
    }
    var backChapterId,backChapterName;
    function renderChapterNotes(data){
        $('#chapter-actions').removeClass('d-lg-flex');
        $('.chapter-notes,#chapter-all-action').show();
        $('#print-all-action').hide();
        var quotes=data.rows;
        var htmlStr='';
        if(quotes.length!=0) {
            htmlStr = " <div id=\"chapter-all-action\" class='col-9 mt-4 d-none d-lg-flex'>\n" +
                "                        <div class=\"icon material-icons\" id='backfromnotes' onclick=\"javascript:backToAll();\">keyboard_backspace</div>\n" +
                "<div class=\"mdl-tooltip\" data-mdl-for=\"backfromnotes\">\n" +
                "Back to <strong>Main</strong>\n" +
                "</div>"+
                "                    </div>"+"<div id='print-chapter-notes'> <ul class='notes-by-user'>";
            for (var i = 0; i < quotes.length; i++) {
                backChapterId = quotes[0].chapterId;
                backChapterName = quotes[0].chapterName.toString();
                if (quotes[i].text == null ) {
                    htmlStr += "<li><a>" + quotes[i].quote + "</a></li>";
                } else if (quotes[i].text != null) {

                    htmlStr += "<li>"+
                        "<h2>"+"<span class='comment-bg'>"+quotes[i].text+"</span>"+"</h2>"+
                        "<a>" + quotes[i].quote + "</a>"+
                        "</li>";
                }
            }
            htmlStr += "</ul></div>";
        }
        else{
            htmlStr+=" <div id=\"chapter-all-action\" class='col-9 mt-4'>\n" +
                "                        <div class=\"icon material-icons d-none d-md-block\" id='backfromnotes' onclick=\"javascript:backToAll();\">keyboard_backspace</div>\n" +
                "<div class=\"mdl-tooltip\" data-mdl-for=\"backfromnotes\">\n" +
                "Back to <strong>Main</strong>\n" +
                "</div>"+
                "                    </div>"+"<div class='text-center' style='margin:0 auto'>" +
                "<img src=\"${assetPath(src: 'ws/boxempty.svg')}\"  width='125px' height='117px'>"+
                "<p>Your Highlights is empty.<br>Please add your Highlights from chapters.</p>"+
                "</div>";
            // $('#print-all-action .print').attr('disabled','disabled');
        }
        document.getElementById('chapterNotes').innerHTML=htmlStr;
        garabageclear();
        window.componentHandler.upgradeDom();
    }
    function garabageclear() {
        if (bookLang != "English" && bookLang !="") {
            $('#print-chapter-notes ul li').addClass('d-none');
            $('#print-chapter-notes ul li:has("h2")').removeClass('d-none').addClass('d-block');
            var clearGarbage = document.querySelectorAll("#print-chapter-notes li a");

            for (var i = 0; i < clearGarbage.length; i++) {
                clearGarbage[i].style.display = "none";
            }
        }
    }


</script>


<script>
    if (localStorage.getItem("storageResId") != null) {
        function openReadMaterial() {
            var adminResId = localStorage.getItem("storageResId");
            displayReadingMaterial(adminResId);
            $('#content-data-all').addClass('d-none');
            localStorage.removeItem('storageResId');
        }
        openReadMaterial();

    }
    if (localStorage.getItem("storeAudio") != null) {
        function openAudio() {
            var storeAudio = localStorage.getItem("storeAudio");
            playHostedMediaAudio(storeAudio);
            localStorage.removeItem('storeAudio');
        }
        openAudio();
    }
    if (localStorage.getItem("storeVideo") != null) {
        function openVideo() {
            var storeVideo = localStorage.getItem("storeVideo");
            playHostedMediaVideo(storeVideo);
            localStorage.removeItem('storeVideo');
        }
        openVideo();
    }

    if(flashcardPresent){
        reviseNow(myResource,myResourceName);
        showPubSlide=true;
    }



    function closeSider(){
        $('.side-wrapper').hide();
        $('.btn-close-area p').text('open');
        $('.btn-close-area').attr('onclick','javascript:openSider();');
        // $('.side-chapters').removeClass('col-3').addClass('col-1');
        // $('.resource-contents').removeClass('col-9').addClass('col-11');
    }
    function openSider(){
        $('.side-wrapper').show();
        $('.btn-close-area p').text('close');
        $('.btn-close-area').attr('onclick','javascript:closeSider();');
        // $('.side-chapters').addClass('col-3').removeClass('col-1');
        // $('.resource-contents').addClass('col-9').removeClass('col-11');
    }


    function addNotesfromResource(){
        window.location='/resourceCreator/addNotes?resourceType=Notes&mode=create&bookId='+urlBookId+'&chapterId='+noteschapterId+'&chapterName='+selectedchapterName;

    }
    function shareBooks(){
        var booksUrl = "https://wonderslate.page.link/?apn=com.wonderslate.wonderpublish&ibi=com.wonderslate.app&link="+encodeURIComponent(window.location.href);
        openShareContentModal("ebooks", booksUrl);
    }
    $(document).ready(function (){
        selectedchapterName=$('.mdl-list__item.orangeText a').text();
        <sec:ifNotLoggedIn>
        <% if("true".equals(freeBook)&&!hasBookAccess) {%>
        openRegister("free",'${book.title}','${book.id}');
        <%}%>
        $("#closeButton").hide();
        </sec:ifNotLoggedIn>
    });
</script>

<script>
    if($(window).width()<=768) {
        if (chapterId) {
            $('.related-book-wrapper').show();
        }
    }

    $(document).ready(function () {
        if (bookLang != "English") {

            $('#htmlreadingcontent iframe').on('load', function () {
                var getIframe = $("#htmlreadingcontent iframe").contents().find(".annotator-wrapper .annotator-adder .google-search-btn");
                getIframe.css("display", "none");
                $("#htmlreadingcontent iframe").contents().find(".annotator-wrapper .annotator-adder .highlight-btn").css("margin-right", "0px");
                $("#htmlreadingcontent iframe").contents().find(".annotator-wrapper .annotator-adder .highlight-btn").css("padding-right", "30px");

            });
        }
    });

    if (loggedInUser) {
        // Book usage duration
        var startTime, duration;
        var currentDate = new Date();
        var logDate = currentDate.getDate() + "/" + (currentDate.getMonth() + 1) + "/" + currentDate.getFullYear();

        window.onload = function () {
            startTime = new Date().getTime();
        }

        window.onbeforeunload = function () {
            duration = new Date().getTime() - startTime;
            <g:remoteFunction controller="usermanagement" action="updateUserBookDurationLog" params="'logDate='+logDate+'&logDuration='+duration+'&bookId='+urlBookId" />
        }

        document.addEventListener('visibilitychange', function () {
            var state = document["visibilityState"];
            if (state == "visible") {
                startTime = new Date().getTime();
            } else if (state == "hidden") {
                duration = new Date().getTime() - startTime;
                <g:remoteFunction controller="usermanagement" action="updateUserBookDurationLog" params="'logDate='+logDate+'&logDuration='+duration+'&bookId='+urlBookId" />
            }
        });
    }

</script>

<g:render template="/${session['entryController']}/footer_new"></g:render>

<script>
    // This code taken from the resourceContents - Tab events for WS & Whitelabels
    function showVideos(){
        $('.nav-tabs a[href="#videos-tab"]').tab('show');
        $('html, body').animate({scrollTop: '0px'}, 300);
    }

    $('.nav-tabs a[href="#allnotes"]').on('shown.bs.tab',function () {
        viewAllNotes();
        $('.nav-tabs a[href="#allnotes-content"]').tab('show');
        if($(window).width()<=768){
            $('.resource-contents > .tab-content,.add-tabs').removeClass('d-none');
            $('.side-chapters').addClass('d-none');
            $('.mdl-layout__drawer-button').hide();
            $('.mobile-back-button').show();
            $('.mobile-back-button').attr('onclick','mobileBackToChapters()');
            $('html, body').animate({scrollTop: '0px'}, 300);
        }
    });
    $('.nav-tabs a[href="#allchapters"]').on('shown.bs.tab',function () {
        $('.nav-tabs a[href="#all"]').tab('show');
        $('html, body').animate({scrollTop: '0px'}, 300);
    });
    $(".nav-tabs a[href='#chapterNotes']").on('shown.bs.tab', function(){
        viewChapterNotes();
        $('.mobile-back-button').attr('onclick','backToAll()');
        $('html, body').animate({scrollTop: '0px'}, 300);
    });
    $(".nav-tabs a[href='#create-test']").on('shown.bs.tab', function(){
        createBookTest();
        $('.mobile-back-button').attr('onclick','backToAll()');
        $('html, body').animate({scrollTop: '0px'}, 300);
    });
    $(".nav-tabs a[href='#favMcqs']").on('shown.bs.tab', function(){
        $('.loading-icon').removeClass('hidden');
        openFavMcq();
    });
    if($(window).width()<=768) {
        if (chapterId) {
            $('.resource-contents > .tab-content,.add-tabs').removeClass('d-none');
            $('.side-chapters').addClass('d-none');
            $('#content-data-all').show().removeClass('d-none');
            $('.epub-action').hide();
        }
    }
    window.addEventListener('online',function (){
        online = true;
        $('#networkCheckModal').modal('hide');

    });
    window.addEventListener('offline', function (){
        online = false;
        $('#networkCheckModal').modal('show');

    });

    <%if("true".equals(session["commonWhiteLabel"])){%>
        document.querySelectorAll('.topBarItem').forEach(item=>{
            item.classList.add('d-none');
        })
    <%}%>

    function bckBtn(){
        if(document.referrer!="" && document.referrer.includes("ebook-details")){
            window.close()
        }else{
            window.history.back()
        }
    }
</script>

</main>

</div>

</div>

</div>
</html>



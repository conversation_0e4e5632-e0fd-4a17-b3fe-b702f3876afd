<!DOCTYPE html>
<g:render template="/wonderpublish/loginChecker"></g:render>

<g:render template="/${session['entryController']}/navheader_new"></g:render>
<style>
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .guest-user-alert,#top-navbar,header,.mdl-js-layout{
        display: none !important;
    }
}
@media only screen and (max-device-width: 1024px) and (min-device-width: 768px) and (orientation: portrait) {
    .guest-user-alert,#top-navbar,header,.mdl-js-layout{
        display: none !important;
    }
}
.title-wrapper {
    overflow: hidden;
}
</style>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.3/dist/katex.min.css" integrity="sha384-Juol1FqnotbkyZUT5Z7gUPjQ9gzlwCENvUZTpQBAPxtusdwFLRy382PSDx5UUJ4/" crossorigin="anonymous">

<script>
    var fromQuiz="${params.fromQuiz}";
</script>
<script type="text/javascript"
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
</script>

<script type="text/x-mathjax-config">
MathJax.Hub.Config({
tex2jax: {inlineMath: [['$','$'], ['\\(','\\)']]},
messageStyle: "none",
  showProcessingMessages: false
});
</script>
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.3/dist/katex.min.js" integrity="sha384-97gW6UIJxnlKemYavrqDHSX3SiygeOwIZhwyOKRfSaf0JWKRVj9hLASHgFTzT+0O" crossorigin="anonymous"></script>
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.3/dist/contrib/auto-render.min.js" integrity="sha384-+VBxd3r6XgURycqtZ117nYw44OOcIax56Z4dCRWbxyPt0Koah1uHoK0o4+/RRE05" crossorigin="anonymous"></script>

<div class="loading-icon">
    <div class="loading-icon">
        <div class="loader-wrapper">
            <div class="loader">Loading</div>
        </div>
    </div>
</div>
<div class="modal fade" id="successModal">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header">

            </div>

            <!-- Modal body -->
            <div class="modal-body">
                <i class="material-icons">check_circle_outline</i>
                <p>The set <span id="setName"></span> is <a id="msg-updated">created.</a></p>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" onclick="javascript:backToMain();" class="btn close-btn">Close</button>
            </div>

        </div>
    </div>
</div>
<section class="displaySliderTemplate">

    <div class="container" id="flashCardSets">



        <div role="tabpanel" class="tab-pane fade in show active" id="all">
            <div class="container">
                <div id="content-data-all" class="col-md-12 col-xs-12 col-sm-12"></div>
                <div id="htmlreadingcontent" class="row quiz-section"></div>

            </div>
        </div>



    </div>
    <div class="container" id="allCards">

    </div>
    <g:render template="/resources/flashcardMatch"></g:render>

    <div class="modal fade" id="expandFlashcard" role="dialog">
        <div class="modal-dialog modal-lg modal-dialog-centered justify-content-center">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title text-center" id="flashcard-name"></h4>
                </div>
                <div class="modal-body" style="overflow:auto;">
                    <div id="updateTerms" class="text-center"></div>
                    <div id="updateDefinition" class="text-center mt-2"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
    var previousChapterId = -1;
    var studySetResId ='${params.resId}';
    var flashCardsPresent = false;
    var noOfQuestions  =  "${params.noOfQuestions}"

    var editMode='${params.mode}';

</script>
<div class="d-none d-lg-block">
    <g:render template="/${session['entryController']}/footer_new"></g:render>
</div>

<g:render template="/resources/revision"></g:render>
<script>
    document.addEventListener("DOMContentLoaded", function() {
        renderMathInElement(document.body, {
            // customised options
            // • auto-render specific keys, e.g.:
            delimiters: [
                {left: '$$', right: '$$', display: true},
                {left: '$', right: '$', display: false},
                {left: '\\(', right: '\\)', display: false},
                {left: '\\[', right: '\\]', display: true}
            ],
            // • rendering keys, e.g.:
            throwOnError : false
        });
    });
</script>
<script>
    if(editMode==='edit'){
        goToEditScreen(studySetResId);
    }
    function showCompletedModal(){
        var setnameSaved=resname;
        document.getElementById('setName').innerHTML=setnameSaved;
        $('#successModal').modal('show');

    }
    function expandModal(receiveItems,type){
        console.log(type);
        var myTerms=keyValues[receiveItems].term;
        var myDefenition=keyValues[receiveItems].definition;
        document.getElementById('updateTerms').innerHTML=myTerms;
        document.getElementById('updateDefinition').innerHTML=myDefenition;
        document.getElementById('flashcard-name').innerHTML=resname;
        $('#expandFlashcard').modal('show');
        if(type===true){
            document.getElementById('updateTerms').innerHTML='';
        }
        else if(type===false){
            document.getElementById('updateDefinition').innerHTML='';
        }
        renderMathInElement(document.body);
    }

</script>






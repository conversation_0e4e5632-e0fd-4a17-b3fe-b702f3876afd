<%@ page import  = 'javax.servlet.http.Cookie' %>
<%  String requestURL = request.getRequestURL().toString();
String servletPath = request.getServletPath();
String appURL = requestURL.substring (0, requestURL.indexOf(servletPath));
session.setAttribute("servername", appURL);
def newCookie = new javax.servlet.http.Cookie( "siteName", "libwonder");
newCookie.path = "/"
response.addCookie newCookie;
%>
<g:render template="/libwonder/navheader_new"></g:render>

<style>
.banner_wrap {
    width: 90%;
    margin: 0 auto;
}
.LibWonder {
    width: 90%;
    margin: 10px auto !important;
}
.LibWonder, .LibWonder .ws-header {
    background: transparent;
    box-shadow: none;
}
.libwonder header.LibWonder {
    border-bottom: none;
}
@media only screen and (max-width: 1200px) {
    .banner_wrap {
        width: 100%;
    }
    .LibWonder {
        width: 100%;
        margin: 0 auto !important;
    }
}
</style>

<div class="curve-bg">
    <img src="${assetPath(src: 'libwonder/curve-bg.svg')}">
</div>
<section class="banner_wrap p-4 p-md-5 my-5">
    <div class="row mt-5">
        <div class="banner_info col-12 col-md-6">
            <h1 class="d-flex align-items-center mb-2">
                <strong>Lib</strong>Wonder <ion-icon name="chevron-forward-outline"></ion-icon> <span>eBooks</span>
            </h1>
            <h5>eBooks as in eLibraries are a passé.<br>Welcome to next generation <strong>eLib+</strong>.</h5>
            <div class="d-flex align-items-center justify-content-center justify-content-md-start mt-3 pt-3">
                <sec:ifNotLoggedIn>
                    <button class="btn login-btn mr-3" onclick="javascript:loginOpen()">Login</button>
                    <button class="btn signup-btn" onclick="javascript:signupModal()">Sign Up</button>
                </sec:ifNotLoggedIn>
                <sec:ifLoggedIn>
                    <a class="btn btn-lg signup-btn" href="/wsLibrary/myLibrary">Go to Library</a>
                </sec:ifLoggedIn>
            </div>
            <sec:ifNotLoggedIn>
            <div class="d-flex align-items-center justify-content-center d-md-none mt-4">
                <% if(showLibrary){%>
                <a class="btn btn-lg signup-btn" href="/wsLibrary/myLibrary">Go to Library</a>
                <%}%>
            </div>
            </sec:ifNotLoggedIn>
            <div class="d-flex align-items-center justify-content-center justify-content-md-start mt-4">
                <p>Available on</p>
                <i class="fa-brands fa-apple" style="color: rgba(68, 68, 68, 0.48);font-size: 16px;margin: 0 10px;"></i>
                <i class="fa-brands fa-google-play" style="color: rgba(68, 68, 68, 0.48);font-size: 16px;margin: 0 10px;"></i>
            </div>
        </div>
        <div class="banner_img col-12 col-md-6">
            <img src="${assetPath(src: 'libwonder/lib-banner.png')}" style="width: 75%;">
        </div>
    </div>
</section>

<g:render template="/libwonder/footer_new"></g:render>
<script>localStorage.removeItem("myinstitute");</script>
</body>
</html>


<div class="home-section institutes-area pt-4 mt-2 order-3">
    <div class="d-flex justify-content-between align-items-end pb-3">
        <h6 class="mb-0">Institutes</h6>
    </div>
    <div id="instituteLists" class="row flex-nowrap align-items-stretch ml-0" style="overflow-x: scroll;">
        <%for(int i=0;i<session["userInstitutes"].size();i++){%>
            <div class="institute-list col-6">
                <a id="institute_${session["userInstitutes"][i].id}" href="/instituteHome?instituteId=${session["userInstitutes"][i].id}&instituteName=${session["userInstitutes"][i].name}" class="card card-modifier text-center p-3 h-100">
                    <%if(session["userInstitutes"][i].logo!=null){%>
                        <img loading="lazy" class="institutes-logo" src="/institute/instituteImage?instituteId=${session["userInstitutes"][i].id}" alt="${session["userInstitutes"][i].name} Logo"/>
                    <%} else {%>
                        <asset:image src="/wonderslate/7-ws-institute.png"></asset:image>
                    <%}%>
                    <h6>${session["userInstitutes"][i].name}</h6>
                </a>
            </div>
        <%}%>
    </div>
</div>

<script>
    var instituteSize = ${session["userInstitutes"].size()};
    var instituteParentElement = document.getElementById("instituteLists");
    var instituteElement = document.querySelectorAll(".institute-list");
    if(instituteSize <= 2) {
        instituteParentElement.classList.replace('row', 'd-flex');
        instituteElement.forEach(element => {
            element.classList.replace("institute-list", "flex-fill");
        });
    }
</script>
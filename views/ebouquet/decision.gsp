
<g:render template="/ebouquet/navheader_new"></g:render>
<link rel="preconnect" href="https://fonts.gstatic.com">
<link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700;800&display=swap" rel="stylesheet">
<style>

.card {
    border-radius: 25px;
}
.card h1 {
    text-transform: uppercase;
    font-weight: bold;
    margin-bottom: 20px;
    font-family: 'Open Sans', sans-serif;
}
.card p {
    color: #000000;
    font-family: 'Open Sans', sans-serif;
}
.contact_form button {
    border-radius: 10px;
    text-transform: uppercase;
    font-weight: 700;
    font-family: 'Open Sans', sans-serif;
    background: #ffffff;
    color: #1E4598;
    font-size: 16px;
}


    /*.contact_form ::placeholder {*/
    /*    font-size: 12px !important;*/
    /*}*/
    /*.contact_form :-ms-input-placeholder {*/
    /*    font-size: 12px !important;*/
    /*}*/
    /*.contact_form ::-ms-input-placeholder {*/
    /*    font-size: 12px !important;*/
    /*}*/
    .contact_card {
        background-color: #1E4598;
        color: #FFF;
        border: none;
        border-radius: 12px;
    }
    .contact_card h4 {
        font-weight: 600;
        font-size: 18px;
        /*letter-spacing: 2px;*/
        font-family: 'Open Sans', sans-serif;
    }
    .contact_info {
        border-right: 1px solid #dee2e6;
    }
    @media only screen and (max-width:767px) {
        .contact_info {
            border-right: none;
            border-bottom: 1px solid #dee2e6;
        }
    }
    .contact_info ion-icon {
        margin-right: 1rem;
        padding: 10px;
        background: #FFF;
        border-radius: 50px;
        color: #1E4598;
    }
    .contact_info p {
        color: #FFF;
    }
    /*.contact_form .submit_btn {*/
    /*    background-color: #252425;*/
    /*    border-radius: 0;*/
    /*    padding: 7px 25px;*/
    /*}*/
    .contact_form input, .contact_form textarea {
        /*font-size: 14px;*/
        border: none;
        border-radius: 10px;
        color: #000000;
        font-family: 'Open Sans', sans-serif;
    }
    .contact_form .form-control:focus {
        color: #000000;
        border: none !important;
        box-shadow: none !important;
    }
    .contact_form textarea {
        min-height: 120px;
    }
    .contact_form .invalid-feedback {
        color: orangered;
    }
</style>

<section class="privacy">
    <div class="container">
        <div class="card shadow p-4 p-md-5 my-md-5 col-12 col-lg-10 mx-auto">
        <h1>Ready to make a decision</h1>
        <p class="mb-2">We are happy to curate the perfect list for you. Just fill the form below so we connect with you at the earliest!</p>
        <p class="mb-2">We are excited to add to your bouquet of choices.</p>
        <p>We can also be reached at <a href="mailto:<EMAIL>"><EMAIL></a> or call us right away and speak to our sales manager, Rajesh Raheja at 9821161908 for a discussion.</p>
        <div class="row card contact_card my-4 mx-0 col-12 col-md-8 col-lg-6">
            <div class="d-flex flex-wrap pt-3 pb-4">
%{--                <div class="contact_info col-12 col-md-6 mb-4 mb-md-0 py-4 py-md-0">--}%
%{--                    <h4 class="text-center pb-0 pb-md-5">GET IN TOUCH</h4>--}%
%{--                    <div class="d-flex align-items-center pt-3 pt-md-5">--}%
%{--                        <ion-icon name="call"></ion-icon>--}%
%{--                        <p>+91 9821161908</p>--}%
%{--                    </div>--}%
%{--                    <div class="d-flex align-items-center pt-3">--}%
%{--                        <ion-icon name="mail"></ion-icon>--}%
%{--                        <p><EMAIL></p>--}%
%{--                    </div>--}%
%{--                </div>--}%
                <div class="contact_form col-12">
                    <h4 class="text-center pb-3 pb-md-2">SEND US A MESSAGE</h4>
                    <form class="contact-form" method="post" action="/log/addContactForm" novalidate>
                        <div class="form-group">
                            %{--<label>Name <span class="text-danger">*</span> </label>--}%
                            <input type="text" name="name" class="form-control" placeholder="Name" required>
                            <div class="invalid-feedback">
                                Please enter your name.
                            </div>
                        </div>
                        <div class="form-group">
                            %{--<label>Email <span class="text-danger">*</span> </label>--}%
                            <input type="email" name="email" class="form-control" placeholder="Email" required>
                            <div class="invalid-feedback">
                                Please enter valid email address.
                            </div>
                        </div>
                        <div class="form-group">
                            %{--<label>Phone Number <span class="text-danger">*</span> </label>--}%
                            <input type="tel" class="form-control" name="phone" minlength="10" maxlength="10" placeholder="Mobile" required>
                            <div class="invalid-feedback">
                                Please enter valid phone number.
                            </div>
                        </div>
                        <div class="form-group">
                            %{--<label>Comment <span class="text-danger">*</span> </label>--}%
                            <textarea class="form-control" name="comment" placeholder="Message" required></textarea>
                            <div class="invalid-feedback">
                                Please enter your message.
                            </div>
                        </div>
                        %{--<div class="form-group form-check">
                            <label class="form-check-label"><input type="checkbox" name="copy" class="form-check-input"> Send me a copy of this submission.</label>
                        </div>--}%
                        <input type="hidden" name="noredirect" value="yes">
                        <div class="form-group text-center">
                            <button type="submit" class="btn col-md-4 submit_btn">Submit</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    </div>
</section>

<script src="https://unpkg.com/ionicons@5.1.2/dist/ionicons.js"></script>

<script>
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('contact-form');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    } else {
                        setTimeout(function(){
                            alert('Submitted successfully!');
                        },100);
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();
</script>

<g:render template="/ebouquet/footer_new"></g:render>

<%@ page import  = 'javax.servlet.http.Cookie' %>
<%  String requestURL = request.getRequestURL().toString();
String servletPath = request.getServletPath();
String appURL = requestURL.substring (0, requestURL.indexOf(servletPath));
session.setAttribute("servername", appURL);
def newCookie = new javax.servlet.http.Cookie( "siteName", "ebouquet");
newCookie.path = "/"
response.addCookie newCookie;
%>

<%@ page import="com.wonderslate.institute.InstituteIpAddress; com.wonderslate.data.UtilService; com.wonderslate.usermanagement.User; com.wonderslate.data.ResourceType" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <%if(header!=null&&!"notadded".equals(header)){%>
    <%= header %>
    <%}else{%>
    <title><%= title!=null?title:"SAGE | e-Bouquet"%></title>
    <meta name="description" content="SAGE | TEXTS">
    <%}%>
    <link rel="icon"  href="${assetPath(src: 'etexts/favicon.ico')}" type="image/x-icon">

    <meta name="theme-color" content="#1E4598" />
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <asset:stylesheet href="landingpage/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    %{--    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap" rel="stylesheet">--}%
    <asset:stylesheet href="landingpage/sageEvidya.css"/>
    <asset:stylesheet href="landingpage/fonts/flaticon.css"/>
    <asset:stylesheet href="landingpage/sageEbouquet.css"/>

    <style>
    .btco-hover-menu .show > .dropdown-toggle::after{
        transform: rotate(-90deg);
    }
    .btco-hover-menu ul li {
        position:relative;
        border-left: 1px solid white;
    }
    .btco-hover-menu ul li.dropdown:last-child {
        border-left: none;
    }
    .btco-hover-menu ul li:first-child {
        border-left: none;
    }
    .btco-hover-menu ul ul li {
        position:relative;
    }
    .btco-hover-menu ul ul li:hover> ul {
        display:block;
    }
    .btco-hover-menu ul ul ul {
        position:absolute;
        top:0;
        left:-100% !important;
        min-width:220px;
        display:none;
    }
    .btco-hover-menu ul li.login_button {
        border-right: none;
    }

    </style>

</head>
<%session['siteId'] = new Integer(24);%>


<body class="eutkarsh evidya etexts ebouquet" data-spy="scroll" data-target=".ws-header" data-offset="50">

<sec:ifNotLoggedIn>
%{--<g:render template="/funlearn/signupNew"></g:render>--}%
</sec:ifNotLoggedIn>

<header class="sageEvidya">
    <%
        if(params.tokenId==null){%>
    <div class="ebouquet-logo text-center p-3 pt-5">
        <a href="/ebouquet/index"><span class="logo"><img src="${assetPath(src: 'ebouquet/logo.png')}" alt="SAGE e-Bouquet"></span></a>
    </div>
    <div class="ws-menu-start">
        <nav class="ws-header container-fluid navbar navbar-expand-md navbar-default btco-hover-menu">
            <div class="d-none mobile-profile justify-content-center">
                <%if(session["userdetails"]==null){%>
                %{--<li class="nav-item">
                    <a class="nav-link login" id="evidyaLogin1">LOGIN & SIGN UP</a>
                </li>--}%
                <%}else{%>

                <li class="nav-item">
                    <a class="nav-link" class="dropdown-toggle" data-toggle="dropdown">
                        <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                        <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="rounded-circle">
                        <%} else { %> <img src="${assetPath(src: 'ebouquet/user-icon.png')}" alt="" class="rounded-circle bg-white">
                        <%}%>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right">
                        <div class="media p-3">
                            <a href="/creation/userProfile">
                                <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                                <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="mr-3 rounded-circle drop-profile">
                                <%} else { %> <img src="${assetPath(src: 'ebouquet/user-icon.png')}" alt="" class="mr-3 rounded-circle drop-profile bg-white">
                                <%}%>
                                <a href="/creation/userProfile" class="edit-btn"><i class="material-icons">edit</i></a>
                            </a>
                            <div class="media-body">
                                <p class="user-name">Hello <span id="loggedin-user-name"><%= session["userdetails"]!=null?session["userdetails"].name:"" %></span>,</p>
                                <p class="user-mail"><%= session["userdetails"]!=null?session["userdetails"].email:""%></p>
                            </div>
                        </div>
                        <a class="dropdown-item order-pr d-none" id="order-pr" href="\creation/userProfile#orders">Your Orders</a>
                        %{--<a class="dropdown-item" href="#">Wishlist</a>--}%
                        <a class="dropdown-item" href="/logoff" id="logout">Not <%= session["userdetails"]!=null?session["userdetails"].name:"" %>? <span>Sign out</span></a>
                    </div>
                </li>
                <%}%>
                <li class="nav-item right">
                    <div id="toggle" class="right">
                        <span></span><span></span><span></span><span></span>
                    </div>
                    <div id="menu">
                        <ul id="menu-left-menu" class="menu">
                            <li><a href="/ebouquet/store">DISCOVER</a></li>
%{--                            <li><a href="/ebouquet/howItWorks">HOW IT WORKS</a></li>--}%
                            <li><a href="/ebouquet/requestDemo">REQUEST A DEMO</a></li>
                            <li><a href="/ebouquet/contact">SUPPORT</a></li>
                            <% if(session['userdetails']!=null && (showLibrary)){%>
                            <li><a href="/library">MY <span style="font-size: 28px;">e-bouquet</span></a>
                                <%}%>
                            </li>
                        </ul>
                    </div>
                </li>
            </div>

            <ul class="navbar-nav right-menu d-flex d-md-flex align-items-center mx-auto">

                <li class="nav-item">
                    <a class="nav-link estore" href="/ebouquet/store">DISCOVER</a>
                </li>

%{--                <li class="nav-item">--}%
%{--                    <a class="nav-link" href="/ebouquet/howItWorks">HOW IT WORKS</a>--}%
%{--                </li>--}%
                <li class="nav-item">
                    <a class="nav-link" href="/ebouquet/requestDemo">REQUEST A DEMO</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/ebouquet/contact">SUPPORT</a>
                </li>
                <% if(session['userdetails']!=null && (showLibrary)){%>
                <li class="nav-item">
                <a class="nav-link" href="/library">MY <span style="font-size: 17px;">e-bouquet</span></a>
                </li>
                <%}%>

                <sec:ifNotLoggedIn>
                    %{--<li class="nav-item login_button">
                        <a class="nav-link" id="evidyaLogin">SIGN IN</a>
                    </li>--}%
                </sec:ifNotLoggedIn>

                <sec:ifLoggedIn>
                    <li class="nav-item dropdown">
                        <a class="nav-link p-0" class="dropdown-toggle" data-toggle="dropdown">
                            <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                            <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="rounded-circle">
                            <%} else { %> <img src="${assetPath(src: 'ebouquet/user-icon.png')}" alt="" class="rounded-circle bg-white">
                            <%}%>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right">
                            <div class="media p-3">
                                <a href="/creation/userProfile">
                                    <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                                    <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="mr-3 rounded-circle drop-profile">
                                    <%} else { %> <img src="${assetPath(src: 'ebouquet/user-icon.png')}" alt="" class="mr-3 rounded-circle drop-profile bg-white">
                                    <%}%>
                                    <span class="edit-btn"><i class="material-icons">edit</i></span>
                                </a>
                                <div class="media-body">
                                    <p class="user-name">Hello <span id="loggedin-user-name"><%= session["userdetails"]!=null?session["userdetails"].name:"" %></span>,</p>
                                    <p class="user-mail"><%= session["userdetails"]!=null?session["userdetails"].email:""%></p>
                                </div>
                            </div>
                            <a class="dropdown-item order-pr d-none" id="order-pr" href="\creation/userProfile#orders">Your Orders</a>
                            %{--<a class="dropdown-item" href="#">Wishlist</a>--}%
                            <a class="dropdown-item" href="/logoff" id="logout">Not <%= session["userdetails"]!=null?session["userdetails"].name:"" %>? <span>Sign out</span></a>
                        </div>
                    </li>
                </sec:ifLoggedIn>
            </ul>
        </nav>

    </div>
    <%}%>
</header>

<div class="modal" id="signInSuccess">
    <div class="modal-dialog  modal-sm modal-dialog-centered">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header">


            </div>

            <!-- Modal body -->
            <div class="modal-body">
                <p class="text-center" style="color:#233982;font-size: 16px;">Signed in Successfully</p>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                %{--                <button type="button" class="btn btn-danger" data-dismiss="modal">Continue</button>--}%
            </div>

        </div>
    </div>
</div>



<script>
    var defaultSiteName="${grailsApplication.config.grails.appServer.default}";


    function createAccount(){
        $('.evidyaloginWrapper').hide();
        $('.evidyaSignup').show();
    }
    function loginContinue() {
        $('.evidyaSignup').hide();
        $('.evidyaloginWrapper').show();
    }
</script>

<script src="https://apis.google.com/js/api:client.js"></script>
<script>
    var otpReg = "${otpReg}";

    var flds = new Array (
        'name',
        'username',
        'signup-password',
        'mobile',
        'state'
    );

    function userSignIn() {
        if($('#email').val()=="" || $('#password').val()=="") {
            $('#loginSignup').modal('show');
            $('#signInError').show();
        } else {
            $('#sign-in-div').hide();
            $('#connecting-div').show();
        }
    }

    function formSubmit() {
        if (validateSignUp()) {
            document.adduser.username.value = document.adduser.username.value.toLowerCase();
            document.adduser.email.value = document.adduser.username.value.toLowerCase();
            checkUsernameExists();
        }
    }

    function validateSignUp(){
        var allFilled=true;
        document.getElementById('emailexists').style.display = 'none';

        for (i=0; i<flds.length; i++) {
            if( !$("#"+flds[i]).val() ) {
                //actual code to check all fields needs to be entered. use the array of fields
                $("#"+flds[i]).css('border', '1px solid rgba(240, 90, 40, 0.5)');
                $("#"+flds[i]).css('border', '1px solid rgba(240, 90, 40, 0.5)');
                allFilled = false;
            } else {
                // $("#"+flds[i]).css('border', 'none');

            }
        }

        if(!allFilled) {
            $('.alert').show();
        } else {
            var email = $("#username").val();
            var atpos = email.indexOf("@");
            var dotpos = email.lastIndexOf(".");

            if (atpos<1 || dotpos<atpos+2 || dotpos+2>=email.length) {
                $("#username").css('border', '1px solid rgba(240, 90, 40, 0.5)');
                return false;
            }
        }

        return allFilled;
    }

    function checkUsernameExists() {
        var username = $("#username").val();
        <g:remoteFunction controller="creation" action="checkUserNameExists"  onSuccess='userNameExistsResult(data);' params="'username='+username" />
    }

    function userNameExistsResult(data) {
        if(data=="0") {
            $('#connecting-div').show();

            if(otpReg=="true") {
                $('#loginSignup').modal('hide');
                $('#otp-mobile').val($('#mobile').val());
                $('#otp-email-txt').text($('#username').val());
                $('#otp-email-txt1').text($('#username').val());
                $('#otp-mobile1').text($('#otp-mobile').val());
                $('#otp-next').modal('show');
            } else {
                document.adduser.submit();
            }

            $('#sign-up-div').hide();
        } else {
            $('#loginSignup').modal('show');
            document.getElementById('emailexists').style.display = 'block';
        }
    }

    function formFPSubmit() {
        $("#emailidnf").hide();

        if( !$("#fPemail").val() ) {
            //actual code to check all fields needs to be entered. use the array of fields
            alert("Please enter your registered email address");
            $("#email").addClass('has-error');
            $("#email").closest('.input-group').addClass('has-error');
        } else {
            var email = $("#fPemail").val();
            var atpos = email.indexOf("@");
            var dotpos = email.lastIndexOf(".");

            if (atpos<1 || dotpos<atpos+2 || dotpos+2>=email.length) {

                if(siteId==23){
                    document.getElementById('showEmailerror').innerHTML='Please enter valid email address.';
                }
                else {
                    return false;
                }
            } else {
                $("#loader").show();
                <g:remoteFunction controller="creation" action="forgottenPassword"  onSuccess='displayFPResults(data);'
						params="'email='+email" />
            }
        }
    }

    function displayFPResults(data) {
        var userEmail = $('#fPemail').val();

        if("OK"==data.status) {
            // $('#loginSignup').modal('show');
            // $('#loginSignup').attr('data-page', 'reset-completed');
            $('.forgotPassword').hide();
            $('#reset-password-completed').show();
            $('#fp-user-email').html("“"+userEmail+"�?");
        } else if("Google"==data.status) {
            $('.forgotPassword').hide();
            $('#reset-google-paswd').hide();
            $('#fp-user-email1').html("“"+userEmail+"�?");
        }
        else if("Fail"==data.status){
            $('.forgotPassword').show();
            $('#account-exists').show();
            document.getElementById('showEmailerror').innerHTML='Please enter valid email address.';
        }
    }

    <%  if(params.loginFailed=="true"&&!"24".equals(""+session["siteId"])) {%>
    $(window).on('load',function() {
        $('#loginSignup').modal('show');
    });
    <%  } %>
    function showForgetPassword() {
        $('.evidyaloginWrapper').hide();
        $('.forgotPassword').show();
    }
    function evidyabackSingup(){
        $('#account-exists').hide();
        $('.evidyaSignup').show();

    }

    function logoutClicked() {
        document.cookie = '';
        document.cookie = 'currentUrl' + "=" + window.location.href + "; path=/";
        // console.log(document.cookie);
        window.location.href ="/logoff";
    }


    function submitSignIn(){
        document.signin.username.value= "${session["siteId"]}_"+document.signin.username_temp.value;
        if ($("#email").val() == "" || $("#email").val() == null){
            alert("Please enter email");
        } else if ($("#password").val() == "" || $("#password").val() == null){
            alert("Please enter password");
        } else {
            document.signin.submit();
        }
    }
</script>



<style>
.reviewer_logins form label {
    font-size: 18px;
}
.reviewer_logins form input {
    font-size: 16px;
    border-width: 2px;
    border-color: #999;
    height: 45px;
    font-size: 16px;
}
.reviewer_logins .frgtPassword {
    font-size: 16px;
    color: #007bff;
}
.reviewer_logins #sign-in {
    padding: 10px 30px;
}
.reviewer_logins .forgotPassword #fPbtn {
    width: 100%;
    color: #ffffff;
    background: #1E4598;
    margin-top: 1rem !important;
}

#videoModal .modal-body .close {
    position: absolute;
    top: -10px;
    right: -10px;
    background: #fff;
    border-radius: 50px;
    width: 28px;
    height: 28px;
    line-height: 0;
    opacity: 1;
    border: 2px solid #000;
}
#videoModal .modal-body iframe {
    border: none;
}
@media screen and (min-width: 768px) and (min-height: 769px) {
    body.ebouquet {
        height: 100vh;
    }
    footer.footer-menu {
        position: fixed;
        width: 100%;
        bottom: 0;
    }
}
@media screen and (min-width: 1024px) and (max-width: 1150px) and (max-height: 850px) and (orientation : landscape) {
    body.ebouquet {
        height: auto !important;
    }
    footer.footer-menu {
        position: relative !important;
    }
}
</style>

<section class="content-Preview">

    <div class="container reviewer_logins col-12 col-sm-8 col-md-6 col-lg-4 py-4 py-lg-5 my-lg-4">
        <div class="">
            <div class="evidyaloginWrapper">
                <form method="post" action="/login/authenticate" name="signin">
            <div class="form-group row">
                <label for="email" class="col-sm-3 col-form-label">Username</label>
                <div class="col-sm-9">
                    <input type="text" class="form-control" id="email" name="username_temp" required>
                    <input type="hidden" name="username">
                </div>
            </div>
            <div class="form-group row">
                <label for="password" class="col-sm-3 col-form-label">Password</label>
                <div class="col-sm-9">
                    <input class="form-control mb-2" type="password" id="password" name="password" required>
                    <a href="javascript:showForgetPassword();" class="mb-3 frgtPassword">Forgot Password?</a>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-3 col-form-label d-none d-sm-block"></label>
                <div class="col-sm-9">
                    <input type="button" id="sign-in" class="btn btn-lg btn-primary mt-2" value="LOGIN" onclick="submitSignIn()">
                </div>
            </div>
        </form>
                <div id="loginFailedEvidya" style="display:none; color: #F05A2A; margin-top: 15px;">Login failed. Please try again!</div>
            </div>
            <div class="forgotPassword" style="display: none;">
                <h3>Reset Password</h3>
                <p class="mt-4">To recieve a link to reset your password, please enter your account email address.</p>
                <g:form name="forgotpassword" class="form-horizontal mt-4" method="post" autocomplete="off">
                    <div class="form-group row">
                        <label for="fPemail" class="col-sm-3 col-form-label">Email</label>
                        <div class="col-sm-9">
                            <input type="text" class="form-control" id="fPemail" name="username" required>
                            <input type="hidden" name="username">
                        </div>
                    </div>
                    <span id="showEmailerror" style="color:red;
                    display: block;
                    font-size: 14px;
                    text-align: center;"></span>
                    <button type="button" id="fPbtn" onclick="javascript:formFPSubmit();" class="btn btn-lg reset">Send reset link</button>
                    <a id="back-login" class="btn btn-back mt-4" onclick="javascript:evidyabackLogin();">Back to Login</a>
                </g:form>
            </div>
            <div id="reset-password-completed" style="display: none;">
                <div class="text-center">
                    <p>Password reset link sent.</p>
                    <p>We’ve sent instructions on how to reset your password to <span id="fp-user-email"></span>. If you haven’t received an email from e-bouquet within a couple of minutes, please check your spam folder.</p>
                    <a id="back-login" class="btn mt-4" onclick="javascript:evidyabackLogin();">Back to Login</a>
                </div>
            </div>
        </div>
    </div>
</section>

<g:render template="/evidya/commonfooter_new"></g:render>
<asset:javascript src="moment.min.js"/>

<%
    if(params.tokenId==null && !isBookPage){%>
<footer class="footer-menu pt-4">
    <div class="container">
        <div class="row align-items-center m-0">
            <div class="col-8">
                <div class="d-flex mb-2">
                    <p class="mb-0 pr-1"><strong>e-bouquet apps: </strong></p>
                    <a href="#"> web,</a>
                    <a href="#"> iOS,</a>
                    <a href="#"> Android,</a>
                    <a href="#"> Mac,</a>
                    <p class="mb-0 pr-1">and</p>
                    <a href="#"> PC</a>
                </div>
                <div class="d-flex mb-2">
                    <p class="mb-0 pr-1"><strong>What SAGE does: </strong></p>
                    <a href="https://in.sagepub.com/en-in/sas/home/" target="_blank"> our company,</a>
                    <a href="http://blog.sagepub.in/" target="_blank"> blog,</a>
                    <a href="https://in.sagepub.com/en-in/sas/books-india" target="_blank"> books,</a>
                    <a href="https://stealadeal.sagepub.in/" target="_blank"> stealadeal,</a>
                    <a href="https://in.sagepub.com/en-in/sas/journals-india" target="_blank"> journals,</a>
                    <a href="https://in.sagepub.com/en-in/sas/digital-library-products-india" target="_blank"> digital products</a>
                </div>
                <div class="d-flex mb-2">
                    <p class="mb-0 pr-1"><strong>Fine print: </strong></p>
                    <a href="/ebouquet/privacyPolicy"> privacy and cookie policy,</a>
                    <a href="/ebouquet/termsOfUse"> terms of service,</a>
                    <a href="/ebouquet/faq"> questions</a>
                </div>
                <div class="d-flex mb-2">
                    <p class="mb-0 pr-1"><strong>Copyright </strong> &copy; 2021-2022 SAGE. All rights reserved</p>
                </div>
                <ul class="d-flex flex-wrap social-icons pt-2 pl-0 mb-0">
                    <li><a href="https://www.facebook.com/SAGEPublicationsIndiaPvtLtd/" target="_blank"><img src="${assetPath(src: 'ebouquet/facebook.png')}" alt="Facebook Icon"> </a> </li>
                    <li><a href="https://www.instagram.com/sage_publishing_india/" target="_blank"><img src="${assetPath(src: 'ebouquet/instagram.png')}" alt="Instagram Icon"> </a> </li>
                    <li><a href="https://www.linkedin.com/company/sage-publications-india-pvt--ltd-" target="_blank"><img src="${assetPath(src: 'ebouquet/linkedin.png')}" alt="Linknedin Icon"> </a> </li>
                    <li><a href="https://www.youtube.com/user/sagepublications" target="_blank"><img src="${assetPath(src: 'ebouquet/youtube.png')}" alt="Youtube Icon"> </a> </li>
                    <li><a href="https://twitter.com/SAGEPubIndia" target="_blank"><img src="${assetPath(src: 'ebouquet/twitter.png')}" alt="Twitter Icon"> </a> </li>
                    <li><a href="mailto:<EMAIL>"><img src="${assetPath(src: 'ebouquet/email.png')}" alt="Email Icon"> </a> </li>
                </ul>

                %{--                <ul class="d-flex flex-wrap social-icons pt-2 pl-0 mb-0">--}%
                %{--                    <li><a href="termsofuse">Terms of Use</a></li>--}%
                %{--                </ul>--}%

            </div>

            <div class="col-4 promise-icon text-center">
                <img src="${assetPath(src: 'ebouquet/promise-icon.png')}" alt="Promise Icon">
            </div>
        </div>
    </div>
</footer>

<% } %>
<asset:javascript src="landingpage/popper.min.js" async="true"/>
<asset:javascript src="landingpage/bootstrap.min.js" async="true"/>

<asset:javascript src="landingpage/jquery.shorten.js" async="true"/>
<asset:javascript src="landingpage/slick.js" async="true"/>
<asset:javascript src="landingpage/index.js" async="true"/>

<sec:ifNotLoggedIn>
    <g:render   template="/creation/register"></g:render>
</sec:ifNotLoggedIn>



<script>
    $( "#evidyaLogin, #evidyaLogin1" ).click(function() {
        document.getElementById('loginFailedEvidya').style.display = 'none';
        $( ".evidyaLogin" ).toggle();
        $( ".browseMenu" ).hide();
        $('.forgotPassword,#reset-password-completed,#reset-google-paswd,.evidyaSignup').hide();
        $('.evidyaloginWrapper').show();
        $('body').removeClass('profile-popup-open');
    });
    $( ".browse").click(function() {
        $( ".browseMenu" ).toggle();
        $( ".evidyaLogin" ).hide();
        $('body').removeClass('profile-popup-open');
    });

    $(function(){
        $(".img-wrapper").hover(function(){
                $(this).find(".zoomImage").fadeIn();
            }
            ,function(){
                $(this).find(".zoomImage").fadeOut();
            }
        );
    });
</script>
<script>

    var serverPath= "${request.contextPath}";

    function showregister(registerType){
        $("#registerModal").modal("show");
        document.getElementById('forgotPassword').style.display = 'none';
        document.getElementById('tandc').style.display = 'none';

        if("login"==registerType) {
            document.getElementById('signup').style.display = 'none';
            document.getElementById('login').style.display = 'block';
            document.getElementById('loginFailedEvidya').style.display = 'none';
        }
        else{
            document.getElementById('login').style.display = 'none';
            document.getElementById('signup').style.display = 'block';
        }

    }


    <%if("true".equals(params.loginFailed)){%>
    $( ".evidyaLogin" ).toggle();
    $( ".browseMenu" ).hide();
    $( "#loginFailedEvidya" ).show();

    <%}%>

    var syllabusType="${syllabusType}";

    var country ="${country}";
    var noOfNotifications=0;
    var messagetype="message";
    function getTopicsMap(mode){
        <g:remoteFunction controller="funlearn" action="topicsMap"  onSuccess='initializeDataIndex(data);'
                params="'country='+country+'&mode='+mode" />
    }
    <%if("true".equals(showDiscover)||"true".equals(homeDiscover)){%>
    getTopicsMap('topic');
    <%}%>





    function getLocalDate(dateStr){
        var newDateStr = dateStr.substr(0,dateStr.length-18)+" "+dateStr.substr(dateStr.length-5);
        return newDateStr;
    }

    function getCookie(cookiename){
        // Get name followed by anything except a semicolon
        var cookiestring=RegExp(""+cookiename+"[^;]+").exec(document.cookie);
        // Return everything after the equal sign
        return unescape(!!cookiestring ? cookiestring.toString().replace(/^[^=]+./,"") : "");
    }

    function setSelectedValue(selectObj, valueToSet) {
        for (var i = 0; i < selectObj.options.length; i++) {
            if (selectObj.options[i].text.replace(/\W+/g, '') == valueToSet.replace(/\W+/g, '')) {
                selectObj.options[i].selected = true;
                return;
            }
        }
    }



    // checkNewMessages();
</script>


<script>
    function openSignup(){
        $('.evidyaLogin').show();
        createAccount();
    }
</script>
<script>
    function evidyaloginOpen() {
        $('#loginBenifits,#popupLogin').modal('hide');
        $('.evidyaLogin').show();
    }
    function evidyabackLogin() {
        $('.forgotPassword,#reset-password-completed,#reset-google-paswd').hide();
        $('.evidyaloginWrapper').show();
    }
</script>
<script>
    $(document).click(function(){
        $(".browseMenu,.evidyaLogin").hide();
        $('body').removeClass('profile-popup-open');

    });


    $(".browseMenu,.evidyaLogin,.browse,#evidyaLogin, #evidyaLogin1, #createProfile, .cmn-login,.logOfflibrary,input").click(function(e){
        e.stopPropagation();
        //$('body').removeClass('profile-popup-open');
    });

    $( "#createProfile" ).click(function() {
        document.getElementById('loginFailedEvidya').style.display = 'none';
        $( ".evidyaLogin" ).toggle();
        $( ".browseMenu" ).hide();
        $('.forgotPassword,#reset-password-completed,#reset-google-paswd,.evidyaSignup').hide();
        $('.evidyaloginWrapper').show();
        $('body').addClass('profile-popup-open');
    });

</script>
<%if("true".equals(session["successModal"])){
    session["successModal"] = null
%>
<script>
    $('#signInSuccess').modal('show');
</script>
<%}%>

<script>
    $( document ).ready( function () {
        $( '.dropdown-menu a.dropdown-toggle' ).on( 'click', function ( e ) {
            var $el = $( this );
            var $parent = $( this ).offsetParent( ".dropdown-menu" );
            if ( !$( this ).next().hasClass( 'show' ) ) {
                $( this ).parents( '.dropdown-menu' ).first().find( '.show' ).removeClass( "show" );
            }
            var $subMenu = $( this ).next( ".dropdown-menu" );
            $subMenu.toggleClass( 'show' );
            $( this ).parent( "li" ).toggleClass( 'show' );
            $( this ).parents( 'li.nav-item.dropdown.show' ).on( 'hidden.bs.dropdown', function ( e ) {
                $( '.dropdown-menu .show' ).removeClass( "show" );
            } );
            if ( !$parent.parent().hasClass( 'navbar-nav' ) ) {
                $el.next().css( { "top": $el[0].offsetTop, "left": $parent.outerWidth() - 4 } );
            }
            return false;
        } );
    } );


    $('#toggle').on('click', function(){
        $(this).toggleClass('open');
        $('body').toggleClass('active');
        $('body').removeClass('profile-popup-open');
    });
</script>



<asset:javascript src="landingpage/bootstrap-3-typeahead.js"/>

<script>
    $('#search-book').typeahead({
        minLength : 3,
        source: function(query, process) {
            $.ajax({
                url: '/discover/searchList',
                method: 'GET',
                data: {query:query},
                dataType: 'JSON',
                success: function fetchBooks1(data) {
                    process($.map(data.searchList, function(item) {
                        if(query === '') {
                            return(item);
                        } else {
                            return item;
                        }
                    }));
                }
            })
        },
        afterSelect: function(){
            submitSearch();
        }
    });

    function submitSearch(){
        $('.loading-icon').removeClass('hidden');
        var searchString = document.getElementById("search-book").value;
        window.location.href = "/etexts/store?search=true&searchString="+searchString;

    }


    $(document).on("keypress", "#search-book", function(e) {
        if (e.which == 13) {
            if (e.keyCode == 13) {
                if(document.getElementById("search-book").value == '' || document.getElementById("search-book").value == undefined || document.getElementById("search-book").value == null) location.reload();
                else submitSearch();
            }
        }
    });

    autoPlayYouTubeModal();

    //FUNCTION TO GET AND AUTO PLAY YOUTUBE VIDEO FROM DATATAG
    function autoPlayYouTubeModal() {
        var trigger = $("body").find('[data-toggle="modal"]');
        trigger.click(function () {
            var theModal = $(this).data("target"),
                videoSRC = $(this).attr("data-theVideo"),
                videoSRCauto = videoSRC + "?autoplay=1";
            $(theModal + ' iframe').attr('src', videoSRCauto);
            $(theModal + ' button.close').click(function () {
                $(theModal + ' iframe').attr('src', videoSRC);
            });
        });
    }

    $('#videoModal').on('hidden.bs.modal', function () {
        $('#videoModal iframe').removeAttr('src');
    })

</script>

<script>
    function SendLinkByMail() {
        var subject= "Free Trial | eTexts";
        var body = "";
        body += "";
        var uri = "mailto:<EMAIL>?subject=";
        uri += encodeURIComponent(subject);
        uri += "&body=";
        uri += encodeURIComponent(body);
        window.open(uri);
    }
</script>
<script>
    <sec:ifLoggedIn>
        window.location.href="/ebouquet/store";
    </sec:ifLoggedIn>
</script>

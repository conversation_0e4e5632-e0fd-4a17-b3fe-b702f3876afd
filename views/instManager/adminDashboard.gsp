<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container"><br>
   <h2 class="text-center">Institute: ${instituteName}</h2>
    <hr/>

    <!-- Overview Section -->
    <div class="row text-center">
    <%if(iBookGPTSiteAdmin){%>
        <div class="col-md-3 col-sm-6">
            <div class="panel panel-primary">
                <div class="panel-heading">Total Courses</div>
                <div class="panel-body">
                    <h3>${dashboardData.totalCourses}</h3>
                </div>
                <div class="panel-footer">
                    <g:link action="listCourses" class="btn btn-primary btn-block">Manage Courses</g:link>
                </div>
            </div>
        </div>
    <%}%>
        <div class="col-md-3 col-sm-6">
            <div class="panel panel-info">
                <div class="panel-heading">Total Batches</div>
                <div class="panel-body">
                    <h3>${dashboardData.totalBatches}</h3>
                </div>
                <div class="panel-footer">
                    <g:link action="listBatches" params="[instituteId: instituteId]" class="btn btn-info btn-block">Manage Batches</g:link>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6">
            <div class="panel panel-success">
                <div class="panel-heading">Total Users</div>
                <div class="panel-body">
                    <h3>${dashboardData.totalStudents}</h3>
                </div>
                <div class="panel-footer">
                    <g:link action="listUsersInBatch" params="[instituteId: instituteId]" class="btn btn-success btn-block">Manage Users</g:link>
                </div>
            </div>
        </div>
    <%if(iBookGPTSiteAdmin){%>
    <div class="col-md-3 col-sm-6">
        <div class="panel panel-success">
            <div class="panel-heading">Total Books</div>
            <div class="panel-body">
                <h3>${dashboardData.totalBooksAssigned}</h3>
            </div>
            <div class="panel-footer">
                <g:link action="listBooksInBatch" params="[instituteId: instituteId,batchId:defaultBatchId]" class="btn btn-info btn-block">Manage Books</g:link>
            </div>
        </div>
    </div>
    <%}%>
    <sec:ifAnyGranted roles="ROLE_GPT_MANAGER">
    <div class="col-md-3 col-sm-6">
        <div class="panel panel-warning">
            <div class="panel-heading">AI Prompts</div>
            <div class="panel-body">
                <h3><i class="fa fa-cogs"></i></h3>
            </div>
            <div class="panel-footer">
                <g:link action="manageInstitutePrompts" params="[instituteId: instituteId]" class="btn btn-warning btn-block">Manage Prompts</g:link>
            </div>
        </div>
    </div>
    </sec:ifAnyGranted>

    </div>
 <br><br>
    <!-- Recent Activities Section -->
    <div class="row">
        <!-- Recent Batches -->
        <div class="col-md-6">
            <h3>Recent Batches</h3>
            <table class="table table-striped">
                <thead>
                <tr>
                    <th>Batch Name</th>
                    <th>Course</th>
                    <th>Grade</th>
                </tr>
                </thead>
                <tbody>
                <g:each in="${dashboardData.recentBatches}" var="batch">
                    <tr>
                        <td>${batch.name}</td>
                        <td>${batch.courseName}</td>
                        <td>${batch.grade}</td>
                        </tr>
                </g:each>
                </tbody>
            </table>
        </div>

        <!-- Recent Users -->
        <div class="col-md-6">
            <h3>Recent Users</h3>
            <table class="table table-striped">
                <thead>
                <tr>
                    <th>Username</th>
                    <th>Role</th>
                </tr>
                </thead>
                <tbody>
                <g:each in="${dashboardData.recentUsers}" var="user">
                    <tr>
                        <td>${user.name}</td>
                        <td>${user.userType}</td>
                    </tr>
                </g:each>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Recent Books Assigned -->
    <div class="row">
        <div class="col-md-12">
            <h3>Recent Books Assigned</h3>
            <table class="table table-striped">
                <thead>
                <tr>
                    <th>Book Title</th>
                    <th>Batch</th>

                </tr>
                </thead>
                <tbody>
                <g:each in="${dashboardData.recentBooksAssigned}" var="bookInfo">
                    <tr>
                        <td>${bookInfo.bookTitle}</td>
                        <td>${bookInfo.batchName}</td>

                    </tr>
                </g:each>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Notifications or Announcements (Optional) -->
    <!-- You can add a section here if needed -->

</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>


</body>
</html>

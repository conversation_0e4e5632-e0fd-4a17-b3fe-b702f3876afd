<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<link href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
<script>
    var loggedIn=false;
</script>
<style>
    .table-bordered th,td {
        padding: 10px;
    }

    @media (min-width: 576px) {
        .modal-dialog-centered {
            min-height: calc(100% - 3.5rem);
        }
    }
    @media (min-width: 576px) {
        .modal-dialog {
            margin: 1.75rem auto;
        }
    }

    /* Prompt management specific styles */
    .prompt-container {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
    }

    .prompt-box {
        width: 48%;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 15px;
        background-color: #f9f9f9;
    }

    .prompt-box h4 {
        margin-top: 0;
        padding-bottom: 10px;
        border-bottom: 1px solid #ddd;
    }

    .prompt-list {
        min-height: 300px;
        max-height: 500px;
        overflow-y: auto;
        padding: 10px;
        background-color: #fff;
        border: 1px solid #eee;
        border-radius: 4px;
    }

    .prompt-item {
        padding: 10px;
        margin-bottom: 8px;
        background-color: #f5f5f5;
        border: 1px solid #ddd;
        border-radius: 4px;
        cursor: move;
    }

    .prompt-item:hover {
        background-color: #e9e9e9;
    }

    .tab-content {
        padding-top: 20px;
    }

    .save-btn {
        margin-top: 15px;
        float: right;
    }

    .ui-sortable-placeholder {
        border: 1px dashed #ccc;
        background-color: #f9f9f9;
        height: 40px;
        margin-bottom: 8px;
    }

    .alert-container {
        margin-top: 15px;
    }
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-10 main' style="margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h3 class="text-center">Manage Prompts for ${institute.name} Sir</h3>

                    <div class="alert-container">
                        <div id="success-alert" class="alert alert-success" style="display: none;"></div>
                        <div id="error-alert" class="alert alert-danger" style="display: none;"></div>
                        <div id="warning-alert" class="alert alert-warning" style="display: none;"></div>
                    </div>

                    <!-- Tabs -->
                    <ul class="nav nav-tabs" id="promptTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="student-tab" data-toggle="tab" href="#student" role="tab" aria-controls="student" aria-selected="true">Student Prompts</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="teacher-tab" data-toggle="tab" href="#teacher" role="tab" aria-controls="teacher" aria-selected="false">Teacher Prompts</a>
                        </li>
                    </ul>

                    <!-- Tab Content -->
                    <div class="tab-content">
                        <!-- Student Prompts Tab -->
                        <div class="tab-pane fade show active" id="student" role="tabpanel" aria-labelledby="student-tab">
                            <div class="prompt-container">
                                <div class="prompt-box">
                                    <h4>Available Prompts</h4>
                                    <div class="prompt-list" id="available-student-prompts">
                                        <g:each in="${availableStudentPrompts}" var="prompt">
                                            <div class="prompt-item" data-prompt-type="${prompt.promptType}">
                                                <strong>${prompt.promptLabel}</strong>
                                            </div>
                                        </g:each>
                                    </div>
                                </div>
                                <div class="prompt-box">
                                    <h4>Selected Prompts</h4>
                                    <div class="prompt-list" id="selected-student-prompts">
                                        <g:each in="${selectedStudentPrompts}" var="promptMst">
                                            <g:set var="prompt" value="${com.wonderslate.data.Prompts.findByPromptType(promptMst.promptType)}" />
                                            <div class="prompt-item" data-prompt-type="${promptMst.promptType}">
                                                <strong>${prompt?.promptLabel}</strong>
                                            </div>
                                        </g:each>
                                    </div>
                                    <button id="save-student-prompts" class="btn btn-primary save-btn">Save Student Prompts</button>
                                </div>
                            </div>
                        </div>

                        <!-- Teacher Prompts Tab -->
                        <div class="tab-pane fade" id="teacher" role="tabpanel" aria-labelledby="teacher-tab">
                            <div class="prompt-container">
                                <div class="prompt-box">
                                    <h4>Available Prompts</h4>
                                    <div class="prompt-list" id="available-teacher-prompts">
                                        <!-- Teacher prompts will be loaded via AJAX -->
                                        <div class="text-center loading-icon">
                                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                                            <p>Loading prompts...</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="prompt-box">
                                    <h4>Selected Prompts</h4>
                                    <div class="prompt-list" id="selected-teacher-prompts">
                                        <!-- Selected teacher prompts will be populated via drag and drop -->
                                    </div>
                                    <button id="save-teacher-prompts" class="btn btn-primary save-btn">Save Teacher Prompts</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>

<script>
    $(document).ready(function() {
        console.log('Document ready, initializing tabs and sortable lists');

        // Ensure Bootstrap tabs work correctly
        $('#promptTabs a').on('click', function (e) {
            e.preventDefault();
            $(this).tab('show');
            console.log('Tab clicked:', $(this).attr('href'));
        });

        // Initialize sortable lists
        $("#available-student-prompts, #selected-student-prompts").sortable({
            connectWith: ".prompt-list",
            placeholder: "ui-sortable-placeholder",
            cursor: "move"
        }).disableSelection();

        $("#available-teacher-prompts, #selected-teacher-prompts").sortable({
            connectWith: ".prompt-list",
            placeholder: "ui-sortable-placeholder",
            cursor: "move"
        }).disableSelection();

        // Load teacher data when teacher tab is shown
        $('#teacher-tab').on('shown.bs.tab', function (e) {
            console.log('Teacher tab shown');
            loadTeacherData();
        });

        // Ensure tabs are properly initialized
        setTimeout(function() {
            console.log('Ensuring tabs are properly initialized');
            $('#student-tab').tab('show');
        }, 500);

        // Save Student Prompts
        $("#save-student-prompts").click(function() {
            savePrompts('Student');
        });

        // Save Teacher Prompts
        $("#save-teacher-prompts").click(function() {
            savePrompts('Teacher');
        });

        // Function to save prompts
        function savePrompts(userType) {
            console.log('Saving prompts for userType:', userType);
            $('.loading-icon').removeClass('hidden');

            // Get the list of selected prompt types
            var selectedList = userType === 'Student' ? "#selected-student-prompts" : "#selected-teacher-prompts";
            var promptTypes = [];

            $(selectedList + " .prompt-item").each(function() {
                var promptType = $(this).data("prompt-type");
                console.log('Found prompt type:', promptType);
                promptTypes.push(promptType);
            });

            console.log('Prompt types to save:', promptTypes);

            // If no prompts are selected, show a message
            if (promptTypes.length === 0) {
                console.log('No prompts selected');
                $('.loading-icon').addClass('hidden');
                showAlert('#warning-alert', 'No prompts selected. Please select at least one prompt.');
                return;
            }

            var saveUrl = "${request.contextPath}/instManager/saveInstitutePrompts";
            console.log('Sending AJAX request to:', saveUrl);

            // Send AJAX request to save
            $.ajax({
                url: saveUrl,
                type: "POST",
                data: {
                    instituteId: ${institute.id},
                    userType: userType,
                    promptTypes: promptTypes.join(',')
                },
                success: function(response) {
                    console.log('Save response:', response);
                    $('.loading-icon').addClass('hidden');
                    if (response.status === 'success') {
                        showAlert('#success-alert', response.message);

                        // If we're saving teacher prompts, reload the teacher data
                        if (userType === 'Teacher') {
                            console.log('Reloading teacher data after save');
                            loadTeacherData();
                        } else {
                            // For student prompts, reload the page to show the updated data
                            console.log('Reloading page after saving student prompts');
                            setTimeout(function() {
                                window.location.reload();
                            }, 1500);
                        }
                    } else {
                        showAlert('#error-alert', response.message || 'Unknown error');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Save error:', status, error);
                    console.log('Response:', xhr.responseText);
                    $('.loading-icon').addClass('hidden');
                    showAlert('#error-alert', "Error: " + error + " (Status: " + xhr.status + ")");
                }
            });
        }

        // Function to show alert messages
        function showAlert(selector, message) {
            $(selector).html(message).show();
            setTimeout(function() {
                $(selector).fadeOut();
            }, 5000);
        }

        // Function to load teacher data
        function loadTeacherData() {
            console.log('Loading teacher data...');
            $('.loading-icon').removeClass('hidden');

            // Check if data is already loaded
            if ($('#selected-teacher-prompts .prompt-item').length > 0) {
                console.log('Teacher data already loaded, skipping fetch');
                $('.loading-icon').addClass('hidden');
                return;
            }

            // Log the URL we're calling
            var teacherDataUrl = "${request.contextPath}/instManager/getTeacherData";
            console.log('Fetching teacher data from:', teacherDataUrl);

            // Fetch teacher data via AJAX
            $.ajax({
                url: "${request.contextPath}/instManager/getTeacherData",
                type: "GET",
                data: {
                    instituteId: ${institute.id}
                },
                success: function(response) {
                    $('.loading-icon').addClass('hidden');

                    if (response.status === 'success') {
                        // Clear the loading icon and existing prompts
                        $('#available-teacher-prompts').empty();
                        $('#selected-teacher-prompts').empty();

                        console.log('Response data:', response);

                        // Add available prompts
                        if (response.data && response.data.length > 0) {
                            console.log('Adding available prompts:', response.data.length);
                            // Add actual data to available prompts
                            response.data.forEach(function(item) {
                                var promptItem = $('<div class="prompt-item" data-prompt-type="' + item.promptType + '">' +
                                                  '<strong>' + item.promptLabel + '</strong></div>');
                                $('#available-teacher-prompts').append(promptItem);
                            });
                        } else {
                            // If no available prompts, show a message
                            console.log('No available prompts');
                            $('#available-teacher-prompts').html('<div class="text-center"><p>No available prompts</p></div>');
                        }

                        // Add selected prompts
                        if (response.selectedData && response.selectedData.length > 0) {
                            console.log('Adding selected prompts:', response.selectedData.length);
                            response.selectedData.forEach(function(item) {
                                var promptItem = $('<div class="prompt-item" data-prompt-type="' + item.promptType + '">' +
                                                  '<strong>' + item.promptLabel + '</strong></div>');
                                $('#selected-teacher-prompts').append(promptItem);
                            });
                        } else {
                            // If no selected prompts, show a message
                            console.log('No selected prompts');
                            $('#selected-teacher-prompts').html('<div class="text-center"><p>No selected prompts</p></div>');
                        }

                        // Re-initialize sortable for teacher prompts
                        $("#available-teacher-prompts, #selected-teacher-prompts").sortable({
                            connectWith: ".prompt-list",
                            placeholder: "ui-sortable-placeholder",
                            cursor: "move"
                        }).disableSelection();

                        // Refresh sortable
                        $("#available-teacher-prompts, #selected-teacher-prompts").sortable('refresh');
                    } else {
                        showAlert('#error-alert', response.message || 'Failed to load teacher data');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX error:', status, error);
                    console.log('Response:', xhr.responseText);
                    console.log('Status code:', xhr.status);

                    $('.loading-icon').addClass('hidden');
                    showAlert('#error-alert', "Error loading teacher data: " + error + " (Status: " + xhr.status + ")");

                    // Add sample data on error
                    console.log('Adding sample data due to error');

                    // Clear the loading icon and existing prompts
                    $('#available-teacher-prompts').empty();
                    $('#selected-teacher-prompts').empty();

                    // Try to load selected prompts from the page
                    var selectedPrompts = [];
                    <g:each in="${selectedTeacherPrompts}" var="promptMst">
                        <g:set var="prompt" value="${com.wonderslate.data.Prompts.findByPromptType(promptMst.promptType)}" />
                        selectedPrompts.push({
                            promptType: "${promptMst.promptType}",
                            promptLabel: "${prompt?.promptLabel}"
                        });
                    </g:each>

                    // If we have selected prompts from the server, use them
                    if (selectedPrompts.length > 0) {
                        console.log('Using selected prompts from server:', selectedPrompts.length);
                        selectedPrompts.forEach(function(item) {
                            var promptItem = $('<div class="prompt-item" data-prompt-type="' + item.promptType + '">' +
                                              '<strong>' + item.promptLabel + '</strong></div>');
                            $('#selected-teacher-prompts').append(promptItem);
                        });
                    }

                    // If no selected prompts, show a message in the selected container
                    if (selectedPrompts.length === 0) {
                        $('#selected-teacher-prompts').html('<div class="text-center"><p>No selected prompts</p></div>');
                    }

                    // Show a message in the available container
                    $('#available-teacher-prompts').html('<div class="text-center"><p>Error loading available prompts</p></div>');

                    // Try to reload after a delay
                    setTimeout(function() {
                        console.log('Retrying to load teacher data...');
                        loadTeacherData();
                    }, 5000);

                    // Re-initialize sortable for teacher prompts
                    $("#available-teacher-prompts, #selected-teacher-prompts").sortable({
                        connectWith: ".prompt-list",
                        placeholder: "ui-sortable-placeholder",
                        cursor: "move"
                    }).disableSelection();

                    // Refresh sortable
                    $("#available-teacher-prompts, #selected-teacher-prompts").sortable('refresh');
                }
            });
        }

        // Load student data initially
        // This is already loaded from the server-side
    });
</script>

</body>
</html>

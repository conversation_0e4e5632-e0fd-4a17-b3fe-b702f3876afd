<style>
#mcqquestionsection .mcq-question-div{
  position: static !important;
  width: 100%;
  border-left: 1px solid #ededed;
  padding-left: 0.5rem;
}
#mcqquestionsection p{
  margin: 0;
}
  .test-gen-chapters .chapter-selection-table .chapter-selection{
    border-top:1px solid #ededed;
  }
  #quizModal #questionumber-containter > div > a{
    display: inline-flex;
    justify-content: center;
  }
.correct-answer-learn{
padding-bottom: 1rem;
}
.mcqquestion img{
  width: auto !important;
  height: auto !important;
  max-width: 200px !important;
  max-height: 200px !important;
}
.question-number-help-dropdown{
  padding: 10px !important;
}
</style>

<% if(!"books".equals(session["entryController"])){%>

<div class="modal" tabindex="-1" role="dialog" id="quizModal" style="overflow-y: auto;">
  <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
   <div class="modal-content">
    <div class="modal-header text-center d-flex justify-content-between">
      <h4 class="modal-title hello quizTitle">Created Test</h4>
      <button type="button" class="close close-modal close-sagemodal" onclick="javascript:resetTestType();">
        <i class="material-icons">close</i>
      </button>

    </div>
    <div class="practice-questions">
      <div id="mcqquestionsection">
        <div class="modal-body quiz-modal-body">

            <div id="quizdescription" style="display: none"></div>
              <div style="display: flex;">
                  <div class="questionumber-containter hidden-xs" id="questionNumberContainer" >
                     <div class="modal-footer quiz-modal-footer row-centered" id="footer-containter"></div>
                    <div id="questionumber-containter" style="position: static;width:250px;"></div>
                  </div>
                  <div class="mcq-question-div" id="individualQuestionContainer">
                    <div class="question-section-main">
                      <div id="passage" style="display: none; margin-bottom: 15px;"></div>
                      <div id="directions" style="display: none; margin-bottom: 15px;"></div>
                       <p class="question-title">Question:</p>
                      <div class="question practice-question" id="question"></div>
                       <p class="question-title" id="question-option" style="margin-top: 25px;">Options:</p>
                      <div class=" mcqquestion" id="mcqquestion" style="margin-top:0px;margin-bottom: 0px;"></div>
                    </div>
                    <div class="mcq-question-div mcq-learn">
                      <div class="row" id="answerpassage" style="display: none"></div>
                      <div class="row" id="quizanswerdescription" style="display: none"></div>
                      <div id="answer-holder" class="answer-holder"></div>
                    </div>
                </div>
                </div>
            </div>
        <div class="modal-footer quiz-modal-footer row-centered" id="quiz-modal-footer">
          <div class="col-md-4 done-btn-div col-xs-12 col-sm-4">
            <a href="javascript:done();" id="quiz-Done" class="btn submit-quiz-btn">Submit Exercise</a>
          </div>
          <div class="d-flex justify-content-between col-md-8 col-xs-12 col-sm-8" style="padding-left: 0;">
            <a href="javascript:prevQ();" id="prevButton" class="d-flex btn previous-btn pull-left disabled">
              <i class="material-icons" aria-hidden="true" style="position: relative; top: 3px;">chevron_left</i> Previous
            </a>
            <a href="javascript:nextQ();" class="btn next-btn disabled">Next</a>
          </div>
        </div>



      </div>
      <div id="score-container-div">
        <div class="score-container" id="score-container"></div>

      </div>
      <div class='modal-footer quiz-modal-footer' id="review-footer" style="display: none;">
        <div class='col-md-6 col-xs-6'>
          <a href='javascript:showPerformanceDetails();' class='btn btn-review pull-left'>Review</a>
        </div>
        <div class='col-md-6 col-xs-6'>
          <a href='#' id="close-quizz-modal" class='btn next-btn pull-right close-modal text-white' data-dismiss='modal'>Close</a>
        </div>
      </div>

    </div>
  </div>
</div>
</div>

<%}%>


<script>
$('#quizModal').on('click', '.close-modal', function(e) {
  $('.previous-btn').removeClass('disabled');
  e.preventDefault();
  $('.modal').hide();
  $('.modal-backdrop').hide();
  $('body').css({
    'overflow-y' : 'auto'
  });

  <% if(!"books".equals(session["entryController"])){%>
  document.getElementById('score-container').innerHTML = "";
  document.getElementById('answer-holder').style.display = "none";
  <% } else if("books".equals(session["entryController"])){%>
  window.location.href = "/test-generator";
  <%}%>

});
$('#quizModal').on('click', '.show-explanation-btn', function(e) {
  e.preventDefault();
  $(this).parents('.show-explanation').next('.correct-answer-explanation').slideToggle(100);
  $(this).html($(this).html() == 'Show Explanation' ? 'Hide Explanation' : 'Show Explanation');
});

</script>
<asset:javascript src="mcq.js"/>

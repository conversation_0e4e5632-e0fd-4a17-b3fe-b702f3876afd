<!doctype html>

<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Prepjoy Affiliation</title>
    <meta name="description" content="">
    <link rel="icon"  href="${assetPath(src: 'prepJoy/faviconPrepJoy.ico')}" type="image/x-icon">
    <link rel="windows-touch-icon" href="icon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0,maximum-scale=1.0, user-scalable=0" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Righteous&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"/>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.1/dist/css/bootstrap.min.css" rel="stylesheet" crossorigin="anonymous">
    <asset:stylesheet href="prepJoy/prepjoyWebsites/prepjoyFooter.css" async="true"/>
    <asset:stylesheet href="prepJoy/affiliation.css" async="true"/>
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
</head>

<body>

<div class="modal" id="formModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered modal-dialog-zoom" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="sign-up form">
                    <form>
                        <div class="form-group row">
                            <div class="col-sm-12">
                                <input type="text" class="form-control" id="inputName" placeholder="Name" required>
                            </div>
                        </div>
                        <div class="form-group row">
                            <div class="col-sm-12">
                                <input type="tel" class="form-control"  minlength="10"  maxlength="10"   pattern="[1-9]{1}[0-9]{9}"  onChange="mobileValidate()" id="mobileNumber" placeholder="Mobile Number" required>
                            </div>
                        </div>
                        <div class="form-group row">
                            <div class="col-sm-12">
                                <input type="email" class="form-control" name="email" pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$"  onchange="ValidateEmail(email)" id="email" placeholder="Email" required>
                            </div>
                        </div>
                        <div class="form-group row">
                            <div class="col-sm-12">
                                <input type="text" class="form-control" id="state" placeholder="State" required>
                            </div>
                        </div>
                        <div class="error-msg text-center">

                        </div>
                        <div class="error-msg1 text-center">

                        </div>
                        <div class="error-msg2 text-center">

                        </div>
                        <div class="submit_button">
                            <button id="submit-button" type="button" onclick="emailTemplate()">Join Us</button>
                        </div>
                    </form>
                </div>

            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="exampleModalCenter" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-zoom" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Congratulations you have completed step 1.</p>
                <p>We will reach out to you to complete this process</p>
            </div>
            <div class="submit_button">
                <a id="submit-button"  onclick="closeDialogue()" >Ok</a>
            </div>
            <lottie-player src="https://assets7.lottiefiles.com/packages/lf20_jEMHbp.json" id="discountAnimation" background="transparent"  speed="1" loop style="width: 300px; height: 300px;display: none"></lottie-player>

        </div>
    </div>
</div>
<nav class="navbar m-0">
    <div class="container">
        <div class="inner-navbar w-100 d-flex justify-content-between align-items-center">
            <div class="prepjoy-header__logo ml-0 ml-md-4 p-4">
                <a href="/prepjoy" target="_blank"><img src="${assetPath(src:'prepJoy/prepjoy.svg')}" /></a>
            </div>
            <div class="button-container">
                <a id="joinNow1" onclick="signupmodal()">Join Now</a>
            </div>
        </div>

    </div>

</nav>

<section class="banner hero-img py-5 first " id="second-banner">
    <div class="banner hero-img container py-5">
        <div class="banner-heading text-left col-12 col-md-5">
            <div class="prep-logo" data-aos="fade-in" data-aos-delay="100">
                <a href="/prepjoy" target="_blank"><img src="${assetPath(src:'prepJoy/prepjoy.svg')}" /></a>
            </div>
            <p class="text_effect_wrap">
                <span class="text_effect text_effect_left">Earn Money with Prepjoy</span>
                <span class="text_effect text_effect_left">Affiliation Program</span>
            </p>
            <div class="button-container" data-aos="fade-up" data-aos-delay="200">
                <a id="joinNow2" onclick="signupmodal()">Join Now</a>
            </div>
        </div>
        <div class="banner-img scene col-12 col-md-7">
            <div class="layer" data-depth="0.2">
                <img src="${assetPath(src:'prepJoy/bannerAffil.png')}"  data-aos="fade-up" data-aos-delay="300"/>
            </div>
        </div>
    </div>
</section>

<section class="affiliates program">
    <div class="container">
        <h1 class="text-center" data-aos="fade-up" data-aos-delay="100">
            <span class="section-title">Prepjoy</span>
            <span class="section-title-primary">Join</span>
        </h1>
        <h1 class="text-center" data-aos="fade-up" data-aos-delay="200">
            Affiliation program and start earning now
        </h1>
        <div class="three-card-parent">
            <div class="col-12 col-md-4 first-child" data-aos="fade-up" data-aos-delay="100">
                <div class="img-container">
                    <img src="${assetPath(src:'prepJoy/partner.png')}"  />
                </div>
                <div class="description-card text-center">
                    <h3> Register as affiliate partners</h3>
                    <p>Be among the first to earn revenue for referring eBooks for users. It’s free to join – no fees and no minimum sales. </p>

                </div>
            </div>
            <div class="col-12 col-md-4 second-child" data-aos="fade-up" data-aos-delay="300">
                <div class="img-container">
                    <img src="${assetPath(src:'prepJoy/ebook.png')}"  />
                </div>
                <div class="description-card text-center">
                    <h3> Promote eBooks</h3>
                    <p>Help to grow Wonderslate's global user base. When your visitors click a link and purchase an eBook, you make money.</p>

                </div>
            </div>
            <div class="col-12 col-md-4 third-child" data-aos="fade-up" data-aos-delay="500">
                <div class="img-container">
                    <img src="${assetPath(src:'prepJoy/profits.png')}"  />
                </div>
                <div class="description-card text-center">
                    <h3>Start earning</h3>
                    <p>Earn up to 10% money for each purchase of an eBook with your unique referral link. We offer regular and flexible payment options.</p>

                </div>
            </div>
        </div>
    </div>

</section>

<section class="information">
    <div class="container information-class">
        <h1 class="text-center" data-aos="fade-up" data-aos-delay="100">
            <span class="section-title">Prepjoy</span>
            <span class="section-title-primary">Why</span>
        </h1>
        <div class="four-card d-flex">
            <div class="col-12 col-md-3" data-aos="fade-up" data-aos-delay="200">
                <div class="img-container">
                    <img src="${assetPath(src:'prepJoy/people.png')}"/>
                </div>
                <div class="description-card text-center">
                    <h4>Over 1 Million Active Users</h4>
                </div>
            </div>
            <div class="col-12 col-md-3" data-aos="fade-up" data-aos-delay="300">
                <div class="img-container">
                    <img src="${assetPath(src:'prepJoy/ebook.png')}"/>
                </div>
                <div class="description-card text-center">
                    <h4>5000+ eBooks and Course</h4>
                </div>
            </div>
            <div class="col-12 col-md-3" data-aos="fade-up" data-aos-delay="400">
                <div class="img-container">
                    <img src="${assetPath(src:'prepJoy/responsive.png')}"/>
                </div>
                <div class="description-card text-center">
                    <h4>Available on desktop, mobile, iOS, and Android</h4>
                </div>
            </div>
            <div class="col-12 col-md-3" data-aos="fade-up" data-aos-delay="500">
                <div class="img-container">
                    <img src="${assetPath(src:'prepJoy/commission.png')}"/>
                </div>
                <div class="description-card text-center">
                    <h4>Highest Commission Percentage (10%)</h4>
                </div>
            </div>
        </div>
    </div>

</section>
<section class="banner hero-img py-5" id="second-banner">
    <div class="banner hero-img container py-5">
        <div class="banner-img scene_1 col-12 col-md-7">
            <div class="layer" data-depth="0.2">
                <img src="${assetPath(src:'prepJoy/affiliationBanner2.png')}" data-aos="fade-up" data-aos-delay="100"/>
            </div>
        </div>
        <div class="banner-heading text-left col-12 col-md-5">
            <div class="prep-logo" data-aos="fade-in" data-aos-delay="200">
                <a href="/prepjoy" target="_blank"><img src="${assetPath(src:'prepJoy/prepjoy.svg')}" /></a>
            </div>
            <p class="text_effect_wrap">
                <span class="text_effect text_effect_left">Earn Money with Prepjoy</span>
                <span class="text_effect text_effect_left">Affiliation Program</span>
            </p>
            <div class="button-container" data-aos="fade-up" data-aos-delay="300">
                <a id="joinNow" onclick="signupmodal()">Join Now</a>
            </div>
        </div>

    </div>
</section>
<section class="prepjoy-footer">
    <footer class="text-center">
        <div class="prepjoy-header__logo text-center">
            <a href="/prepjoy" ><img src="${assetPath(src:'prepJoy/prepjoy.svg')}" /></a>
            <p>Watch it, Read it, Play it</p>
        </div>
        <div class="prepjoy-footer__socialIcons">
            <ul>
                <li><a id="prepjoy-fb" href="https://www.facebook.com/prepjoycurrentaffairs" target="_blank"><i class="fa-brands fa-facebook"></i></a></li>
                <li><a id="prepjoy-insta" href="https://www.instagram.com/prepjoycurrentaffairs/" target="_blank"><i class="fa-brands fa-instagram"></i></a></li>
                <li><a id="prepjoy-tel" href="https://t.me/prepjoy" target="_blank"><i class="fa-brands fa-telegram"></i></a></li>
                <li><a id="prepjoy-yt" href="https://www.youtube.com/channel/UCLWNuBkvbS03Jax0RsaL6Tg" target="_blank"><i class="fa-brands fa-youtube"></i></a></li>
            </ul>
        </div>
    </footer>


</section>


<script src="https://code.jquery.com/jquery-3.1.1.min.js">
<script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.slim.min.js" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.1/dist/js/bootstrap.min.js" crossorigin="anonymous"></script>
%{-- JS Libraries --}%
<asset:javascript src="parallax.min.js"/>
<asset:javascript src="parallax-scroll.js"/>
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<g:render template="/prepjoy/prepjoy-loader"></g:render>



<script>
    var validMobile = false;
    var validEmail = false;
    var mobileErrorAppended = false;
    var emailErrorAppeded = false;
    var isCorrectAppended = false;
    var isAppended = false;
    function mobileValidate(){

        var mobileNumber = $('#mobileNumber').val();
        var mobilelength = mobileNumber.length;
        if(mobilelength != 10)
        {
            if(mobileNumber != "" )
            {
                $('.error-msg').empty();
                $('.error-msg1').empty().append($('<p style="color:#E83500;font-size:20px;">Please enter a valid number <p>'));

            }
            validMobile = false;
        }
        else if(mobilelength == 10)
        {
            $('.error-msg1').empty();
            validMobile = true;
        }
    }

    function signupmodal(){
        $('#formModal').modal('show');
        $('.error-msg1').show();
        $('.error-msg2').show();
        validMobile = false;
        validEmail = false;
    }
    function emailTemplate(){
        mobileValidate();
        $('#email').trigger("change");
        var name = $('#inputName').val();
        var email = $('#email').val();
        var state = $('#state').val();
        var mobileNo = $('#mobileNumber').val();

        if(name != "" && email != "" && state != "" && mobileNo != "" && validMobile && validEmail)
        {
            $('#loading').show();
            <g:remoteFunction controller="log" action="affiliationFormRecord"  onSuccess='enquired(data);'
              params="'name='+name+'&email='+email+'&state='+state+'&mobile='+mobileNo"/>
        }
        else if(name != "" && email != "" && state != "" && mobileNo != "" && (!validMobile || !validEmail) ){

                $('.error-msg').empty().append($('<p style="color:#E83500;font-size:20px;">Please fill in the correct details.<p>'));

                $('.error-msg1').hide();
                $('.error-msg2').hide();
            }

        else{

                $('.error-msg').empty().append($('<p style="color:#E83500;font-size:20px;">All fields are required <p>'));


        }
    }
    function enquired(data){
        successAnimation()
        $('#exampleModalCenter').modal('show');
        $('#formModal').modal('hide');
        $('#loading').hide();

    }
    $('#formModal').on('hidden.bs.modal', function () {
        $(this).find('form').trigger('reset');
        $('.error-msg').empty();
        $('.error-msg1').empty();
        $('.error-msg2').empty();
    });

    var player = document.querySelector("lottie-player");
    function successAnimation(){
        $('lottie-player').css('display','block');
        player.play();
    }


    $('#mobileNumber').keypress(function(e) {
        var a = [];
        var k = e.which;

        for (i = 48; i < 58; i++)
            a.push(i);

        if (!(a.indexOf(k)>=0))
            e.preventDefault();
    });

    function closeDialogue(){
        $('#exampleModalCenter').modal('hide');
        var link = "/prepjoy";
        window.open(link, '_blank');
    }



    $(window).scroll(function() {
        var scroll = $(window).scrollTop();

        if (scroll >= 500) {
            $(".navbar.m-0").addClass("scrolled");
        } else {
            $(".navbar.m-0").removeClass("scrolled");
        }
    });

    AOS.init({
        once: true,
        duration: 800,
    });

    if ($('.scene,.scene_1').length > 0 ) {
        $('.scene,.scene_1').parallax({
            scalarX: 10.0,
            scalarY: 10.0,
        });
    }

    var $text_effect = $('.text_effect_wrap');
    var $window = $(window);

    function scroll_addclass() {
        var window_height = $(window).height() - 100;
        var window_top_position = $window.scrollTop();
        var window_bottom_position = (window_top_position + window_height);

        $.each($text_effect, function () {
            var $element = $(this);
            var element_height = $element.outerHeight();
            var element_top_position = $element.offset().top;
            var element_bottom_position = (element_top_position + element_height);

            if ((element_bottom_position >= window_top_position) &&
                (element_top_position <= window_bottom_position)) {
                $element.addClass('is_show');
            }
        });
    }

    $window.on('scroll resize', scroll_addclass);
    $window.trigger('scroll');


    function ValidateEmail(input) {
        var name = $('#inputName').val();
        var email = $('#email').val();
        var mobileNo = $('#mobileNumber').val();
        var validRegex = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/;
        if (input.value.match(validRegex)) {
            $('.error-msg2').empty();
            validEmail = true;
            return true;
        } else {
            if( email != "" )
            {
                $('.error-msg').empty();
                $('.error-msg2').empty().append($('<p style="color:#E83500;font-size:20px;">Invalid email address!<p>'));

            }
            validEmail = false;
            return false;
        }
    }
    $(window).on('beforeunload', function(){
        $(window).scrollTop(0);
    });


    $("form").on('input', function(){

        var name = $('#inputName').val();
        var state = $('#state').val();
        var mobileNo = $('#mobileNumber').val();
        var mobilelength = mobileNo.length;
        if(mobilelength<10)
        {
            validMobile = false;
        }
        $("input").on('change', function(){
        if(!validMobile )
        {
            mobileValidate();
        }
        if(!validEmail && validMobile)
        {

            $('#email').trigger("change");
        }
        });
        if(name != "" && state != "" && validEmail && validMobile)
        {
            $('.error-msg').empty();
            $('.error-msg1').empty();
            $('.error-msg2').empty();
        }
    });

</script>
</body>
</html>

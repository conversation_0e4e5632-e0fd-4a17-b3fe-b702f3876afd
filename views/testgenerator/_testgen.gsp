<div class="modal modal-wide fade"   id="testgenerator" tabindex="-1" role="dialog" aria-labelledby="testgeneratorLabel" aria-hidden="true">

    <div class="modal-dialog modal-lg">
        <div class="modal-content ">

                <div class="modal-header sum-modal-header">

                <div class="row" style="margin-top:0px;margin-bottom: 0px">
                    <div class="col-md-12 text-right">
                        <button type="button" class="btn btn-success btn-sm" data-dismiss="modal" id="close-test"  aria-label="Close"> <span aria-hidden="true">&times;</span></button>
                    </div>
                </div>
                <span class="quizTitle sum-modal-header-text">Test Creator</span>
            </div>
            <div class="modal-body">

                <div id="testgeneratorcontainer" class="normaltext">
                    <div id="div1">
                        <div class="row"><div class="col-md-3 col-md-offset-1 ">
                            Create test from : </div>
                            <div class="col-md-3"> <select name="chapter" class="form-control" id="quizlevel" onchange="javascript:chapterModeSelected(1,this)">
                                <option value="">Select</option>
                                <option value="thischapter">Current Chapter</option>
                        <option value="multiplechapters">Multiple Chapters</option>
                        <option value="multiplebooks">Multiple Books</option></select>
                        </div> </div>
                    </div>
                    <div >
                        <br><div class='row'><div class='col-md-3 col-md-offset-1'>Select Books :</div><div class='col-md-8'><div class='row' id="div2"></div></div></div>
                    </div>
                    <div>
                        <br><div class='row'><div class='col-md-3 col-md-offset-1'>Select Chapters :</div><div class='col-md-8'><div class='row' id="div3"></div></div></div>
                    </div>
                    <div>
                        <br><div class='row'><div class='col-md-3 col-md-offset-1 '>Select test type : </div><div class='col-md-6'id="div4"></div></div>
                    </div>
                    <div >
                        <br><div class='row'><div class='col-md-3 col-md-offset-1'>Select difficulty level :</div><div class='col-md-6'><div class='row' id="div5"></div></div></div>
                    </div>

                    <div>
                        <br><div class='row'><div class='col-md-3 col-md-offset-1 '>Number of questions : </div><div class='col-md-6'  id="div6"></div></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                 <div class="row text-center" style="display: none" id="loading"><div class="col-md-7 orange" ><i class="fa fa-spinner fa-2x fa-spin"></i> </div></div>
                <div class="row text-left">
                    <div class="col-md-10 col-md-offset-1 smallerText greytext">** Only books and chapters which has quizzes will be displayed for selection.</div>
                </div>
                <div class="row text-left">
                    <div class="col-md-10 col-md-offset-1 smallerText greytext">** Passage based Multiple choice quizzes will not be included in the test generator.</div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var chapterMode;
    var currentNumber=1;
    var totalDivs=6;
    var bookId="${bookId}";
    var noOfChapters=0;
    var chapterIds="";
    var quizType='';
    var noOfDifficultyLevels=0;
    var difficultyLevels="";
    var noOfBooks=0;
    var bookIds="";
    var noOfQuestions=0;
    function testgen(){
        $("#testgenerator").modal('show');
        hideDivs(1);
        $("#loading").hide();
    }
    function chapterModeSelected(number,field){
        chapterMode = field[field.selectedIndex].value;
        if(field.selectedIndex==0){
            currentNumber = 1;
        }
       else{
            currentNumber=number+1;
            if("thischapter"==chapterMode){
               chapterIds = previousChapterId;
                $("#div2").hide(500);
                $("#div3").hide(500);
                $("#loading").show();
                <g:remoteFunction controller="testgenerator" action="getTestQuizTypes" onSuccess='displayQuizTypes(data);' params="'chaptersList='+chapterIds"/>
            }else  if("multiplebooks"==chapterMode){
                $("#div2").hide(500);
                $("#div3").hide(500);
                getUserBooks();
            }
            else getTestChapters();
        }
       hideDivs(currentNumber);
    }

    function hideDivs(number){
        var i=number+1;
        for(var i=(number+1);i<=totalDivs;i++){
            document.getElementById("div"+i).innerHTML="";
            $("#div"+i).hide(500);

        }
    }
    function getUserBooks(){
        $("#loading").show();
        <g:remoteFunction controller="testgenerator" action="getUserBooks" onSuccess='displayBooks(data);'/>
    }
    function getTestChapters(){
        $("#loading").show();
        <g:remoteFunction controller="testgenerator" action="getTestChapters" onSuccess='displayChapters(data);' params="'bookId='+bookId"/>
    }

    function displayBooks(data){
        $("#loading").hide();
        var books = data.results;
        noOfBooks = books.length;

        var displayStr="";
        for(i=0;i<noOfBooks;i++){
            // displayStr+="<div class='col-md-4'>"+(i+1)+". "+books[i].title+"&nbsp;<input type='checkbox' name='book"+i+"' id='book"+i+"' value='"+books[i].bookId+"'></div>";
            displayStr+= "<div class='col-md-6' style='padding-left:0; margin-bottom: 20px;'>"+"<input type='checkbox' name='book"+i+"' id='book"+i+"' value='"+books[i].bookId+"'>"+"&nbsp;"+(i+1)+". "+books[i].title+"</div>";
            if((i+1)%3==0) displayStr+="<br><br>";
        }
        displayStr +="</div><div class='row' id='booklistnext'><br><div class='col-md-10'><a href='javascript:getChaptersForBooks(true);' class='btn-primary' style='padding: 10px 10px; border-radius: 5px;'>NEXT</a></div></div>" +
                "<div class='row'><br><div class='col-md-10 red' id='booklistalert' style='display: none'>Please select atleast one book</div>";
        document.getElementById("div2").innerHTML=displayStr;
        $("#div2").show(500);
    }
    function displayChapters(data){
        $("#loading").hide();
        var chapters = data.results;
        noOfChapters = chapters.length;

        var displayStr="";
        for(i=0;i<noOfChapters;i++){
            displayStr += "<div class='col-md-6' style='padding-left:0; margin-bottom: 20px;'>"+"<input type='checkbox' name='chapter"+i+"' id='chapter"+i+"' value='"+chapters[i].id+"'>"+"&nbsp;"+(i+1)+". "+chapters[i].name+"</div>";

             //displayStr+="<div class='col-md-4'>"+(i+1)+". "+chapters[i].name+"&nbsp;<input type='checkbox' name='chapter"+i+"' id='chapter"+i+"' value='"+chapters[i].id+"'></div>";
            if((i+1)%3==0) displayStr+="<br><br>";
        }
        displayStr +="</div><div class='row' id='chapterlistnext'><br><div class='col-md-10'><a href='javascript:getTestQuizTypes(true);' class='btn-primary' style='padding: 10px 10px; border-radius: 5px;'>NEXT</a></div></div>" +
                "<div class='row'><div class='col-md-10 red' id='chapterlistalert' style='display: none'>Please select atleast one chapter</div>";
        document.getElementById("div3").innerHTML=displayStr;
        $("#div3").show(500);
    }

    function getChaptersForBooks(selectMode){
        bookIds="";
        for(i=0;i<noOfBooks;i++){
            if(document.getElementById('book'+i).checked){
                if(bookIds.length>0) bookIds+=","+document.getElementById('book'+i).value;
                else bookIds=document.getElementById('book'+i).value;
            }

        }
        if(bookIds.length>0){
            $("#booklistalert").hide(500);
            $("#booklistnext").hide(500);
            for(var i=0;i<noOfBooks;i++) document.getElementById("book"+i).disabled=true;
            $("#loading").show();
            <g:remoteFunction controller="testgenerator" action="getTestChapters" onSuccess='displayChapters(data);' params="'bookId='+bookIds"/>
        }
        else{
            $("#booklistalert").show(500);
        }
    }
    
    function getTestQuizTypes(selectMode){
        chapterIds="";
       for(i=0;i<noOfChapters;i++){
           if(document.getElementById('chapter'+i).checked){
               if(chapterIds.length>0) chapterIds+=","+document.getElementById('chapter'+i).value;
               else chapterIds=document.getElementById('chapter'+i).value;
           }

       }
        if(chapterIds.length>0){
            $("#chapterlistalert").hide(500);
            $("#chapterlistnext").hide(500);
            for(var i=0;i<noOfChapters;i++) document.getElementById("chapter"+i).disabled=true;
            $("#loading").show();
            <g:remoteFunction controller="testgenerator" action="getTestQuizTypes" onSuccess='displayQuizTypes(data);' params="'chaptersList='+chapterIds"/>
        }
        else{
            $("#chapterlistalert").show(500);
        }
    }

    function displayQuizTypes(data){
    $("#loading").hide();
        var quizTypes = data.results;
        var displayStr= "     " +
                " " +
                "                             <select name='quizType' id='quizType' class='form-control' onchange='javascript:getTestDifficultyLevels(3,this)'>" +
                "                                <option value=''>Select</option>" ;

        for(var i=0;i<quizTypes.length;i++){
            displayStr += "        <option value='"+quizTypes[i].resType+"'>"+quizTypes[i].resType+"</option>" ;

        }

        document.getElementById("div4").innerHTML=displayStr;
        $("#div4").show(500);
    }

    function getTestDifficultyLevels(number,field){
        quizType = field[field.selectedIndex].value;
        $("#loading").show();
        <g:remoteFunction controller="testgenerator" action="getTestDifficultyLevels" onSuccess='displayDifficultyLevels(data);' params="'chaptersList='+chapterIds+'&resType='+quizType"/>
    }

    function displayDifficultyLevels(data){
        $("#loading").hide();
        var difficultyLevel = data.results;
        noOfDifficultyLevels = difficultyLevel.length;
        if(noOfDifficultyLevels>0) {
            var displayStr = "";
            for (i = 0; i < noOfDifficultyLevels; i++) {
                displayStr += "<div class='col-md-4'>" + difficultyLevel[i].diffLevel + "&nbsp;<input type='checkbox' name='difflevel" + i + "' id='difflevel" + i + "' value='" + difficultyLevel[i].diffLevel + "'></div>";
                if ((i + 1) % 3 == 0) displayStr += "<br><br>";
            }
            displayStr += "</div><div class='row' id='diffLevelnext'><div class='col-md-10' style='margin-top: 15px;'><a href='javascript:getTestNumberOfQuestions(true);' class='btn-primary' style='padding: 10px 10px; border-radius: 5px;'>NEXT</a></div></div>" +
                    "<div class='row'><br><div class='col-md-10 red' id='diffLevelalert' style='display: none'>Please select atleast one difficulty level</div>";
            document.getElementById("div5").innerHTML = displayStr;
            $("#div5").show(500);
        }
        else{
            difficultyLevels="all";
            $("#loading").show();
            <g:remoteFunction controller="testgenerator" action="getTestNumberOfQuestions" onSuccess='displayNumberOfQuestions(data);' params="'chaptersList='+chapterIds+'&resType='+quizType+'&difficultyLevel=all'"/>
        }
    }

    function getTestNumberOfQuestions(selectMode){
        difficultyLevels="";
        for(i=0;i<noOfDifficultyLevels;i++){
            if(document.getElementById('difflevel'+i).checked){
                if(difficultyLevels.length>0) difficultyLevels+=","+"\'"+document.getElementById('difflevel'+i).value+"\'";
                else difficultyLevels="\'"+document.getElementById('difflevel'+i).value+"\'";
            }

        }
        if(difficultyLevels.length>0){
            $("#diffLevelalert").hide(500);
            $("#diffLevelnext").hide(500);
            for(var i=0;i<noOfDifficultyLevels;i++) document.getElementById("difflevel"+i).disabled=true;
            $("#loading").show();
            <g:remoteFunction controller="testgenerator" action="getTestNumberOfQuestions" onSuccess='displayNumberOfQuestions(data);' params="'chaptersList='+chapterIds+'&resType='+quizType+'&difficultyLevel='+difficultyLevels"/>
        }
        else{
            $("#diffLevelalert").show(500);
        }
    }

    function displayNumberOfQuestions(data){
        $("#loading").hide();
       var  questions = data.results;
        noOfQuestions = questions[0].questions;

        var displayStr= "<input type='text' maxlength='5' size='5' name='noOfQuestions' id='noOfQuestions'>&nbsp;&nbsp;("+questions[0].questions+" available) " ;

        displayStr += " <div class='row' id='createnext'><br><div class='col-md-10 col-md-offset-4'><a href='javascript:createTest();' class='btn-primary' style='padding: 10px 10px; border-radius: 5px;'>CREATE TEST</a></div></div>" +
                "<div class='row'><br><div class='col-md-10 red text-center' id='createalert' style='display: none'></div></div>";
        document.getElementById("div6").innerHTML=displayStr;
        $("#div6").show(500);
    }

    function createTest(){
        $("#createalert").hide();
       var field = document.getElementById("noOfQuestions");
        if(field.value==""){
            document.getElementById("createalert").innerHTML="Please enter number of questions";
            $("#createalert").show(500);
        }else if(isNaN(field.value)){
            document.getElementById("createalert").innerHTML="Please enter number only";
            $("#createalert").show(500);
        }else if(noOfQuestions<field.value){
            document.getElementById("createalert").innerHTML="Number of questions should be less than or equal to available questions.";
            $("#createalert").show(500);
        }else{
            document.getElementById("quizlevel").selectedIndex=0;
            $("#createalert").hide(500);
            quiz = {};
            quiz.title = "Created Test";
            quiz.link = "";
            quiz.type=quizType;
            quiz.mode="play";
            quiz.generated="true";
            $("#testgenerator").modal('hide');
            startTime = new Date();
            $("#loading").show();
            if("Match the answers"==quizType)
            <g:remoteFunction controller="funlearn" action="quizQuestionAnswers" onSuccess='initializeQuestions(data);' params="'chaptersList='+chapterIds+'&resType='+quizType+'&difficultyLevel='+difficultyLevels+'&noOfQuestions='+field.value+'&createtest=true'"/>
                    else
            <g:remoteFunction controller="funlearn" action="quizQuestions" onSuccess='initializeQuestions(data);' params="'chaptersList='+chapterIds+'&resType='+quizType+'&difficultyLevel='+difficultyLevels+'&noOfQuestions='+field.value+'&createtest=true'"/>

        }
    }

$('#close-test').click(function() {
    $('.create-test, .create-test a').removeClass('active');
    $('.obj-dropdown').find('.hidden-content').slideUp();
});

</script>
<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>



<div class="container">
    <div class="custom_container_new" style="margin-bottom: 2rem; margin-top: 1rem;">
        <a onclick="javascript:window.history.back();" style="cursor: pointer"><i class="material-icons mr-2">keyboard_backspace</i></a>
    </div>
    <h3>Add / Edit Vendor Delivery Details</h3>
    <form id="vendorDeliveryForm" method="POST" >

       <div class="row">
           <div class="col-md-3">
        <div class="form-group">
            <label for="calculationType">Calculation Type:</label>
            <select class="form-control" id="calculationType" name="calculationType" required>
                <option value="">Select Calculation Type</option>
                <option value="Location" <%=vendorDeliveryDetails!=null&&"Location".equals(vendorDeliveryDetails.calculationType)?"selected":""%>>Location</option>
                <option value="Weight" <%=vendorDeliveryDetails!=null&&"Weight".equals(vendorDeliveryDetails.calculationType)?"selected":""%>>Weight</option>
            </select>
        </div>
           </div>

            <div class="col-md-3">
                <div class="form-group">
                    <label for="threshold">Free delivery threshold</label>
                    <input type="number" class="form-control" id="threshold" name="threshold" value="<%=vendorDeliveryDetails!=null&&vendorDeliveryDetails.threshold!=null?vendorDeliveryDetails.threshold:""%>">
                </div>
            </div>

            <div class="col-md-3">
                <div class="form-group">
                    <label for="threshold">Flat fees</label>
                    <input type="number" class="form-control" id="flatFee" name="flatFee" value="<%=vendorDeliveryDetails!=null&&vendorDeliveryDetails.flatFee!=null?vendorDeliveryDetails.flatFee:""%>">
                </div>
            </div>
        </div>
        <button type="submit" class="btn btn-primary" onclick="validateForm(event)">Save</button>
    </form>
<br><br>



<div id="addLocation" style="display: none">
    <form name="locationForm" id="locationForm">
    <div class="row">
        <div class="col-md-12"><h4>Add Locations</h4></div>
    </div>
    <div class="row">
        <div class="col-md-3">
            <div class="form-group">
                <label for="locationType">Location Type:</label>
                <select class="form-control" id="locationType" name="locationType" required>
                    <option value="">Select Location Type</option>
                    <option value="City">City</option>
                    <option value="State">State</option>
                    <option value="Country">Country</option>
                    <option value="Default">Default</option>
                </select>
            </div>
        </div>

        <div class="col-md-3">
            <div class="form-group">
                <label for="locationName">Location name</label>
                <input type="text" class="form-control" id="locationName" name="locationName" required>
            </div>
        </div>

        <div class="col-md-3">
            <div class="form-group">
                <label for="fee">Fees</label>
                <input type="number" class="form-control" id="fee" name="fee" required>
            </div>
        </div>
    </div>
    <button type="submit" class="btn btn-primary" onclick="validateLocationForm(event)">Save</button>
    </form>

    <div class="row" >
        <div class="col-md-9" id="existingLocations" style="display: none">

        </div>
    </div><br><br>

</div>


<div id="addWeight" style="display: none">
    <form name="weightForm" id="weightForm">
        <div class="row">
            <div class="col-md-12"><h4>Add Weights</h4></div>
        </div>

        <div class="row">
            <div class="col-md-3">
                <div class="form-group">
                    <label for="weightFrom">Weight From (Grams)</label>
                    <input type="text" class="form-control" id="weightFrom" name="weightFrom" required>
                </div>
            </div>

            <div class="col-md-3">
                <div class="form-group">
                    <label for="weightTo">Weight To (Grams)</label>
                    <input type="text" class="form-control" id="weightTo" name="weightTo" required>
                </div>
            </div>

            <div class="col-md-3">
                <div class="form-group">
                    <label for="weightFee">Fees</label>
                    <input type="number" class="form-control" id="weightFee" name="weightFee" required>
                </div>
            </div>
        </div>
        <button type="submit" class="btn btn-primary" onclick="validateWeightForm(event)">Save</button>
    </form><br><br>

    <div class="row" >
        <div class="col-md-9" id="existingWeights" style="display: none">

        </div>
    </div><br><br>

</div>
    <form name="feesTester" id="feesTester">
        <div class="row">
            <div class="col-md-12"><h5>Test calculation</h5></div>
        </div>

        <div class="row">
            <div class="col-md-3">
                <div class="form-group">
                    <label for="totalWeight">Total Weight (Grams)</label>
                    <input type="text" class="form-control" id="totalWeight" name="totalWeight" >
                </div>
            </div>

        <div class="col-md-3">
            <div class="form-group">
                <label for="testerLocationName">Location name</label>
                <input type="text" class="form-control" id="testerLocationName" name="testerLocationName" >
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label for="bookPrice">Total books price</label>
                <input type="number" class="form-control" id="bookPrice" name="bookPrice" required>
            </div>
        </div>
        </div>

        <button type="submit" class="btn btn-primary" onclick="getFees(event)">Check Fees</button><br>
        <div class="row">
            <div class="col-md-9" id="feesMessage"></div>
        </div>
    </form><br><br>
</div>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script>
    <%if(vendorDeliveryDetails!=null&&"Location".equals(vendorDeliveryDetails.calculationType)){%>
    $("#addWeight").hide();
    $("#addLocation").show();
    getDeliveryLocations();
    <%}else if(vendorDeliveryDetails!=null&&"Weight".equals(vendorDeliveryDetails.calculationType)){%>
    $("#addWeight").show();
    $("#addLocation").hide();
    getDeliveryWeights();
    <%}%>

    function deleteDeliveryWeight(weightId){
        if(confirm("Are you sure to delete?")) {
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="deliveryCharges" action="deleleDeliveryWeight" params="'weightId='+weightId" onSuccess = "weightDeleted(data);"/>
        }
    }

    function weightDeleted(data){
        getDeliveryWeights();
    }
    function getDeliveryWeights(){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="deliveryCharges" action="getDeliveryWeights" params="'vendorId=${vendorId}'" onSuccess = "displayWeights(data);"/>

    }
    function displayWeights(data) {
        $('.loading-icon').addClass('hidden');
        var weights = data.deliveryWeights;
        var htmlStr;
        if (weights.length == 0) htmlStr = "";
        else {
            htmlStr = "<table width=\"100%\" border='1'>\n" +
                "            <tr>\n" +
                "                <th width=\"30%\">Weight From</th>\n" +
                "                <th width=\"30%\">Weight To</th>\n" +
                "                <th width=\"30%\">Fees</th>\n" +
                "                <th></th>\n" +
                "            </tr>";

        for (var i = 0; i < weights.length; i++) {
            htmlStr += " <tr>\n" +
                "                <td>" + weights[i].weightFrom + "</td>\n" +
                "                <td>" + weights[i].weightTo + "</td>\n" +
                "                <td>" + weights[i].fee + "</td>\n" +
                "                <td><a href='javascript:deleteDeliveryWeight(" + weights[i].id + ");'>Delete</a></td>\n" +
                "            </tr>";
        }

        htmlStr += "            </table>\n" +
            "        </div>";
    }
        document.getElementById("existingWeights").innerHTML=htmlStr;
        $("#existingWeights").show(500);
    }

    function deleteDeliveryLocation(locationId){
        if(confirm("Are you sure to delete?")) {
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="deliveryCharges" action="deleleDeliveryLocation" params="'locationId='+locationId" onSuccess = "locationDeleted(data);"/>
        }
    }

    function locationDeleted(data){
        getDeliveryLocations();
    }
    function getDeliveryLocations(){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="deliveryCharges" action="getDeliveryLocations" params="'vendorId=${vendorId}'" onSuccess = "displayLocations(data);"/>

    }
    function displayLocations(data){
        $('.loading-icon').addClass('hidden');
        var locations = data.deliveryLocations;
        var htmlStr;
        if (locations.length == 0) htmlStr = "";
        else {
            htmlStr = "<table width=\"100%\" border='1'>\n" +
                "            <tr>\n" +
                "                <th width=\"30%\">Location Type</th>\n" +
                "                <th width=\"30%\">Location name</th>\n" +
                "                <th width=\"30%\">Fees</th>\n" +
                "                <th></th>\n" +
                "            </tr>";
            for (var i = 0; i < locations.length; i++) {
                htmlStr += " <tr>\n" +
                    "                <td>" + locations[i].locationType + "</td>\n" +
                    "                <td>" + locations[i].locationName + "</td>\n" +
                    "                <td>" + locations[i].fee + "</td>\n" +
                    "                <td><a href='javascript:deleteDeliveryLocation(" + locations[i].id + ");'>Delete</a></td>\n" +
                    "            </tr>";
            }

            htmlStr += "            </table>\n" +
                "        </div>";
        }
        document.getElementById("existingLocations").innerHTML=htmlStr;
        $("#existingLocations").show(500);
    }
    function validateLocationForm(event){


        event.preventDefault(); // Prevents the default form submission

        if(genericValidateForm("locationForm")) {
            var locationType = document.getElementById("locationType").value;
            var locationName = document.getElementById("locationName").value;
            var fee = document.getElementById("fee").value;

            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="deliveryCharges" action="saveLocationDeliveryFeesForVendor" params="'vendorId=${vendorId}&locationType='+locationType+'&locationName='+locationName+'&fee='+fee" onSuccess = "locationSaved(data);"/>

        }
    }

    function locationSaved(data){
        document.getElementById("locationType").value="";
        document.getElementById("locationName").value="";
        document.getElementById("fee").value="";

        getDeliveryLocations();
    }

    function validateWeightForm(event){
        event.preventDefault(); // Prevents the default form submission

        if(genericValidateForm("weightForm")) {
            var weightFrom = document.getElementById("weightFrom").value;
            var weightTo = document.getElementById("weightTo").value;
            var fee = document.getElementById("weightFee").value;

            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="deliveryCharges" action="saveWeightDeliveryFeesForVendor" params="'vendorId=${vendorId}&weightFrom='+weightFrom+'&weightTo='+weightTo+'&fee='+fee" onSuccess = "weightSaved(data);"/>

        }
    }

    function weightSaved(data){
        $('.loading-icon').addClass('hidden');
        document.getElementById("weightFrom").value="";
        document.getElementById("weightTo").value="";
        document.getElementById("weightFee").value="";
        getDeliveryWeights();

    }

    function validateForm(event) {
        event.preventDefault(); // Prevents the default form submission

       if(genericValidateForm("vendorDeliveryForm")) {
           var calculationType = document.getElementById("calculationType").value;
           var threshold = document.getElementById("threshold").value;
           var flatFee = document.getElementById("flatFee").value;

           // Perform additional validation if needed

           if (!calculationType) {
               alert("Please select calculation type.");
               $("#calculationType").focus();
               return false;
           }
           $('.loading-icon').removeClass('hidden');
           <g:remoteFunction controller="deliveryCharges" action="saveDeliveryCalculationTypeForVendor" params="'vendorId=${vendorId}&calculationType='+calculationType+'&threshold='+threshold+'&flatFee='+flatFee" onSuccess = "calculationTypeSaved(data);"/>
       }
    }

    function calculationTypeSaved(){
        $('.loading-icon').addClass('hidden');
        var calculationType = document.getElementById("calculationType").value;
        if("Location"==calculationType){
            $("#addWeight").hide();
            $("#addLocation").show();
            getDeliveryLocations();
        }else{
            $("#addWeight").show();
            $("#addLocation").hide();
            getDeliveryWeights();
        }

    }


    function getFees(event){
        event.preventDefault(); // Prevents the default form submission
         document.getElementById("feesMessage").innerHTML="";
        if(genericValidateForm("feesTester")) {
            $('.loading-icon').removeClass('hidden');
            var totalWeight = document.getElementById("totalWeight").value;
            var locationName = document.getElementById("testerLocationName").value;
            var bookPrice = document.getElementById("bookPrice").value;
            <g:remoteFunction controller="deliveryCharges" action="calculateDeliveryCosts" params="'vendorId=${vendorId}&totalWeight='+totalWeight+'&locationName='+locationName+'&bookPrice='+bookPrice" onSuccess = "feesReceived(data);"/>

        }
    }

    function feesReceived(data){
        $('.loading-icon').addClass('hidden');
        document.getElementById("feesMessage").innerHTML="Calculated fees is <b>"+data.fees+"</b>.<br>"+data.information;
    }

</script>

</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <asset:stylesheet href="landingpage/bootstrap.min.css" async="true"/>
</head>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
.form-group a {
    color: white;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>

<body>

<div class="container-fluid" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h2 class="text-center">Question Paper</h2>
                    <div id="printableArea">
                    <!-- Editable Header -->
                        <input type="hidden" name="id" value="${questionPaperSet.id}">
                        <div class="mb-3">
                            ${questionPaperSet.header}
                        </div>

                        <hr>

                    <!-- Sections and Questions -->
                        <g:each in="${questionPaperSet.sections}" var="section">
                            <div>
                                <div class="mb-3">
                                    <b>${section.description}</b>
                                </div>
                                <ul>
                                    <g:each in="${section.questions}" var="question" status="i">
                                        <li>${i + 1}.<%= questions.get(question.objId) %></li>
                                    </g:each>
                                </ul>
                            </div>
                            <hr>
                        </g:each>
                    </div>

                        <div class="text-center">
                            <a href="javascript:printPage()" class="btn btn-primary">Export to PDF / Print</a>
                        </div>

                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
<script>
    function printPage() {
        var printContents = document.getElementById("printableArea").innerHTML;
        var originalContents = document.body.innerHTML;

        document.body.innerHTML = printContents;

        window.print();

        document.body.innerHTML = originalContents;
    }
</script>

</body>
</html>

<g:render template="/arihant/navheader"></g:render>
<style>
    .modal-backdrop{
            display:none !important;
    }
    .arihant .modal{
        position: unset;
        display: block !important;
        background: transparent !important;
    }
    .fade{
        opacity: 1;
    }
    .arihant #loginSignup .modal-body .signup-bg{
        display: none;
    }
    .arihant #loginSignup h1{
        display: none;
    }
    body{
        overflow: hidden;
    }
    #loginSignup .modal-content{
        box-shadow:unset;
        border: none;
    }
    .arihant .modal-dialog{
        -webkit-transform: translate(0, 0) !important;
        transform: translate(0, 0) !important;
    }
    .arihant .login{
        display: block !important;
    }
    .arihant #loginSignup .modal-header{
        justify-content: center;
    }
    .arihant #closeSignupModal{
        display: none;
    }
    .arihant #loginSignup p{
        display: none;
    }
    .arihant label{
        margin-bottom: 0;
    }
    .arihant #loginSignup .continue {
        background: linear-gradient(90deg, #FF3448 0%, #FF9B26 100%);
        background-color: linear-gradient(90deg, #FF3448 0%, #FF9B26 100%);
    }
</style>
<section style="min-height:70vh" class="arihant">
    <g:render template="/funlearn/mobileSignup"></g:render>
</section>
<g:render template="/arihant/footer"></g:render>

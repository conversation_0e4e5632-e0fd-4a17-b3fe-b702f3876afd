<%
  String requestURL = request.getRequestURL().toString();
  String servletPath = request.getServletPath();
  String appURL = requestURL.substring(0, requestURL.indexOf(servletPath));
  session.setAttribute("servername", appURL);
%>

<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader"></g:render>
<div class="login-signup-container">
  <div class="login-signup-form">
    <p class="login-signup-headline">Login</p>
    <p class="easy-login">Easily Using</p>
    <div class="social-btns">
      <oauth2:connect provider="google" id="google-connect-link">
        <button class="google btns">Google</button>
      </oauth2:connect>
      <oauth2:connect provider="facebook" id="facebook-connect-link">
        <button class="facebook btns">Facebook</button>
      </oauth2:connect>
    </div>
    <p class="using-email">OR USING EMAIL</p>
    <div class="form-container">
      <form class="form-horizontal" method="post" action="/login/authenticate">
        <div class="form-inputs">
          <input type="email" name="username" id="email" class="form-control login-signup-input" placeholder="Your Email Address">
          <input type="password" name="password" id="password" class="form-control login-signup-input" placeholder="Enter Password">
          <a href="#" class="show-password" id="show-password">Show passwors</a>
        </div>
        <div class="submit-btn">
          <input type="submit" id="sign-in" class="btn btn-block login-signup-btn" value="Login">
        </div>
        
      </form>
      <div class="row">
        <div class="col-md-4 forgot-password">
        <p> <a href="/funlearn/forgotpassword">Forgot Password?</a></p>
      </div>
      <div class="already-user col-md-8">
        <p class="have-an-account pull-right">New to Wonderslate? <a href="/funlearn/signUp">Create Account</a></p>
      </div>
      </div>
      <div id="loginFailed" style="display:none; color: #F05A2A; margin-top: 15px;">Login failed. Please try again!</div>
    </div>
  </div>
</div>

<g:render template="/${session['entryController']}/footer"></g:render>
<script>
showHidePassword();
$("#sign-in").click(function() {
  sessionStorage.reloadAfterPageLoad = true;
  window.location.reload();
});

$(function() {
if(sessionStorage.reloadAfterPageLoad) {
  $('#loginFailed').show();
  sessionStorage.reloadAfterPageLoad = false;
}
sessionStorage.clear(); 
});
$('#email').focus();
</script>
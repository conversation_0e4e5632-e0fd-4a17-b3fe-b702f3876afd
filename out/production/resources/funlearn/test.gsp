<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Practice</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <asset:stylesheet href="katex.min.css"/>
    <asset:stylesheet href="landingpage/bootstrap.min.css"/>
    <asset:stylesheet href="landingpage/iconfont/icofont.css"/>
    <asset:stylesheet href="landingpage/webquiz.css"/>
    %{--<asset:stylesheet href="webquiz.css"/>--}%
    <asset:stylesheet href="font-awesome.min.css"/>
    <asset:stylesheet href="chapters-modal.css"/>
    <asset:javascript src="katex.min.js"/>
    <asset:javascript src="auto-render.min.js"/>
    <asset:javascript src="landingpage/jquery-3.2.1.min.js"/>
    <asset:javascript src="landingpage/bootstrap.min.js"/>

    <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
    <link href="https://fonts.googleapis.com/css?family=Montserrat:300,400,500,600,700" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Work+Sans:300,400,500,600,700" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Cardo:400,700" rel="stylesheet">
</head>

<body oncontextmenu="return false;">

<div class="sub-header">
    <div class="overlay-container"> </div>
    <button class="close-menu" onclick="javascript:closesideNav() ">
        <span></span>
    </button>
    <div class="submit">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <div class="tim-wrapper">
                    <div class="d-flex align-items-center">
                        <svg id="time-progress" width="28" viewBox="0 0 220 220" xmlns="http://www.w3.org/2000/svg">
                            <g  transform="translate(110,110)">
                                <circle r="100" class="e-c-progress"/>
                                <g id="e-pointer">

                                </g>
                            </g>
                            <g  transform="translate(110,110)">
                                <circle r="100" class="e-c-base"/>
                            </g>

                        </svg>
                        <button class="play" id="pause"></button>
                        <div>
                            <div  id="sectionSelectionDiv" style="display: none">
                                <select id="sectionSelection" onchange="sectionChanged()">
                                    <option value="1">General Aptitude</option>
                                    <option value="2">Section2</option>
                                    <option value="3">Section3</option>
                                </select>
                            </div>
                            <span class="timer display-remain-time">00.00</span>
                        </div>
                    </div>
                </div>
                <div class="read-backarrow"><i class="material-icons">arrow_back</i></div>
                <div class="submitWrapper"><button type="Submit" class="btn-submit" onclick="javascript:submitTest()">Submit</button> </div>
            </div>
        </div>
    </div>
    <div class="options container">

        <p><span class="answered">Questions</span>:<span class="totalquestion" id="totalQuestions"></span></p>
        <div class="menu-wrapper">
            <a class="language" onclick="javascript:changeLanguage();" id="changeLanguage" style="display: none"></a>
            <button class="menu" onclick="javascript:opensideNav() "></button>
        </div>

        <div class="que-side-menu">


            <div class="container">
                <div class="d-flex justify-content-between indicator">
                    <div class="answered">
                        <p><span class="circle"></span>Answered</p>
                    </div>
                    <div class="unanswered">
                        <p><span class="circle"></span>Unanswered</p>
                    </div>
                </div>
                <div class="d-flex justify-content-between indicator">
                    <div class="review">
                        <p><span class="circle"></span>Marked for review</p>
                    </div>
                    %{--<div class="notseen">--}%
                        %{--<p><span class="circle"></span>Not yet seen</p>--}%
                    %{--</div>--}%
                </div>
            </div>

            <div class="tab">
                <ul class="nav nav-tabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" data-toggle="tab" href="#grid">GRID</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" href="#list">LIST</a>
                    </li>
                </ul>
                <div class="tab-content">

                    <div id="grid" class="container tab-pane active grid"><br></div>

                    <div id="list" class="container list tab-pane fade"><br></div>

                    </div>
                </div>
            </div>
        </div>

    </div>


<div class="result-menu">
    <div class="container d-flex justify-content-between align-items-center">
        <i class="material-icons" onclick="javascript:backPressed();">
            arrow_back
        </i>
        <h2>Result-Excercise</h2>
        <p><button class="language" onclick="javascript:changeLanguage();" id="changeLanguage1" style="display: none"></button></p>
    </div>
</div>
<div class="mt-fixed">
    <form name="quiz" class="col-xs-12" method="post">
        <div id="question-block"></div>
    </form>

    <div id="answer-block" style="display: none"></div>
</div>
<div class="modal fade" id="continue-test" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header">

            </div>

            <!-- Modal body -->
            <div class="modal-body" id="temp-mod">
              <div class="content-wrapper">
                  <p class="timeup">Time's Up!</p>
                  <h4 id="completed-subject">The time to complete this section has ended.</h4>
                  <p class="comingup">COMING UP - <span id="comingup-subject"></span></p>
              </div>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer">
            <button onclick="javascript: nextSection();" data-dismiss="modal" class="btn btn-continue">Continue</button>
            </div>

        </div>
    </div>
</div>
<div class="modal fade" id="submit-test" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header">
                 <h1 class="mt-2">Submit test ?</h1>
            </div>

            <!-- Modal body -->
            <div class="modal-body text-center" id="submitTest">
                <p class="summary">Summary</p>
               <span>(Total Questions:100)</span>
               <div class="d-flex justify-content-center">
                   <div>
                        <div class="d-flex align-items-center mt-4">
                            <div class="circle answered" id="answered"><span>38</span></div>
                            <p>Answered</p>
                        </div>
                        <div class="d-flex align-items-center mt-3">
                            <div class="circle unanswered" id="unanswered"><span>38</span></div>
                            <p>Unanswered</p>
                        </div>
                        <div class="d-flex align-items-center mt-3">
                            <div class="circle review" id="review"><span>38</span></div>
                            <p>Marked for Review</p>
                        </div>
                   </div>
               </div>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer">

                <button onclick="javascript:submitForm();" data-dismiss="modal" class="btn submit">SUBMIT</button>
            <button type="button" class="btn btn-secondary Resume" data-dismiss="modal">Resume</button>
            </div>

        </div>
    </div>
</div>
<div class="modal fade" id="force-submit-test" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header">

            </div>

            <!-- Modal body -->
            <div class="modal-body" id="submitTest">
                <h1 class="submit">Submit ?</h1>
                <p>Your test will be submitted now. </p>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer">
                <button onclick="javascript:submitForm();" data-dismiss="modal" class="btn submit">SUBMIT</button>

            </div>

        </div>
    </div>
</div>
<div class='modal fade' id='report-que'>
<div class='modal-dialog modal-dialog-centered modal-sm'>
<div class='modal-content'>
<div class='modal-header'>
<h4 class='modal-title'>Report the question <i class='material-icons'>error</i> </h4>
<button type='button' class='close' data-dismiss='modal'>&times;</button>
</div>
<div class='modal-body'>
<label class='containers'>Spelling Mistake
<input type='checkbox' checked='checked' id="spellingMistake">
<span class='checkmark'></span>
</label>
<label class='containers'>Direction not given
<input type='checkbox' id="directionNotGiven">
<span class='checkmark'></span>
</label>
<label class='containers'>Graph / Image not visible
<input type='checkbox' id="imageNotVisible">
<span class='checkmark'></span>
</label>
<label class='containers' >Incomplete question
<input type='checkbox' id="incompleQuestion">
<span class='checkmark'></span>
</label>
<label class='containers'>Other Issues
<input type='checkbox' id="otherIssues">
<span class='checkmark'></span>
</label>
<div class='letusknow'>
<textarea placeholder='Let us know' id="moreInformation"></textarea>
</div>
</div>
<div class='modal-footer'>
<button type='button' class='btn btn-submit' onclick="openIssueSubmit()">Submit</button>
</div>
</div>
</div>
</div>
<script>
    function continueTest() {
     $('#continue-test').modal('show');
    }
    function submitTest() {
        $('#submit-test').modal('show');
    }
    function forceSubmitTest() {
        $('#force-submit-test').modal('show');
    }
</script>
<script>

    function backPressed(){
        console.log('backpressed');
    }
</script>
<asset:javascript src="webmcq.js"/>
<asset:javascript src="timer.js"/>

<script src="https://cdnjs.cloudflare.com/ajax/libs/Base64/1.0.1/base64.js" type="text/javascript"></script>
<script>

    function getQuestions(){
        <g:remoteFunction controller="funlearn" action="quizQuestionAnswers" params="'quizId='+1863" onSuccess = "initializeQuizData(data);"/>
    }

    function initializeQuizData(data){
        createMCQ(data.results,false,"",chapterDetails.chapterDetails,"",data.examMst,data.examDtl,data.testSeries,false);
        //   createMCQ(data.results,false,"",data.chaptersList,"",data.examMst,data.examDtl,data.testSeries,false,"web","Practice");
        renderMathInElement(document.body);
    }

    getQuestions();

    // scoreAndShowAnswersMCQ(true,data1.results,true,"What kind of super passage is this man");
    /**   $('#answer-block').on('click', '.show-explanation-btn', function(e) {
        e.preventDefault();
        $(this).parents('.show-explanation').next('.correct-answer-explanation').slideToggle(100);
        $(this).html($(this).html() == 'Show Explanation' ? 'Hide Explanation' : 'Show Explanation');
    });*/




</script>

<script>



    window.onload = function() {
        document.addEventListener("contextmenu", function(e){
            e.preventDefault();
        }, false);
        document.addEventListener("keydown", function(e) {
            //document.onkeydown = function(e) {
            // "I" key
            if (e.ctrlKey && e.shiftKey && e.keyCode == 73) {
                disabledEvent(e);
            }
            // "J" key
            if (e.ctrlKey && e.shiftKey && e.keyCode == 74) {
                disabledEvent(e);
            }
            // "S" key + macOS
            if (e.keyCode == 83 && (navigator.platform.match("Mac") ? e.metaKey : e.ctrlKey)) {
                disabledEvent(e);
            }
            // "U" key
            if (e.ctrlKey && e.keyCode == 85) {
                disabledEvent(e);
            }
            // "F12" key
            if (event.keyCode == 123) {
                disabledEvent(e);
            }
        }, false);
        function disabledEvent(e){
            if (e.stopPropagation){
                e.stopPropagation();
            } else if (window.event){
                window.event.cancelBubble = true;
            }
            e.preventDefault();
            return false;
        }
    };
</script>
</body>
</html>
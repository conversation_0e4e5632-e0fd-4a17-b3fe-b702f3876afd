<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<asset:stylesheet href="jquery.simple-dtpicker.css"/>
<script>
    var loggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);">
    <div class='row'>
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-inline p-4">
                    <div class="flex_st1 col-12 p-0">
                        <h5>DELETE USER</h5>
                        <div class="form-group">
                            <input type="texts" class="form-control col-5" name="deleteuser" id="deleteuser"  placeholder="Enter user mobile/email" />
                            <button class="btn btn-lg btn-primary col-2 ml-3"  onclick="deleteUsers();" style="margin:10px auto;">Delete User</button>
                        </div>
                        <div id="batchUsers"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<g:render template="/${session['entryController']}/footer_new"></g:render>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="jquery.simple-dtpicker.js"/>

<script>

    function deleteUsers(){
        var user = document.getElementById("deleteuser").value;
        alert("Are you sure you want to delete this user?");
        <g:remoteFunction controller="admin" action="deleteuserdetails"  params="'user='+user"    onSuccess = "deleteUser(data)"/>

    }

    function deleteUser(data){
        if(data.status=="OK"){
            document.getElementById("batchUsers").innerHTML= "User Deleted Successfully !";
            document.getElementById("batchUsers").style.color = "forestgreen";
        }else
        {
            document.getElementById("batchUsers").innerHTML = "No user found for this mobile/email.";
            document.getElementById("batchUsers").style.color = "red";
        }
    }

</script>


</body>
</html>
<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<script>
    var loggedIn=false;
</script>

<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<style>

    @media only screen and (max-width: 767px) {
        .download-content{
            flex-direction: column !important;
            justify-content: flex-start !important;
        }
    }
</style>

<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 20px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="">
                    <h4 class="text-center my-4">Download Price List</h4>
                    <% if(session["userdetails"].publisherId==null) {%>
                    <div class="d-flex justify-content-evenly align-items-center download-content flex-column" style="gap: 14px">
                        <div class="d-flex align-items-center justify-content-center flex-column w-100">
                            <g:select id="publisherId" class="form-control" optionKey="id" optionValue="name"
                                      name="publisherId" from="${publishers}" noSelection="${['':'All publisher']}"/>
                        </div>
                    <%  } %>
                    <div class="d-flex align-items-center justify-content-center flex-column w-100">
                        <g:select id="bookType" class="form-control" optionKey="bookType" optionValue="bookType"
                                  name="bookType" from="${bookTypes}" noSelection="${['':'Select Book Type']}"/>
                    </div>
                        <div class="w-100">
                            <div class="text-center">
                                <button type="button" id="download-btn" class="btn btn-primary btn-lg col-4" style="border-width: 0px;">Download</button>
                            </div>
                        </div>
                    </div>

                    <hr class="bg-dark">
                    <h4 class="text-center my-4">Upload Price List</h4>
                    <input type="hidden" name="instituteids1" id="instituteids1">
                    <div style="margin-top: 10px;">
                        <div id="errormsg" class="alert alert-danger has-error" role="alert" style="display: none; background: none;"></div>
                        <div id="successmsg" style="display: none"></div>
                        <div id="batchUsers" style="display: none"></div>
                        <div style="margin-right: 0;" id="download" class="d-flex col-md-8 mx-auto justify-content-center">

                            <g:uploadForm name="resource3Form" url="[action:'priceList',controller:'admin']"  method="post" >
                                <div class="d-flex justify-content-center align-items-center flex-column" style="gap: 10px">
                                    <div class="form-group mb-0 w-100">
                                        <input type="hidden" name="mode" value="submit">
                                        <input id="file3" type="file" class="form-control w-100" name="excelFile" accept=".xlsx" style="height: 38px"/>
                                    </div>
                                    <div class="d-flex align-items-center justify-content-center flex-column w-100">
                                        <g:select id="bookTypeUpdate" class="form-control w-100" optionKey="bookType" optionValue="bookType"
                                                  name="bookTypeUpdate" from="${bookTypes}" noSelection="${['':'Select Book Type']}" style="height: 38px"/>
                                    </div>
                                    <div class="form-group text-center w-100">
                                        <button type="button" onclick="javascript:uploadNotes()" class="btn btn-primary btn-lg w-100">Upload</button>
                                    </div>
                                </div>

                            </g:uploadForm>
                        </div>
                    </div>
                </div>
            </div>
            <%if(updateBooks!=null && updateBooks.size()>0){%>
            <table class="table table-hover table-bordered mt-4">
                <tr class="bg-primary text-white">
                    <th class="text-center">Book ID </th>
                    <th class="text-center">Title</th>
                   <th class="text-center">ISBN</th>
                </tr>

                    <g:each in="${updateBooks}" var="institute" status="i">
                        <tr>
                        <td class="text-center">${institute.id}</td>
                        <td>${institute.title}</td>
                            <td class="text-center" id="isbn">${institute.isbn}</td>
                        </tr>
                    </g:each>

            </table>
            <%}else if(updateBooks==[]){%>
          <div class="text-center text-danger">Price not updated for any book.</div>
            <%}%>
        </div>
    </div>

</div>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<!--</div>-->
<script>
    var select;
    var publisherId;
    var initialState =  $('#publisherId option:selected').val();
    publisherId = initialState;
    <% if(session["userdetails"].publisherId==null) {%>
    publisherId = initialState;
    <%  } else{%>
    var noPubId = "${session["userdetails"].publisherId}";
    publisherId = noPubId;
    <%}%>
     //Selecting the publisher
    $('#publisherId').on('change',function (){
        select =  $('#publisherId option:selected').val();
        publisherId = select;

    });


    $('#download-btn').on('click', function() {
            if(document.getElementById("bookType").selectedIndex>0) {
                var bookType = document.getElementById("bookType")[document.getElementById("bookType").selectedIndex].value;
                window.location.href = "/admin/downloadPricelistData?publisherId=" + publisherId + "&bookType=" + bookType;
            }else{
                alert("Select book type");
                document.getElementById("bookType").focus();
            }

    });

    function uploadNotes(){
        if(document.getElementById("bookTypeUpdate").selectedIndex>0) {
            document.resource3Form.submit();
        }else{
            alert("Select book type");
            document.getElementById("bookTypeUpdate").focus();
        }
    }

    <%if(updateBooks!=null && updateBooks.size()>0){%>
        <g:each in="${updateBooks}" var="institute" status="i">
          var isbn =  ${institute.isbn}
              if(isbn == 0 || isbn=='0'){
                  isbn = '';
                  $('#isbn').text(isbn)
              }
    </g:each>
    <%}else if(updateBooks==[]){%>
    console.log(isbn);
    <%}%>

</script>

</body>
</html>

<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:stylesheet href="wonderslate/flashcardHome.css" async="true"/>
<script>
    var loggedIn = false;
</script>

<sec:ifLoggedIn>
    <script>
        loggedIn = true;
    </script>
</sec:ifLoggedIn>

<asset:stylesheet href="discussionboard/discussionboard.css"/>
<asset:stylesheet href="wonderslate/doubts.css" async="true"/>
<script src="/assets/katex.min.js"></script>
<asset:stylesheet href="katex.min.css"/>
<script src="/assets/auto-render.min.js"></script>
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link href="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.css" rel="stylesheet">
<script src="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.js"></script>

<script type="text/javascript" async
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
</script>
<script>
    var cuRes=""
    var resData = []
</script>
<script type="text/x-mathjax-config">
MathJax.Hub.Config({tex2jax: {inlineMath: [['$','$'], ['\\(','\\)']]}});
</script>


<script src="https://cdn.ckeditor.com/4.7.1/full-all/ckeditor.js"></script>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<section style="min-height: 100vh" class="admin-wrapper">
    <div class="container-fluid" style="margin: 0px">
        <div class="row">
            <div class="col-12 col-md-2">
                <ul class="nav flex-column  nav-pills discussion-menu" role="tablist">
                    <li onclick="loadAwaitingApprovalData('KeyValues')" class="nav-item">
                        <a class="nav-link active" data-toggle="pill" href="#KeyValues">Flashcards</a>
                    </li>
                    <li onclick="loadAwaitingApprovalData('Notes')" class="nav-item">
                        <a class="nav-link" data-toggle="pill" href="#notes">Notes</a>
                    </li>
                    <li onclick="loadAwaitingApprovalData('Multiple Choice Questions')" class="nav-item">
                        <a class="nav-link" data-toggle="pill" href="#mcq">MCQ</a>
                    </li>
                    <li onclick="loadAwaitingApprovalData('Reference Videos')" class="nav-item">
                        <a class="nav-link" data-toggle="pill" href="#mcq">Videos</a>
                    </li>
                    <li onclick="loadAwaitingApprovalData('Reference Web Links')" class="nav-item">
                        <a class="nav-link" data-toggle="pill" href="#weblink">Web Link</a>
                    </li>
                </ul>
            </div>
            <div class="col-12 col-md-7">
                <div class="line"><img src="${assetPath(src: 'discussionboard/line.svg')}" alt=""></div>
                <div class="container mt-2" id="flashCardSets">

                </div>
                <div id="pagination-div" ></div>
                <div class="container mt-2" id="NoflashCardSets">

                </div>
            </div>
            <div class="col-12 col-md-2 mt-4">
                <select onchange="filterchanged(0)" id="admin-filter" class="form-control d-none">
                    <option value="UM" selected>Unmoderated</option>
                </select>

                <div class="moderate-btns">
                    <div id="multiple-action-icon"></div>
                </div>
            </div>
        </div>
    </div>

</section>

<g:render template="/books/footer_new"></g:render>

<asset:javascript src="landingpage/bootstrap-3-typeahead.js"/>
<asset:javascript src="sharer.min.js"/>
<asset:javascript src="multiselect.js"/>
<script type="text/javascript" async
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
</script>

<script>
    var siteId = "${session['siteId']}";
    var fileObj = {};
    var multiSelectedIds = [];
    var flashCardsList = [];
    var answerList = [];
    var recordsPerPage = 20;
    var recordsPerBatch = 100;
    var count = 0;
    var batchIndex = 0;

    function displayFlashCardSets(flashCardSets, loopInitialValue,loopLimiter){
        var htmlStr="<div class='row p-2'> " +
            "<div class='col-12 col-lg-10'> " +
            "<div class='col-12'>"+
            "<div class='pl-2 pt-3 pb-0 ml-4 mr-4 pr-2'>" ;
        var setNames=flashCardSets[0].resType;
        if(setNames=='KeyValues'){
            setNames='Flashcard sets'
        }
        htmlStr +="  <p class='info-text reset-color'>"+setNames+"</p>";
        htmlStr +=  "</div>";
        htmlStr +="<div class=\"d-flex flex-wrap flex-column flashcards\">\n";
        for(var i=loopInitialValue;i<loopLimiter;i++){
            htmlStr += "<div class='d-flex align-items-center'><input value='"+flashCardSets[i].id  +"' onchange='multiSelectChkChanged(this)' type=\"checkbox\" class=\"mr-3\">";
           if(flashCardSets[i].resType=='KeyValues') {
               htmlStr += " <a href='/resources/displayFlashCards?resId=" + flashCardSets[i].id + "&name=" + encodeURIComponent(flashCardSets[i].resourceName).replace(/'/g,"&#39;") + "'>" ;
           }
           else if(flashCardSets[i].resType=='Notes') {
                htmlStr += " <a target='_blank' href='/resources/notesViewer?resId=" + flashCardSets[i].id + "&name=" + encodeURIComponent(flashCardSets[i].resourceName).replace(/'/g,"&#39;") + "'>"  ;
            }
           else if(flashCardSets[i].resType=='Multiple Choice Questions') {
               htmlStr += " <a target='_blank' href='/funlearn/quiz?fromMode=book&quizMode=practice&fromTab=all&viewedFrom=quiz&resId=" + flashCardSets[i].id + "&name=" + encodeURIComponent(flashCardSets[i].resourceName).replace(/'/g,"&#39;") + "'>";
           }
           else if(flashCardSets[i].resType=='Reference Videos') {
               htmlStr += " <a target='_blank' href='/videos?resId=" + flashCardSets[i].id + "&name=" + flashCardSets[i].resLink + "&share=true'>" ;
           }
            htmlStr +=   "<div class=\"col-10 mt-3\" id=\"flash" + flashCardSets[i].id + "\">\n";
            htmlStr +=  "                <div class=\"flashcard-set\">\n" +
                "                    <div class=\"title-wrapper d-flex align-items-center justify-content-between\">\n" +
                "                        <div class='d-flex'><h4>"+flashCardSets[i].resourceName +"</h4>\n" ;

            htmlStr += "</div>\n" +
                "                        <div class=\"flashcard-actions\">\n";



            htmlStr += "                        </div>\n" +
                "                    </div>\n" ;
            if(flashCardSets[i].resType=='KeyValues') {
                htmlStr += "<a href='/resources/displayFlashCards?resId=" + flashCardSets[i].id + "&name=" + encodeURIComponent(flashCardSets[i].resourceName).replace(/'/g,"&#39;") + "' class='d-block h-100' onclick='javascript:callAppLoader()'>";
            }
            else if(flashCardSets[i].resType=='Notes') {
                htmlStr += "<a target='_blank' href='/resources/notesViewer?resId=" + flashCardSets[i].id + "&name=" + encodeURIComponent(flashCardSets[i].resourceName).replace(/'/g,"&#39;") + "' class='d-block h-100' onclick='javascript:callAppLoader()'>";
            }
            else if(flashCardSets[i].resType=='Multiple Choice Questions') {
                htmlStr += "<a target='_blank' href='/funlearn/quiz?fromMode=book&quizMode=practice&fromTab=all&viewedFrom=quiz&resId=" + flashCardSets[i].id + "&name=" + encodeURIComponent(flashCardSets[i].resourceName).replace(/'/g,"&#39;") + "' class='d-block h-100' onclick='javascript:callAppLoader()'>";
            }
            else if(flashCardSets[i].resType=='Reference Videos') {
                htmlStr += "<a target='_blank' href='/videos?resId=" + flashCardSets[i].id + "&name=" + flashCardSets[i].resLink + "&share=true' class='d-block h-100' onclick='javascript:callAppLoader()'>";
            }
            else if(flashCardSets[i].resType=='Reference Web Links') {
                htmlStr += "<a href='javascript:openWebRef(" + flashCardSets[i].id + ",\""+flashCardSets[i].resLink +"\")' class='d-block h-100'>";
            }
            htmlStr += "<div>";
            if(flashCardSets[i].createdBy !='') {
                htmlStr +=   "                        <p class='m-0'>By: " + flashCardSets[i].createdBy.split('_').pop() + "</p>" ;
            }
            if(flashCardSets[i].description){
                htmlStr +=  "<p>"+flashCardSets[i].description+"</p>";
            }
            else {
                htmlStr += "                    <p></p>\n";
            }
            htmlStr += "<div class=\"progress d-none\">\n" +
                "    <div class=\"progress-bar green\" role=\"progressbar\" aria-valuenow=\"70\" aria-valuemin=\"0\" aria-valuemax=\"100\" style=\"width:20%\">\n" +

                "    </div>\n" +
                "  </div>"+
                "                </div></div>\n" +
                "</a>"+
                "            </div></div>\n" ;
        }
        htmlStr += "        </div>";


            document.getElementById("flashCardSets").innerHTML = htmlStr;


    }
    function loadAwaitingApprovalData(resType){
        if(resType){
            cuRes = resType;
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="admin" action="getAwaitingApprovalData" params="'resType='+resType" onSuccess="showAwaitingApprovalData(data)"/>
        }
    }
    function moderateSelectedResource(resIds=""){
        if(resIds.toString().split('-').length>0){
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="admin" action="moderateResource" params="'resIdList='+resIds" onSuccess="moderationResponse(data)"/>
        }else{
            alert("Please select some resource.")
        }
    }
    function moderationResponse(data){
        if(data.status=='ok'){
            alert("Resource Moderated.")
            location.reload()
        }
    }
    function deleteSelectedResource(resIds=""){
        if(resIds.toString().split('-').length>0){
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="admin" action="rejectResourceModeration" params="'resIdList='+resIds" onSuccess="deletionResponse(data)"/>
        }else{
            alert("Please select some resource.")
        }
    }
    function deletionResponse(data){
        if(data.status=='ok'){
            alert("Resource Deleted.")
            location.reload()
        }
    }
    function showAwaitingApprovalData(data){
        $('.loading-icon').addClass('hidden');
        if("OK"==data.status.toUpperCase()) {
            resData = data.awaitingApprovalList;
            if(cuRes="KeyValues"){
                displayFlashCardsList(resData);
            }
            $('#pagination-div').show();

        }else if(data.awaitingApprovalList.length==0 || data.awaitingApprovalList.length==''){
            document.getElementById('flashCardSets').innerHTML = "<p class='text-center'>No Resources to moderate</p>";
            $('#pagination-div').hide();
        }
        $('.loading-icon').addClass('hidden');
    }

    function displayFlashCardsList(data) {
        $('.loading-icon').addClass('hidden');
        var flashCards = data;
        count = data.length;
        if(batchIndex == 0){
            flashCardsList = [];
        }
        var value = ((batchIndex * recordsPerBatch) / recordsPerPage ) + 1;
        if(batchIndex > 0 && value == Math.ceil(count/recordsPerPage) && flashCardsList.length <  count){
            for(var i=flashCardsList.length; i< count - flashCards.length;i++){
                flashCardsList.push(undefined);
            }
        }
        for(i=0;i<flashCards.length;i++){
            flashCardsList[((value*recordsPerPage)-recordsPerPage)+i] = flashCards[i]
        }
        showPagination('flashCards', value);
    }

    loadAwaitingApprovalData('KeyValues')
</script>
<script>
    var multiSelectedIds = [];

    function multiSelectChkChanged(field){

        if($(field).is(":checked") == true){
            multiSelectedIds.push($(field).val());
            if(multiSelectedIds.length == 1) {
                $('#multiple-action-icon').replaceWith("<div id=\"multiple-action-icon\" > <button onclick=\"moderateSelectedResource(multiSelectedIds.join('-'))\" ><i class=\"material-icons\">visibility</i>Moderation Done</button> " +
                    "\n" +
                    "<button class=\"delete\" onclick=\"deleteSelectedResource(multiSelectedIds.join('-'))\" ><i class=\"material-icons\">delete</i>Delete</button> </div>")
            }
        }else if($(field).is(':checked') == false){
            if(multiSelectedIds.indexOf($(field).val()) > -1){
                multiSelectedIds.splice(multiSelectedIds.indexOf($(field).val()),1);
            }
            if(multiSelectedIds.length <= 0) $('#multiple-action-icon').replaceWith("<div id=\"multiple-action-icon\"></div>")
        }
    }


    function showPagination(screen,value){
        var  data = flashCardsList;
        var htmlStr = "";
        if(value < 1 ) value = 1;
        var loopInitialValue = 0;
        var loopLimiter = 0;
        if(count > 0 && value <= Math.ceil(count/recordsPerPage)){
            htmlStr = htmlStr + "<ul class=\"pagination justify-content-center mt-4\">\n" +
                "                        <li onclick='showPagination(\""+screen+"\",-1)' class=\"page-item\"><a class=\"page-link actions\" ><span class=\"material-icons\">\n" +
                "                            first_page\n" +
                "                        </span>Skip to First </a></li>\n" +
                "                        <li onclick='showPagination(\""+screen+"\","+(value - 1)+")' class=\"page-item\"><a class=\"page-link actions\" ><span class=\"material-icons\">\n" +
                "                            chevron_left\n" +
                "                        </span>Previous</a></li>\n" +
                "                        <li onclick='showPagination(\""+screen+"\","+(value)+")' class=\"page-item active\"><a class=\"page-link\" >"+value+"</a></li>\n";
            if(count > (recordsPerPage*value)) htmlStr = htmlStr + " <li onclick='showPagination(\""+screen+"\","+(value + 1)+")' class=\"page-item\"><a class=\"page-link\" >"+(value + 1)+"</a></li>\n";
            htmlStr = htmlStr + "                        <li onclick='showPagination(\""+screen+"\","+(value + 1)+")' class=\"page-item\"><a class=\"page-link actions\" ><span class=\"material-icons\">\n" +
                "                            chevron_right\n" +
                "                        </span>Next</a></li>\n" +
                "                        <li onclick='showPagination(\""+screen+"\","+(Math.ceil(count/recordsPerPage))+")' class=\"page-item\"><a class=\"page-link actions\" ><span class=\"material-icons\">\n" +
                "                            last_page\n" +
                "                        </span>Skip to Last</a></li>\n" +
                "                    </ul>";
            $("#pagination-div").html(htmlStr);
        }else if(count == 0 || data.length == 0) $("#pagination-div").html("");
        if(value == 1)  {
            if(data.length > recordsPerPage) loopLimiter = recordsPerPage;
            else loopLimiter = data.length;
        }
        else {
            loopInitialValue = (value - 1) * recordsPerPage;
            loopLimiter = 0;
            if(data.length > loopInitialValue + recordsPerPage) loopLimiter = loopInitialValue + recordsPerPage;
            else loopLimiter = data.length
        }
        if(count ==0 ||  (data[loopInitialValue] != undefined && loopInitialValue < data.length)) {
            if(screen == "flashCards") displayFlashCardSets(data,loopInitialValue,loopLimiter);


        }
        else {
            var previousBatch = batchIndex;
            batchIndex = Math.floor(((value - 1) * recordsPerPage)/recordsPerBatch);
            if( value <= Math.ceil(count/recordsPerPage) && (data.length < count || data[loopInitialValue] == undefined)) filterchanged(batchIndex)
        }

    }
    function openWebRef(id,link){
        initCallGoogle(id,"References");
        if(link.indexOf("http")==-1) link = "http://"+link;
        window.open(link, '_blank');
        if(loggedInUser){

            updateUserView(id,"all","weblinks");

        }
        else{

            updateView(id,"all","weblinks");

        }
    }
    function initCallGoogle(id,loadedType){


        var bookTitle = "${bookName}";
        var seoFriendlyTitle = "${title}";
        <%if("books".equals(grailsApplication.config.grails.appServer.default)&&"1".equals(""+session["siteId"])) {%>
        var currentUrl = window.location.href;
        //add the resId
        if(currentUrl.indexOf("&resId")==-1)
            currentUrl=currentUrl+"&resId="+id;
        else{
            currentUrl = currentUrl.substr(0,currentUrl.indexOf("&resId"))+"&resId="+id;
        }
        history.pushState({
            id: 'homepage'
        }, '', currentUrl);
        if(seoFriendlyTitle.indexOf("Wonderslate")>0) {
            var wonderslateIndex =   seoFriendlyTitle.indexOf(" - Wonderslate");
            seoFriendlyTitle = seoFriendlyTitle.substr(0,wonderslateIndex)+": "+loadedType+" - Wonderslate";
            document.title = seoFriendlyTitle;
        }

        callGoogle(window.location.pathname+"/"+window.location.search);

        <%}
       %>

    }
    function updateView(id,fromTab,viewedFrom){

        <g:remoteFunction controller="log" action="updateView" params="'id='+id+'&source=web&fromTab='+fromTab+'&viewedFrom='+viewedFrom" />
    }
    function updateViewChapter(id,fromTab,viewedFrom,action){
        <g:remoteFunction controller="log" action="updateView" params="'chapterId='+id+'&source=web&fromTab='+fromTab+'&viewedFrom='+viewedFrom+'&actionName='+action" />
    }
</script>


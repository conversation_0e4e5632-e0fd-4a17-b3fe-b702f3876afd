<div class="pdfViewer">
    <iframe id="bookGPTReader" src=""></iframe>
</div>

<script>
    let loggedInUser = false;
    let title = '';
    let mobileView = false;
    const bookGPT=true;
    const encryptedKey = '${encryptPdfKey}'
    const hasBookAccess = '${hasBookAccess}'
    const chaptersList =JSON.parse('${chapters}'.replace(/&#92;/g,'\\').replace(/&quot;/g,'"'))
    const knimbusInstUser = false;

    const groupByChapterId = (arr) => {
        return arr.reduce((acc, curr) => {
            if (!acc[curr.chapterId]) {
                acc[curr.chapterId] = {
                    chapterId: curr.chapterId,
                    chapterName: curr.chapterName,
                    resources: []
                };
            }
            acc[curr.chapterId].resources.push({
                bookId: curr.bookId,
                link: curr.link,
                name: curr.name,
                resId: curr.resId,
                resType: curr.resType
            });
            return acc;
        }, {});
    };


    <sec:ifLoggedIn>
        loggedInUser = true;
    </sec:ifLoggedIn>

    const bookGPTReader = document.getElementById('bookGPTReader')

    async function getPDF(resId,chapterId){
        gptResId = resId
        gptChapterId = chapterId
        showAppLoader(true)
        const resourceURL = "/resources/pdfReader?bookLang=&resId="+gptResId+"&bookId="+gptBookId+"&loggedInUser="+loggedInUser+"&bookUrl=/funlearn/getPdfFile?resId="+gptResId+"__encryptedKey="+encryptedKey+"&chapterId="+gptChapterId+"&title="+title+"&knimbusInstUser="+knimbusInstUser+"&mobileView=false&bookGPT=true#zoom=undefined"
        bookGPTReader.src = resourceURL
    }
    getPDF(gptResId,gptChapterId)
    const groupedData = groupByChapterId(chaptersList);
    updateChapterDropDown(groupedData)

</script>
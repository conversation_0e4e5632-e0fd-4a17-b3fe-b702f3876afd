<%@ page import="java.text.SimpleDateFormat" %>


<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
%{--<asset:javascript src="moment.min.js"/>--}%

<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />

<script>
    var loggedIn=false;
</script>
<style>

.datepicker table {
    border-collapse: unset;
}
.datepicker .datepicker-days td, .datepicker .datepicker-days th {
    width: 25px !important;
    height: 25px !important;
}

.form-group .btn-group {
    width: 100%; }
.form-group .btn-group .multiselect.dropdown-toggle {
    width: 100% !important;
    height: 44px;
    line-height: normal;
    background-color: #FFFFFF;
    text-align: left;
    padding: 12px 0 12px 16px;
    box-shadow: none;
    border: 1px solid #cccccc;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 15px;
    color: #000000;
    font-weight: normal; }
.form-group .btn-group .multiselect-container {
    width: 100%;
    line-height: normal;
    font-size: 15px;
    background-color: #FFFFFF;
    color: rgba(68, 68, 68, 0.64);
    max-height: 250px;
    overflow: hidden;
    overflow-y: auto; }
.form-group .btn-group .multiselect-container li {
    line-height: normal;
    font-size: 15px;
    background-color: #FFFFFF;
    color: rgba(68, 68, 68, 0.64); }
.form-group .btn-group .multiselect-container a {
    line-height: normal;
    font-size: 15px;
    background-color: #FFFFFF;
    color: rgba(68, 68, 68, 0.64); }
.modal-dialog-centered {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    justify-content: center;
}
.modal.show .modal-dialog {
    -webkit-transform: none;
    transform: none;
}
.modal.fade .modal-dialog {
    transition: -webkit-transform .3s ease-out;
    transition: transform .3s ease-out;
    transition: transform .3s ease-out,-webkit-transform .3s ease-out;
    -webkit-transform: translate(0,-50px);
    transform: translate(0,-50px);
}
.modal-content{
    width: 100%;
}
@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
</style>
<style>
.copy-container {
    display: flex;
    align-items: center;
    margin-top: 20px;
}

.affiliation-label {
    font-weight: bold;
    margin-right: 10px;
}

#affiliation-text {
    padding: 5px;
    background-color: #f5f5f5;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-right: 10px;
    user-select: text; /* Allows the text to be selected */
}

.copy-btn {
    cursor: pointer;
    color: #007bff;
    font-size: 20px;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>


<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h3 class="text-center">
                        Affiliation Dashboard</h3>
                    <div class="copy-container">
                        <span class="affiliation-label">Site Link:</span>
                        <div id="affiliation-text">${siteLink}</div>
                        <i class="far fa-copy copy-btn" id="copy-btn" title="Copy to clipboard"></i>
                    </div>
                    <div class="copy-container">
                        <span class="affiliation-label">Store Link:</span>
                        <div id="affiliation-text1">${storeLink}</div>
                        <i class="far fa-copy copy-btn" id="copy-btn1" title="Copy to clipboard"></i>
                    </div><br>
                    <div><h5>Visit Report</h5> </div>
                    <%if(results!=null && results.size()>0){%>
                    <!-- create table and show the results -->
                    <table class="table table-responsive table-striped table-bordered">
                        <tr>
                            <th>Visit Date</th>
                            <th>Visit Count</th>
                        </tr>
                        <%
                            for(int i=0;i<results.size();i++){
                                out.println("<tr>");
                                out.println("<td>"+results.get(i).day+"</td>");
                                out.println("<td>"+results.get(i).record_count+"</td>");
                                out.println("</tr>");
                            }
                        %>
                </table>
                    <%}else{%>
                    <div class="d-flex align-items-center">
                        <div class="form-group">
                            No visits in last 7 days
                        </div>
                </div>
                    <%}%>
                   <br><br>
                    <div><h5>Purchase Report</h5> </div>
                    <div class="d-flex align-items-center">
                        <div class="form-group">
                            <label for="startDate">From Date</label><br>
                            <input type="text" class="w-100 form-control" id="startDate" name="startDate" placeholder="From Date"   autocomplete="off" >
                        </div>

                        <div class="form-group ml-4">
                            <label for="endDate">To Date</label><br>
                            <input type="text" class="w-100 form-control" id="endDate" name="endDate" placeholder="To Date"  autocomplete="off" >
                        </div>

                    </div>

                </div>
                <button class="btn btn-primary"  onclick="submitUsageReport()">Submit</button>
                <div style="margin-top: 10px;">
                    <div id="errormsg" class="alert alert-danger has-error" role="alert" style="display: none; background: none;"></div>
                    <div id="successmsg" style="display: none"></div>
                    <div id="publisherCalculation" style="display: none">
                    </div>
                    <div style="float: right; display: none;" id="download">
                        <div class="form-group">
                            <button type="button" id="download-btn" class="btn btn-primary " style="border-width: 0px;" >Download</button>
                        </div>
                    </div>
                    <div id="batchUsers" style="display: none"></div>
                </div>
            </div>
        </div>
    </div>



</div>
</div>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>


<script>

    const siteId1 = "${session["siteId"]}";
    var eBookSalesWS=0;
    var eBookSalesWhiteLabel=0;
    var rechargeSales=0;
    var printbookSales=0;
    var totalDeliveryCosts=0;
    var publisherOnWS=70;
    var publisherOnWhiteLabel=70;
    var publisherPrintbook=90;


    $('#startDate, #endDate').datepicker({
        format: 'dd-mm-yyyy',
        autoclose: true,
        todayHighlight: true,
        orientation: "bottom auto",
        endDate: '+0d'
    });




    function submitUsageReport() {

        var StartDate = document.getElementById("startDate").value
        var EndDate = document.getElementById("endDate").value
        var inValidStartDate = false;
        if (document.getElementById("startDate").value != "" &&  document.getElementById("endDate").value != ""){
            var  startDate1 = new Date(StartDate.split('-')[2],StartDate.split('-')[1],StartDate.split('-')[0]);
            var  endDate1 = new Date(EndDate.split('-')[2],EndDate.split('-')[1],EndDate.split('-')[0]);
            if(endDate1.getTime() < startDate1.getTime()) inValidStartDate = true;
        }
        if (document.getElementById("startDate").value === "" || document.getElementById("endDate").value === "") {
            document.getElementById("errormsg").innerHTML = "Please Select From Date & To Date."
            $("#errormsg").show();
            $("#batchUsers").hide();
            $('#download').hide();
            return
        }

        if (inValidStartDate){
            document.getElementById("errormsg").innerHTML="Please enter valid From Date. From Date cannot be greater then To date";
            $("#errormsg").show();
            $("#batchUsers").hide();
            $('#download').hide();
            $("#publisherCalculation").hide();


        } else {
            $("#errormsg").hide();
            $('#download').hide();
            $("#publisherCalculation").hide();

             <g:remoteFunction controller="partner" action="salesReportByPartner" params="'startDate='+StartDate+'&mode=submit&endDate='+EndDate" onSuccess = "showReports(data);"/>
        }
    }


    function showReports(data){
        $('.loading-icon').addClass('hidden');
        var htmlStr= "                    <table class='table table-responsive table-striped table-bordered'>\n" +
            "                        <tr>\n" +
             "                            <th>Po Date</th>\n" +
            "                            <th>BookType</th>\n" +
            "                            <th>Amount</th>\n" +
            "                            <th>Title</th>\n" +
            "                        </tr>\n" ;
        if(data.status=="OK"){
            var report = data.results;


            for(var i=0;i<report.length;i++){

                htmlStr +="<tr>"+
                    "<td>"+report[i].date_created+"</td>" +
                     "<td>"+report[i].book_type+"</td>" +
                    "<td>"+report[i].amount+"</td>" +
                    "<td>"+report[i].title+"</td>" +
                     "</tr>";
            }
            htmlStr += "</table>";
            document.getElementById("batchUsers").innerHTML= htmlStr;
            $('#download').show();
        }else{
            document.getElementById("batchUsers").innerHTML= "No Records Found.";
            $('#download').hide();
        }
        $("#batchUsers").show();
    }

    $('#download-btn').on('click', function() {
        var startDate=document.getElementById("startDate").value
        var endDate=document.getElementById("endDate").value
        window.location.href = "/partner/salesReportByPartner?mode=download&startDate="+startDate+"&endDate="+endDate;
    });

    document.getElementById("copy-btn").addEventListener("click", function() {
        // Get the div text
        var copyText = document.getElementById("affiliation-text").innerText;

        // Create a temporary textarea to copy the text
        var tempInput = document.createElement("textarea");
        tempInput.value = copyText;
        document.body.appendChild(tempInput);

        // Select and copy the text
        tempInput.select();
        document.execCommand("copy");

        // Remove the temporary textarea
        document.body.removeChild(tempInput);

        // Optionally: Change the icon to indicate success (e.g., show a check mark)
        alert("Affiliation link copied to clipboard!");
    });
    document.getElementById("copy-btn1").addEventListener("click", function() {
        // Get the div text
        var copyText = document.getElementById("affiliation-text1").innerText;

        // Create a temporary textarea to copy the text
        var tempInput = document.createElement("textarea");
        tempInput.value = copyText;
        document.body.appendChild(tempInput);

        // Select and copy the text
        tempInput.select();
        document.execCommand("copy");

        // Remove the temporary textarea
        document.body.removeChild(tempInput);

        // Optionally: Change the icon to indicate success (e.g., show a check mark)
        alert("Affiliation link copied to clipboard!");
    });
</script>

</body>
</html>

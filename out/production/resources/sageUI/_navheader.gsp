<%@ page import="com.wonderslate.usermanagement.User; com.wonderslate.data.ResourceType" %>
<!DOCTYPE html>
<html>
<head>
<!-- Basic -->
<meta charset="utf-8">
<title>Sage</title>
<meta name="keywords" content="Wonderslate" />
<meta name="description" content="Online store for digital text books">
<link rel="icon"  href="${assetPath(src: 'sage/favicon.png')}">
<!-- Mobile Specific Metas -->
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
<!-- Start SmartBanner configuration -->
<meta name="smartbanner:title" content="Digital Smart Books">
<meta name="smartbanner:author" content="Wonderslate">
<meta name="smartbanner:price" content="FREE">
<meta name="smartbanner:price-suffix-apple" content=" - On the App Store">
<meta name="smartbanner:price-suffix-google" content=" - In Google Play">
<meta name="smartbanner:icon-apple" content="${assetPath(src: 'appstore.png')}">
<meta name="smartbanner:icon-google" content="${assetPath(src: 'playstore.png')}">
<meta name="smartbanner:button" content="OPEN">
<meta name="smartbanner:button-url-apple" content="https://itunes.apple.com/us/app/wonderpublish/id1187800915?mt=8">
<meta name="smartbanner:button-url-google" content="https://play.google.com/store/apps/details?id=wonderslate.com.wonderpublish">
<meta name="smartbanner:enabled-platforms" content="android,ios">
<!-- End SmartBanner configuration -->
<!-- Web Fonts  -->
<link href="http://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700,800%7CShadows+Into+Light" rel="stylesheet" type="text/css"/>
<!-- Icon Fonts  -->
<asset:stylesheet href="welldone/font/style.css"/>
<!-- Vendor CSS -->
<asset:stylesheet href="welldone/vendor/waves/waves.css"/>
<asset:stylesheet href="welldone/vendor/slick/slick.css"/>
<asset:stylesheet href="welldone/vendor/slick/slick-theme.css"/>
<asset:stylesheet href="welldone/vendor/bootstrap-select/bootstrap-select.css"/>
<!-- Custom CSS -->
<asset:stylesheet href="welldone/css/style-colors.css"/>
%{-- <asset:stylesheet href="welldone/css/style-layout1.css"/> --}%
<!-- Custom Fonts -->
<link href='https://fonts.googleapis.com/css?family=Roboto:400,300,300italic,400italic,700,700italic' rel='stylesheet' type='text/css'>
<link href='https://fonts.googleapis.com/css?family=Ubuntu:400,700' rel='stylesheet' type='text/css'>
<link href='https://fonts.googleapis.com/css?family=Exo' rel='stylesheet' type='text/css'>
<link href="https://fonts.googleapis.com/css?family=Merriweather" rel="stylesheet" type='text/css'> 
<link href="https://fonts.googleapis.com/css?family=Montserrat" rel="stylesheet"> 
<link href="https://fonts.googleapis.com/css?family=Exo" rel="stylesheet">

<asset:stylesheet href="mainstyle.css"/>
<asset:stylesheet href="font-awesome.min.css"/>
<asset:stylesheet href="clock.css"/>
<asset:stylesheet href="bootstrap.css"/>

<!-- SLIDER REVOLUTION 4.x CSS SETTINGS -->
<asset:stylesheet type="text/css" href="welldone/vendor/rs-plugin/css/settings.css" media="screen" />
<asset:stylesheet href="common.css"/>
<asset:stylesheet href="sage.css"/>




<body>
<header>
  <nav id="nav" class="navbar navbar-wonderslate">
    <div class="container">
      <div class="navbar-header">
        <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#main-navbar" aria-expanded="false">
          <span class="sr-only">Toggle navigation</span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
        </button>
        <a class="navbar-brand" href="/sageUI/index">
          <img src="${assetPath(src: 'sage/logo.png')}" />
        </a>
      </div>

      <div class="collapse navbar-collapse" id="main-navbar">
        <sec:ifNotLoggedIn>
          <ul class="nav navbar-nav navbar-right header-menus">
            <li class="header-menu-item search-book ">
              <a href="/wonderpublish/mylibrary" class="header-menu-item-link" style="padding: 0; margin-left: 30px; margin-top: 5px; display: inline-block">My Library</a>
              
              <a href="#" id="login-signup-dropdown" class="dropdown-toggle header-menu-item-link dropdown user-profile-dropdown" data-toggle="dropdown" style="padding: 0; display: inline-block; margin-left: 10px; border-left: 2px solid; padding-left: 10px;">
                <span class="icon-User_Icon"></span>
              </a>

              <input type="text" class="form-control book-search-input clerfix" id="search-book" data-provide="typeahead" placeholder="Search...">
              <button id="search-button" class="search-icon"><i class="fa fa-search" aria-hidden="true"></i></button>
              <ul class="dropdown-menu login-signup-dropdown" aria-labelledby="login-signup-dropdown" style="top: 30px;">
                <li>
                  <a class="login-btn" href="/funlearn/signIn">Login</a>
                </li>
                <li>
                  <p>New user? 
                    <a href="/funlearn/signUp">Start here</a>
                  </p>
                </li>
              </ul>
            </li>
            
            %{-- <li class="header-menu-item">
            <a href="#" class="header-menu-item-link">
            <span class="icon-cart"></span>
            </a>
            </li> --}%
          </ul>
        </sec:ifNotLoggedIn>

        <sec:ifNotLoggedIn>
          <ul class="nav navbar-nav navbar-right header-menus" style="padding-right: 0;">
            <li class="header-menu-item">
              <a href="/sageUI/index?mode=browse" class="header-menu-item-link">Browse</a>
            </li>
          </ul>
        </sec:ifNotLoggedIn>

        <sec:ifLoggedIn>
          <ul class="nav navbar-nav header-menus">
            <sec:ifAllGranted roles="ROLE_PUBLISHER">
              <li class="header-menu-item">
                <a href="/wonderpublish/pubSales" class="header-menu-item-link">Sales</a>
              </li>
            </sec:ifAllGranted>
          </ul>

          <ul class="nav navbar-nav navbar-right header-menus">
            <li class="header-menu-item">
              <a href="/sageUI/index?mode=browse" class="header-menu-item-link">Browse</a>
            </li>
            <%if("yes".equals(addlMenu)) {%>
              <li class="header-menu-item">
                <a href="https://edge.sagepub.com/devlin/instructor-access-0?destination=node/59137" target="_blank" class="header-menu-item-link">Instructor Resources</a>
              </li>
              <li class="header-menu-item">
                <a href="/wonderpublish/book?bookId=${params.bookId}" class="header-menu-item-link">Student Resources</a>
              </li>
            <% } %>
            <sec:ifAllGranted roles="ROLE_BOOK_CREATOR">
              <li class="header-menu-item">
                <a href="/wonderpublish/pubDesk" class="header-menu-item-link">Publishing Desk</a>
              </li>
            </sec:ifAllGranted>
            <li class="header-menu-item search-book">
               <a href="/wonderpublish/mybooks" class="header-menu-item-link" style="padding: 0; margin-left: 30px; margin-top: 5px; display: inline-block">My Library</a>
              
              <a href="#" id="login-signup-dropdown" class="dropdown-toggle header-menu-item-link dropdown user-profile-dropdown" data-toggle="dropdown" style="padding: 0; display: inline-block; margin-left: 10px; border-left: 2px solid; padding-left: 10px;">
                <span class="icon-User_Icon"></span>
              </a>

              <input type="text" class="form-control book-search-input clerfix" id="search-book" data-provide="typeahead" placeholder="Search...">
              <button id="search-button" class="search-icon"><i class="fa fa-search" aria-hidden="true"></i></button>
              <ul class="dropdown-menu login-signup-dropdown loggedin-user" aria-labelledby="login-signup-dropdown" style="top: 30px;">
                <li class="user-area">
                  <p class="loggedin-user-name">Hello, <span class="user-name"><span class="user-name"><%= session["userdetails"]!=null?session["userdetails"].name:"" %></span></p>
                  <p class="loggedin-user-email"><span class="user-name"><%= session["userdetails"]!=null?session["userdetails"].email:"" %></p>
                </li>
                <li class="logout-are">
                  <p>Not <span class="user-name"><%= session["userdetails"]!=null?session["userdetails"].name:"" %>? <a href="/logoff" id="logout">Logout</a></p>
                </li>
              </ul>
            </li>
            %{-- <li class="header-menu-item">
            <a href="#" class="header-menu-item-link">
            <span class="icon-cart"></span>
            </a>
            </li> --}%
          </ul>
        </sec:ifLoggedIn>
      </div>
    </div>
  </nav>
</header>
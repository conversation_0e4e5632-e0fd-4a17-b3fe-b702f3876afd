<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/privatelabel/navheader_new"></g:render>

<div class="container" style="margin-top: 2rem;min-height: 700px">
    <div class="title_wrapper">
        <h1>Contact Us</h1>
        <p>Any question or remarks? Just write us a message!</p>
    </div>
    <div class="wrapper animate__animated animate__fadeInLeft">
        <div class="company-info">
            <h3 class="infoCard">Contact Information</h3>
            <p class="infoText">Fill up the form and our team will get back to you within 24 hours.</p>
            <ul class="ulList">
                <li><a href="mailto:<EMAIL>"><i class="fa fa-envelope"></i><EMAIL></a></li>
                <li><a href="tel:+91-8088443860"><i class="fa fa-phone"></i>+91-8088443860</a></li>
                <li><a href="https://wa.me/918088443860"><i class="fa fa-whatsapp"></i>+91-8088443860</a></li>
            </ul>
        </div>
        <div class="contact">
            <form id="contactForm">
                <p>
                    <label>Name</label>
                    <input type="text" name="name" id="uname" required>
                </p>
                <p>
                    <label>Email address</label>
                    <input type="email" name="email" id="qemail" required>
                </p>
                <p>
                    <label>Phone Number</label>
                    <input type="text" name="phone" id="phone" required>
                </p>
                <p>
                    <label>Company</label>
                    <input type="text" name="company" id="company" >
                </p>
                <p class="full">
                    <label>Message</label>
                    <textarea name="message" rows="5" id="message" required></textarea>
                </p>
                <p class="full">
                    <button type="submit" id="askQuery">Send Message</button>
                </p>
            </form>
        </div>
    </div>
</div>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<!--------  REPORT SUCCESS MODAL --------->
<div class="modal fade report-success__modal modal-modifier" id="report-success">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close mr-2" data-dismiss="modal" aria-label="Close" style="text-align: end">
                <span aria-hidden="true">x</span>
            </button>

            <div class="modal-body modal-body-modifier text-center">
                <div id="addedIconAnimation" class="scaleAnimation">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <h5 class="mt-3">Message Sent Successfully.</h5>

                <div class="d-flex justify-content-center py-3 col-12">
                    <button type="button" class="okBtn btn btn-lg btn-outline-secondary btn-shadow col-6 col-md-5 mr-2" data-dismiss="modal" aria-label="Close">OK</button>
                </div>
            </div>

        </div>
    </div>
</div>
<g:render template="/privatelabel/footer_new"></g:render>


<script>
    var askQuery = document.getElementById('contactForm');
    var uname = document.getElementById('uname');
    var email = document.getElementById('qemail');
    var mobile = document.getElementById('phone');
    var company = document.getElementById('company');
    var query = document.getElementById('message');

    askQuery.addEventListener('submit',function (e){
        e.preventDefault();

        email = email.value;
        mobile = mobile.value;
        company = company.value;
        query = query.value;
        uname = uname.value;
        <g:remoteFunction controller="creation" action="sendQuery" params="'name='+uname+'&email='+email+'&mobile='+mobile+'&company='+company+'&query='+query" onSuccess="querySent(data)" />
    })

    function querySent(data){
        $('#report-success').modal('show');
        document.getElementById('uname').value = ""
        document.getElementById('qemail').value = ""
        document.getElementById('phone').value = ""
        document.getElementById('company').value = ""
        document.getElementById('message').value = ""
    }
    document.querySelector('.okBtn').addEventListener('click',function (){
        window.location.reload();
    })
</script>
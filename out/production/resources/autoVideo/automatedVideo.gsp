<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
    <link href="https://fonts.googleapis.com/css?family=Rubik:400,500" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Merriweather:300,400,700" rel="stylesheet">
    <asset:stylesheet src="autovideo/style.css"/>
    %{--<script src="http://cdn.mathjax.org/mathjax/latest/MathJax.js?config=TeX-AMS_HTML-full"></script>--}%
    %{--styles: {--}%

    %{--".MathJax_Display": {--}%
    %{--"text-align": "center",--}%
    %{--margin:       "1em 0em"--}%
    %{--},--}%

    %{--".MathJax .merror": {--}%
    %{--"background-color": "#FFFF88",--}%
    %{--color:   "#CC0000",--}%
    %{--border:  "1px solid #CC0000",--}%
    %{--padding: "1px 3px",--}%
    %{--"font-style": "normal",--}%
    %{--"font-size":  "90%"--}%
    %{--}--}%

    %{--}--}%
</head>
<body class="darktheme">
<div id="overlaydark"></div>
<div >
    <audio id="audioElement"></audio>
    <audio id="clickAudioElement" src="/funlearn/showImage?id=1&fileName=clockTicking.mp3&imgType=audio"></audio>
    <audio id="backgroundAudioElement" loop="loop"></audio>
    <input type="hidden" id="placeHolder">
    <div id="slider">
        <section class="ws-title-wrapper" id="1" >
            <div class="ws-title d-flex justify-content-center align-items-center">
                <div>
                    <div class="img-wrapper">
                        <img src="/assets/autovideo/wslogo.svg" class="img-responsive">
                        <p>Always Keep Learning !</p>
                    </div>
                    <div class="form-wrapper">
                        <textarea id="quote" placeholder="Quote"></textarea><br>
                        <input type="text" id="quoteBy" placeholder="Quote By"><br>
                        <select id="language"><option value="English">English</option><option value="Hindi">Hindi</option></select><br>
                        %{--<select id="client"><option value="wonderslate">Wonderslate</option></select>--}%
                        <select id="client"><option value="wonderslate">Wonderslate</option><option value="arivupro">Arihant</option><option value="glamus">Glamus</option><option value="wonderpublish">Wonderpublish</option><option value="prepjoy">PrepJoy</option></select><br>
                        <select id="theme" onchange="testingTheme()"><option value="darkmode">dark mode</option><option value="lightmode">light mode</option></select>
                    </div>
                </div>
            </div>

        </section>
        <section class="ws-title-wrapper" id="2" style="display: none">
            <div class="ws-title d-flex justify-content-center align-items-center">
                <div class="img-wrapper" style="text-align: center;display: none" id="wonderslate">
                    <img src="/assets/autovideo/wslogo.svg" class="img-responsive">
                </div>
                <div class="img-wrapper" style="text-align: center;display: none" id="prepjoy">
                    <img src="/assets/autovideo/prepjoy.png" class="img-responsive">
                </div>
            </div>
            <div class="ws-link d-flex justify-content-end" >
                <div id="wonderslateLink" style="display:none">
                    <a href="#">www.wonderslate.com</a>
                </div>

                <div id="prepjoyLink" style="display:none">
                    <a href="#">www.prepjoy.com</a>
                </div>
            </div>

        </section>
        <section class="start" id="3" style="display: none">
            <div class="ws-quiz d-flex justify-content-center align-items-center">
                <div>
                    <div class="content-wrapper">
                        <%if(!"slidesVideo".equals(params.videoMode)){ %>
                        <p id="topictitle"><span>Topic for</span> the video</p>
                        <%}%>
                        <h3 id="quizTopic">HARAPPAN CIVILZATION</h3>
                    </div>
                    <%if("slidesVideo".equals(params.videoMode)){ %>
                    <div>
                      <span style="color: red"><br><br><br> The links for these books are available in the description.</span>
                    </div>
                    <%}%>
                    <div id="userimg" class="d-flex img-wrapper timeup">
                        <img src="/assets/autovideo/ws-userimg.svg" class="img-responsive img-quiz">
                    </div>
                </div>
            </div>
        </section>
        <section id="4" style="display: none">
            <div class="container">
                <div class="ws-quiz d-flex justify-content-start position-relative align-items-start">
                    <div class="w-100">
                        <div class="que-wrapper" style="margin:0 auto;margin-top:5rem;">
                            %{--<h4 id="questionTracker"></h4>--}%
                            <div class="d-flex mt-3 justify-content-start">
                                <span id="questionIndex"></span>

                                <div class="align-items-start question question_new w-100" id="question">
                                    <div id="slide_header"></div>
                                    <div id="slide_img" style="display: none;"></div>
                                    <div id="slide_points">
                                        <span id="slide_point1" style="display: none;"></span>
                                        <span id="slide_point2" style="display: none;"></span>
                                        <span id="slide_point3" style="display: none;"></span>
                                        <span id="slide_point4" style="display: none;"></span>
                                        <span id="slide_point5" style="display: none;"></span>

                                    </div>
                                </div>

                            </div>
                            <div class="d-flex answers" style="margin-top: 1.5rem;">
                                <div class="col-6">
                                    <div id="divop1" style="display: none">
                                        <div id="opt1class" class="d-flex option-wrapper">
                                            <p class="optionname">a.</p>
                                            <p id="opt1para">The option for the above question
                                            which possibly can be the answer will
                                            come here</p>
                                            <img src="/assets/autovideo/done.svg" class="done">
                                        </div>

                                    </div>
                                </div>
                                <div class="col-6">
                                    <div id="divop2" style="display: none">
                                        <div id="opt2class" class="d-flex option-wrapper ">
                                            <p class="optionname">b.</p>
                                            <p id="opt2para">The option for the above question
                                            which possibly can be the answer will
                                            come here</p>
                                            <img src="/assets/autovideo/done.svg" class="done">
                                        </div>

                                    </div>
                                </div>

                            </div>
                            <div class="d-flex answers" style="margin-top: 1.5rem;">
                                <div class="col-6">
                                    <div id="divop3" style="display: none">

                                        <div id="opt3class" class="d-flex option-wrapper ">
                                            <p class="optionname">c.</p>
                                            <p id="opt3para">The option for the above question
                                            which possibly can be the answer will
                                            come here</p>
                                            <img src="/assets/autovideo/done.svg" class="done">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div id="divop4"  style="display: none">

                                        <div id="opt4class" class="d-flex option-wrapper ">
                                            <p class="optionname">d.</p>
                                            <p id="opt4para">The option for the above question
                                            which possibly can be the answer will
                                            come here</p>
                                            <img src="/assets/autovideo/done.svg" class="done">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex answers" style="margin-top: 1.5rem;">
                                <div class="col-6">
                                    <div id="divop5" style="display: none">

                                        <div id="opt5class" class="d-flex option-wrapper ">
                                            <p class="optionname">e.</p>
                                            <p id="opt5para">The option for the above question
                                            which possibly can be the answer will
                                            come here</p>
                                            <img src="/assets/autovideo/done.svg" class="done">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">

                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <div class="answers mt-3" id="correctAnswerMoved" style="display: none;">
                                        <div class="d-flex option-wrapper ">
                                            <p class="optionname">a.</p>
                                            <p>The option for the above question
                                            which possibly can be the answer will
                                            come here</p>
                                            <img src="/assets/autovideo/done.svg" class="done">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="explanation justify-content-center"  id="correctAnswerExplanation" style="display: none;">
                                        <div>
                                            <span class="exp">Explanation</span>
                                            <p class="exp-answer" id="answerExplanation">
                                                This the the place where the complete explanation of the quiz that will be explainded made with several steps and ways so as the user reading this able to understand and would be able to grasp it so the he kills in the examination.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="timer-container text-center mt-4" id="timer-container" style="display: none">
                            <!--<img src="/assets/autovideo/quebgimg.svg" class="img-responsive img-answer">-->
                            <div class="timer-wrapper">

                                <div class="circle"> <svg width="54" viewBox="0 0 220 220" xmlns="http://www.w3.org/2000/svg">
                                    <g transform="translate(110,110)">
                                        <circle r="100" class="e-c-base"/>
                                        <g transform="rotate(-90)">
                                            <circle r="100" class="e-c-progress"/>
                                            <g id="e-pointer">
                                                <circle cx="100" cy="0" r="8" class="e-c-pointer"/>
                                            </g>
                                        </g>
                                    </g>
                                </svg> <div class="d-flex time-remain"><div class="display-remain-time">10</div><span>s</span></div></div>

                            </div>
                        </div>
                        <div class="img-wrapper timeup text-center mt-4" id="timeup" style="display: none">
                            <img src="/assets/autovideo/timeup.svg" class="img-responsive">
                            <h3>TIME IS UP</h3>
                        </div>
                    </div>

                </div>
                <div class="d-flex img-wrapper">
                    %{--<img src="/assets/autovideo/quebgimg.svg" class="img-responsive img-que" style="z-index: 99;">--}%
                </div>
            </div>
            <!--<div class="ws-link d-flex justify-content-end">-->
            <!--<img src="/assets/autovideo/wslogo.svg" class="img-responsive">-->
            <!--</div>-->
        </section>


        <section id="5" style="">

            <div class="ws-title thanks d-flex justify-content-center align-items-center">
                <div class="img-wrapper">

                    <blockquote>
                        <p> "Thank You for Watching!" </p>
                        <div id="wonderslateEnd" style="display: none" >
                            %{--<footer>You can also practice this quiz on</footer>--}%
                            <a href="">www.wonderslate.com</a>
                        </div>
                        <div id="arihantEnd" style="display: none" >
                            %{--<footer>You can also practice this quiz on</footer>--}%
                            <a href="">www.arihantbooks.com</a>
                        </div>
                        <div id="glamusEnd" style="display: none">
                            %{--<footer>You can also practice this quiz on</footer>--}%
                            <a style="color:#F84970;" href="">www.galmus.in</a>
                        </div>
                        <div id="wonderpublishEnd" style="display: none" >
                            %{--<footer>You can also practice this quiz on</footer>--}%
                            <a href="">www.wonderpublish.com</a>
                        </div>
                        <div id="prepjoyEnd" style="display: none" >
                            <a href="">www.prepjoy.com</a>
                        </div>
                    </blockquote>

                </div>
            </div>

            <div class="ws-link timeup d-flex justify-content-end">
                <div id="arivuproEndLink" style="display:none">
                    <img src="/assets/autovideo/arihant.png" class="img-responsive">
                </div>
                <div id="wonderslateEndLink" style="display:none">
                    <img src="/assets/autovideo/wslogo.svg" class="img-responsive">
                </div>
                <div id="glamusEndLink" style="display:none">
                    <img src="/assets/autovideo/glamus.png" class="img-responsive">
                </div>
                <div id="wonderpublishEndLink" style="display:none">
                    <img src="/assets/autovideo/wslogo-light.svg" class="img-responsive">
                </div>
                <div id="prepjoyEndLink" style="display:none">
                    <img src="/assets/autovideo/prepjoy.png" class="img-responsive">
                </div>
            </div>
        </section>

        <div class="button-wrapper">
            <button id="next-btn" onclick="startQuiz();">Next</button>
        </div>
    </div>
    <asset:javascript src="landingpage/jquery-3.2.1.min.js" /><script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js" integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q" crossorigin="anonymous"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script>
    <!--<script src="https://sdk.amazonaws.com/js/aws-sdk-2.374.0.min.js"></script>-->
    <script src="https://sdk.amazonaws.com/js/aws-sdk-2.283.1.min.js"></script>
    <asset:javascript src="autovideo/script.js"/>
    %{--<script src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.1/MathJax.js?config=TeX-MML-AM_CHTML"></script>--}%
    <script>


        //AWS POLLY config
        AWS.config.accessKeyId = "${awsAccessKeyId}";
        AWS.config.secretAccessKey = "${awsSecretAccessKey}";
        AWS.config.region = 'ap-south-1';
        //instantiating Polly class
        var polly = new AWS.Polly();
        var params = {
            OutputFormat: "mp3",
            Text:"",
            TextType: "ssml",
            VoiceId: "Aditi"
        };
        var audioElement = document.getElementById('audioElement');
        var clickAudioElement= document.getElementById('clickAudioElement');
        var currentState="input";
        var quizCurrentState="question";
        var objectiveTypes;
        var datinin;
        var currentIndex=0;
        var correctOption=0;
        var topicString="";
        function testingTheme() {
            if(document.getElementById("theme").selectedIndex==0) {
                $("body").addClass("darktheme").removeClass("lightheme");
            }
            else if(document.getElementById("theme").selectedIndex==1) {
                $("body").addClass("lightheme").removeClass("darktheme");
            }
        }

        function startQuiz(){
            if(quizData.status=="OK") {
                datinin = quizData.resources;
            }
            $("#1").hide();
            $("#next-btn").hide();
            if(document.getElementById("client").selectedIndex==0) {
                $("#wonderslate").show();
                $("#wonderslateLink").show();
                $("#wonderslateEnd").show();
                $("#wonderslateEndLink").show();
            }
            else if(document.getElementById("client").selectedIndex==1){
                $("#arivupro").show();
                $("#arivuproLink").show();
                $("#arivantEnd").show();
                $("#arivuproEndLink").show();
            }

            else if(document.getElementById("client").selectedIndex==2){
                $("#glamus").show();
                //$("#userimg").hide();
                document.getElementById("userimg").setAttribute("style", "display: none !important");
                $("#glamusLink").show();
                 $("#topictitle").hide();
                $("#glamusEnd").show();
                $("#glamusEndLink").show();
            }
            else if(document.getElementById("client").selectedIndex==3){
                $("#wonderpublish").show();
                $("#wonderpublishLink").show();
                $("#wonderpublishEnd").show();
                $("#wonderpublishEndLink").show();
            }else if(document.getElementById("client").selectedIndex==4){
                $("#prepjoy").show();
                $("#prepjoyLink").show();
                $("#prepjoyEnd").show();
                $("#prepjoyEndLink").show();
            }
            $("#2").show();
            currentState="quote";
            document.getElementById("quizTopic").innerHTML=document.getElementById("quote").value;
            topicString = document.getElementById("quote").value;

            if("Hindi"==document.getElementById("language").value) topicString = "आज का विषय है <break time=\"250ms\"/>"+document.getElementById("quote").value;
            console.log("quizTopic="+topicString);
            getAudioAndStore(topicString,"quizTopic");
            var backgroundAudioElement =document.getElementById("backgroundAudioElement");
            backgroundAudioElement.src = "AirportLounge.mp3";
            backgroundAudioElement.volume="0.03";
            audioElement.src="/funlearn/showImage?id=1&fileName=intro"+document.getElementById("language").value+document.getElementById("client").value+".mp3&imgType=audio";
            audioElement.play();
        }

        function getAudioAndStore(audioText,keyName){
            params.Text="<speak>"+audioText+"</speak>";
            console.log(audioText);
            polly.synthesizeSpeech(params, function(err, data){

                if(err){
                    console.log(err, err.stack);
                }
                else{
                    var audioStream = data.AudioStream;
                    localStorage.setItem(keyName, JSON.stringify(data.AudioStream));
                }

            });
        }

        function playNext(keyName){
            var uInt8Array = new Uint8Array(JSON.parse(localStorage.getItem(keyName)).data);
            var arrayBuffer = uInt8Array.buffer;
            var blob = new Blob([arrayBuffer]);
            var url = URL.createObjectURL(blob);
            audioElement.src = url;
            audioElement.play();

        }

        function playThankyouSlide(data){
            // $("#"+keyName).show();
            var uInt8Array = new Uint8Array(JSON.parse(data).data);
            var arrayBuffer = uInt8Array.buffer;
            var blob = new Blob([arrayBuffer]);
            var url = URL.createObjectURL(blob);
            audioElement.src = url;
            audioElement.play();

        }

        function playNextSlide(data){
            // $("#"+keyName).show();
            var uInt8Array = new Uint8Array(JSON.parse(data).data);
            var arrayBuffer = uInt8Array.buffer;
            var blob = new Blob([arrayBuffer]);
            var url = URL.createObjectURL(blob);
            audioElement.src = url;
            audioElement.play();


        }

        // (function () {
        //     var script = document.createElement("script");
        //     script.type = "text/javascript";
        //     script.src  = "https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML";
        //     document.getElementsByTagName("head")[0].appendChild(script);
        // })();

        $("#audioElement").bind('ended', function() {
            var mjaxURL  = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML';
            // var mjaxURL  = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_CHTML';
// load mathjax script
            $.getScript(mjaxURL, function() {




            // (function () {
            //     var script = document.createElement("script");
            //     script.type = "text/javascript";
            //     script.src  = "https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML";
            //     document.getElementsByTagName("head")[0].appendChild(script);
            // })();
            if ("quote" == currentState) {
                $("#2").hide();
                $("#3").show();
                playNext("quizTopic");
                currentState = "topic"
                //do the prep for next screen

            } else if ("topic" == currentState) {
                // runTopicScreen();
                // currentState="end";
                showQuiz();
                currentState = "quiz";
                quizCurrentState = "question";
                //prepare for quiz
            } else if ("quiz" == currentState) {
                if ("question" == quizCurrentState) {
                    // $("#3").hide();
                    document.getElementById("slide_point1").innerHTML = datinin[currentIndex].slide1;
                   MathJax.Hub.Queue(["Typeset", MathJax.Hub, "slide_point1"]);
                    // $("#slide_point1").show();
                    $("#slide_point1").slideDown(1500);
                    params.Text = "<speak>"+datinin[currentIndex].description1+"</speak>";
                    polly.synthesizeSpeech(params, function (err, data) {
                        if (err) {
                            console.log(err, err.stack);
                        } else {
                            // console.log("in the getAudioAndStore======"+keyName);
                            var audioStream = data.AudioStream;
                            // localStorage.setItem(keyName, JSON.stringify(data.AudioStream));
                            playNextSlide(JSON.stringify(data.AudioStream));
                        }
                        quizCurrentState = "option1";
                    });

                } else if ("option1" == quizCurrentState) {
                    if(datinin[currentIndex].slide2!=null&&datinin[currentIndex].slide2!="") {
                    // $("#3").hide();
                    document.getElementById("slide_point2").innerHTML = datinin[currentIndex].slide2;
                        MathJax.Hub.Queue(["Typeset", MathJax.Hub, "slide_point2"]);
                        $("#slide_point2").slideDown(1500);
                    params.Text = "<speak>"+datinin[currentIndex].description2+"</speak>";
                    polly.synthesizeSpeech(params, function (err, data) {
                        if (err) {
                            console.log(err, err.stack);
                        } else {
                            // console.log("in the getAudioAndStore======"+keyName);
                            var audioStream = data.AudioStream;
                            // localStorage.setItem(keyName, JSON.stringify(data.AudioStream));
                            playNextSlide(JSON.stringify(data.AudioStream));
                        }
                        quizCurrentState="option2";
                    });

                }else{
                        slider();

                    }
                }
                else if ("option2" == quizCurrentState) {
                    if(datinin[currentIndex].slide3!=null&&datinin[currentIndex].slide3!="") {
                    // $("#3").hide();
                    document.getElementById("slide_point3").innerHTML = datinin[currentIndex].slide3;
                        MathJax.Hub.Queue(["Typeset", MathJax.Hub, "slide_point3"]);
                        $("#slide_point3").slideDown(1500);
                    params.Text = "<speak>"+datinin[currentIndex].description3+"</speak>";
                    polly.synthesizeSpeech(params, function (err, data) {
                        if (err) {
                            console.log(err, err.stack);
                        } else {
                            // console.log("in the getAudioAndStore======"+keyName);
                            var audioStream = data.AudioStream;
                            // localStorage.setItem(keyName, JSON.stringify(data.AudioStream));
                            playNextSlide(JSON.stringify(data.AudioStream));
                        }
                        quizCurrentState="option3";
                    });

                }else {
                        slider();

                    }
                }
                else if ("option3" == quizCurrentState) {
                    if(datinin[currentIndex].slide4!=null&&datinin[currentIndex].slide4!="") {
                    // $("#3").hide();
                    document.getElementById("slide_point4").innerHTML = datinin[currentIndex].slide4;
                        MathJax.Hub.Queue(["Typeset", MathJax.Hub, "slide_point4"]);
                        $("#slide_point4").slideDown(1500);
                    params.Text = "<speak>"+datinin[currentIndex].description4+"</speak>";
                    polly.synthesizeSpeech(params, function (err, data) {
                        if (err) {
                            console.log(err, err.stack);
                        } else {
                            // console.log("in the getAudioAndStore======"+keyName);
                            var audioStream = data.AudioStream;
                            // localStorage.setItem(keyName, JSON.stringify(data.AudioStream));
                            playNextSlide(JSON.stringify(data.AudioStream));
                        }
                        quizCurrentState="option4";
                    });

                }else {
                        slider();
                    }
                }
             else if ("option4" == quizCurrentState) {
                    if(datinin[currentIndex].slide5!=null&&datinin[currentIndex].slide5!="") {
                // $("#3").hide();
                document.getElementById("slide_point5").innerHTML = datinin[currentIndex].slide5;
                        MathJax.Hub.Queue(["Typeset", MathJax.Hub, "slide_point5"]);
                        $("#slide_point5").slideDown(1500);
                params.Text = "<speak>"+datinin[currentIndex].description5+"</speak>";
                polly.synthesizeSpeech(params, function (err, data) {
                    if (err) {
                        console.log(err, err.stack);
                    } else {
                        // console.log("in the getAudioAndStore======"+keyName);
                        var audioStream = data.AudioStream;
                        // localStorage.setItem(keyName, JSON.stringify(data.AudioStream));
                        playNextSlide(JSON.stringify(data.AudioStream));
                    }
                    quizCurrentState="answerExplained";
                });

            }else{
                       slider();
                    }
                }
                else if("answerExplained"==quizCurrentState){
                    slider();

                }
            }
            });

        });


        function slider() {
            if((currentIndex+1)<datinin.length){
                if(currentIndex==(datinin.length-2)){


                }
                currentIndex +=1;
                resetDisplay();
                showQuiz(currentIndex);
                currentState="quiz";
                quizCurrentState="question";
                //prepare thank you also

            }
            else{
                $("#4").hide();
                $("#5").show();
                if(document.getElementById("client").selectedIndex==2) {
                    var placeHolderText = "Thank you for watching our video,  please subscribe to our you tube channel. For more details please visit our page, link given in the description below.";
                }else{
                    var placeHolderText = "Thank you for watching the video. If you have any suggestion for us feel free to leave comments.\n" +
                        "If you liked the video and want to see more of it hit the thumbs up and dont forget to click on subscribe button to get more updates of the future videos.";
                }
                if("Hindi"==document.getElementById("language").value) placeHolderText= "वीडियो देखने के लिए धन्यवाद।। यदि आपके पास हमारे लिए कोई सुझाव है तो comment करें। \ n" +
                    "अगर आपको वीडियो पसंद आया है और इसे अधिक देखना चाहते हैं, तो like  बटन पर क्लिक  करें और भविष्य के वीडियो के अधिक अपडेट प्राप्त करने के लिए subscribe बटन पर क्लिक करें";
                currentState="quiz";
                quizCurrentState="thankyou";
                params.Text = "<speak>"+placeHolderText+"</speak>";
                polly.synthesizeSpeech(params, function (err, data) {
                    if (err) {
                        console.log(err, err.stack);
                    } else {
                        // console.log("in the getAudioAndStore======"+keyName);
                        var audioStream = data.AudioStream;
                        // localStorage.setItem(keyName, JSON.stringify(data.AudioStream));
                        playNextSlide(JSON.stringify(data.AudioStream));
                    }
                });
            }


        }


        function removeContentFromLocalStorageAll(){
            var datinin = quizData.resources;
            for ( var j = 0; j < datinin.length; j++) {
                localStorage.removeItem("quizTopic");
                localStorage.removeItem("resources");
                localStorage.removeItem("thankyou");
            }

        }




        function slideend(){
            $("#4").hide();
            $("#5").show();
            if(document.getElementById("client").selectedIndex==2) {
                var placeHolderText = "Thank you for watching our video,  please subscribe to our you tube channel. For more details please visit our page, link given in the description below.";
            }else{
                var placeHolderText = "Thank you for watching the video. If you have any suggestion for us feel free to leave comments.\n" +
                    "If you liked the video and want to see more of it hit the thumbs up and dont forget to click on subscribe button to get more updates of the future videos.";
            }
            if("Hindi"==document.getElementById("language").value) placeHolderText= "वीडियो देखने के लिए धन्यवाद।। यदि आपके पास हमारे लिए कोई सुझाव है तो comment करें। \ n" +
                "अगर आपको वीडियो पसंद आया है और इसे अधिक देखना चाहते हैं, तो like  बटन पर क्लिक  करें और भविष्य के वीडियो के अधिक अपडेट प्राप्त करने के लिए subscribe बटन पर क्लिक करें";
            currentState="quiz";
            quizCurrentState="thankyou";
            params.Text = "<speak>"+placeHolderText+"</speak>";
            polly.synthesizeSpeech(params, function (err, data) {
                if (err) {
                    console.log(err, err.stack);
                } else {
                    // console.log("in the getAudioAndStore======"+keyName);
                    var audioStream = data.AudioStream;
                    // localStorage.setItem(keyName, JSON.stringify(data.AudioStream));
                    playNextSlide(JSON.stringify(data.AudioStream));
                }
            });
        }


        function runTopicScreen(){
            $("#2").hide();
            $("#3").show();
            playNext("quizTopic");
        }

        function removeContentFromLocalStorage(j){
            localStorage.removeItem("quizTopic");
            localStorage.removeItem("resources"+j);

        }

    </script>

    <script>
        //circle start
        var progressBar = document.querySelector('.e-c-progress');
        var indicator = document.getElementById('e-indicator');
        var pointer = document.getElementById('e-pointer');
        var length = Math.PI * 2 * 100;
        var audioPaused=false;

        progressBar.style.strokeDasharray = length;

        function update(value, timePercent) {
            var offset = - length - length * value / (timePercent);
            progressBar.style.strokeDashoffset = offset;

        };

        //circle ends
         const displayOutput = document.querySelector('.display-remain-time')
        const pauseBtn = document.getElementById('pause');
        const setterBtns = document.querySelectorAll('button[data-setter]');

        var intervalTimer;
        var timeLeft;
        var wholeTime = 1 * 60; // manage this to set the whole time
        var isPaused = false;
        var isStarted = false;


        update(wholeTime,wholeTime); //refreshes progress bar
        displayTimeLeft(wholeTime);

        function changeWholeTime(seconds){
            if ((wholeTime + seconds) > 0){
                wholeTime += seconds;
                update(wholeTime,wholeTime);
            }
        }

        for (var i = 0; i < setterBtns.length; i++) {
            setterBtns[i].addEventListener("click", function(event) {
                var param = this.dataset.setter;
                switch (param) {
                    case 'minutes-plus':
                        changeWholeTime(1 * 60);
                        break;
                    case 'minutes-minus':
                        changeWholeTime(-1 * 60);
                        break;
                    case 'seconds-plus':
                        changeWholeTime(1);
                        break;
                    case 'seconds-minus':
                        changeWholeTime(-1);
                        break;
                }
                displayTimeLeft(wholeTime);
            });
        }

        function timer (seconds){ //counts time, takes seconds

            var remainTime = Date.now() + (seconds * 1000);
            displayTimeLeft(seconds);

            intervalTimer = setInterval(function(){
                timeLeft = Math.round((remainTime - Date.now()) / 1000);
                if(timeLeft < 0){
                    clearInterval(intervalTimer);
                    displayTimeLeft(wholeTime);
                    clickAudioElement.pause();
                    $("#timer-container").hide();
                    $("#timeup").show();

                    playNext("correctAnswer_"+currentIndex);
                    quizCurrentState="correctAnswer";
                    return ;
                }
                displayTimeLeft(timeLeft);
            }, 1000);
        }

        function pauseTimer(event){
            if(isStarted === false){
                timer(wholeTime);
                isStarted = true;
                this.classList.remove('play');
                this.classList.add('pause');

                setterBtns.forEach(function(btn){
                    btn.disabled = true;
                    btn.style.opacity = 0.5;
                });

            }else if(isPaused){
                this.classList.remove('play');
                this.classList.add('pause');
                timer(timeLeft);
                isPaused = isPaused ? false : true
            }else{
                this.classList.remove('pause');
                this.classList.add('play');
                clearInterval(intervalTimer);
                isPaused = isPaused ? false : true ;
            }
        }

        function displayTimeLeft (timeLeft){ //displays time on the input
            var minutes = Math.floor(timeLeft / 60);
            var seconds = timeLeft % 60;
            var displayString = seconds;
            displayOutput.textContent = displayString;
            update(timeLeft, wholeTime);
        }

        function startTimer(){
            $("#timer-container").show();
            clickAudioElement.play();
            clickAudioElement.volume="1";
            wholeTime =5;
            timer(wholeTime);
        }

        function getQuestionAnswers(resId){
            <%if(params.resId!=null){%>
            <g:remoteFunction controller="autoVideo" action="getResourceSlide" params="'resId='+resId"
                onSuccess = "setQuiz(data);"/>
            <%}else{%>
            <g:remoteFunction controller="autoVideo" action="getResourceSlide" params="'videoMode=slidesVideo&title=${params.title}'"
                onSuccess = "setQuiz(data);"/>
            <%}%>
        }

        function setQuiz(data){
            quizData=data;
            if(quizData.status=="OK"){
                var datinin = quizData.resources;
                for(var j=0;j<datinin.length;j++){
                    document.getElementById("quote").innerHTML = datinin[j].resourceName;
                }
            }
        }

        function showQuiz(){

            $("#3").hide();
            document.getElementById("slide_header").innerHTML=datinin[currentIndex].description;
            document.getElementById("slide_img").innerHTML=datinin[currentIndex].slide;
            $("#4").show();
            $("#slide_header").show();
            if(datinin[currentIndex].slide!=null && datinin[currentIndex].slide!=="") {
                $("#slide_img").show();
            } else {
                $("#slide_img").hide();
            }

            if(datinin[currentIndex].slide1!=null && datinin[currentIndex].slide1!=="") {
                $("#slide_points").show();
            } else {
                $("#slide_points").hide();
            }

            params.Text =  "<speak>"+datinin[currentIndex].description+"</speak>";
            polly.synthesizeSpeech(params, function (err, data) {
                if (err) {
                    console.log(err, err.stack);
                } else {
                    // console.log("in the getAudioAndStore======"+keyName);
                    var audioStream = data.AudioStream;
                    // localStorage.setItem(keyName, JSON.stringify(data.AudioStream));
                    playNextSlide(JSON.stringify(data.AudioStream));
                }
            });
        }
        getQuestionAnswers(${params.resId});

        document.body.onkeyup = function(e){
            if(e.keyCode == 32){
                //your code
                if(audioPaused){
                    audioPaused=false;
                    audioElement.play();
                }else{
                    audioPaused=true;
                    audioElement.pause();
                }
            }
        }
        function resetDisplay(){
            // document.getElementById("questionTracker").innerHTML="";
            //document.getElementById("question").innerHTML="";
            document.getElementById("slide_header").innerHTML="";
            $("#slide_header").hide();
            $("#slide_img").hide();
            $("#slide_point1").hide();
            $("#slide_point2").hide();
            $("#slide_point3").hide();
            $("#slide_point4").hide();
            $("#slide_point5").hide();
        }

        // function test () { MathJax.Hub.Typeset(); };

    </script>

</div>
</body>
</html>

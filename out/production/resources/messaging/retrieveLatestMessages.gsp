<%@ page import="org.joda.time.*" %>
<asset:stylesheet href="speechbubbles.css"/>
<%
    String[] arr=String.valueOf(Double.parseDouble(timeZone)).split("\\.");
    int hrs = Integer.parseInt(arr[0]);
    int mns = Integer.parseInt(arr[1])*60/10;

	DateTimeZone localTz = DateTimeZone.forOffsetHoursMinutes(hrs,mns);
	DateTime currentUTC = new DateTime(DateTimeZone.UTC);
	DateTime weekBeforeUtc = currentUTC.minusWeeks(1);
	DateTime MonthBeforeUtc = currentUTC.minusMonths(1);
%>
<g:each in="${messages}" var="message">
<% 
	DateTime dtUtc = new DateTime(message.date,DateTimeZone.UTC );
	DateTime dtLocal = dtUtc.withZone(localTz);
		
	String displayStr = "";

	if(!session.thisMonth) { 
		if(currentUTC.isAfter(MonthBeforeUtc)) {
			if(!session.thisMonth)  session.thisMonth = true;
			displayStr = "This month";
		}
	}	
	
	if(!session.thisWeek) { 	
		if(currentUTC.isAfter(weekBeforeUtc)) {
			if(!session.thisWeek)  session.thisWeek = true;
			displayStr = "This week";
		}
	}		

	if(!session.isToday && dtLocal.toString("YYYY-MM-dd").equals(currentUTC.toString("YYYY-MM-dd"))) { 
		if(!session.isToday)  session.isToday = true;
		displayStr = "Today";
	}	
	
	if(!displayStr.equals("")) {
%>
<table width=100%>
	<tr><td>&nbsp;</td></tr>
	<tr valign=absmiddle align=center>
	<td colspan=2>
	<div class="nobubble">&nbsp;&nbsp;<%=displayStr%>&nbsp;&nbsp;</div>
	</td>
	</tr>
	<tr><td>&nbsp;</td></tr>
</table>
<% } %>
<table width=100%>
	<tr valign=absmiddle>
	<% if((session['userdetails'].id+"").equals(message.userId+"")) { %>
	<td width=10% style="color:gray;font-size:10.5px">
	&nbsp;Me&nbsp<br>&nbsp;<%=dtLocal.toString("HH:mm")%>&nbsp
	</td>
	<% } %>
	<td>
	<div class="bubble <%=!(session['userdetails'].id+"").equals(message.userId+"")?"you":"me"%>">
        ${message.message} 
	</div>
	</td>
	<% if(!(session['userdetails'].id+"").equals(message.userId+"")) { %>
	<td width=10% style="color:gray;font-size:10.5px">
	<%
		int id = -1;
		if(session.groupChatId!=null) {
			for(int i=0;i<session.friendIds.length;i++) {
				if(session.friendIds[i].equals(message.userId+"")){
					id = i;
					break;
				}
			}

			if(id!=-1) {
	%>
	&nbsp;${session.friendNms[id]}&nbsp;		
	<% 	
			}
		} else { 
	%>
	&nbsp;${session.friendNm}&nbsp;
	<%	} %>
	<br>&nbsp<%=dtLocal.toString("HH:mm")%>&nbsp;
	</td>
	<% } %>
	</tr>
</table>	
</g:each>

<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<div>
    <div class="container-fluid pub-desks publishing_desk my-5 px-5">
       <h4>${title}</h4>
        <br>
        <b>Price :</b>&nbsp;${price}&nbsp;&nbsp;&nbsp;<b>Validity (in days)</b>&nbsp;${validityDays}
        <br>
       Validity (months):  <select id="validityMonths" name="validityMonths"><option>Select</option>
        <option value="1">1</option>
        <option value="2">2</option>
        <option value="3">3</option>
        <option value="4">4</option>
        <option value="5">5</option>
        <option value="6">6</option>
        <option value="7">7</option>
        <option value="8">8</option>
        <option value="9">9</option>
        <option value="10">10</option>
        <option value="11">11</option>
        <option value="12">12</option>

        </select>
        &nbsp;&nbsp; Price&nbsp;&nbsp;<input type="number" id="price" name="price"><br>
        <input type="button" value="Submit" onclick="addPriceDetails();"><br><br>
        <div id="priceDetails">

        </div>
        </div>
</div>

<g:render template="/${session['entryController']}/footer_new"></g:render>

<script>

    function getPriceDetails(){
        <g:remoteFunction controller="wsshop" action="getValidityExtensionDtls" params="'bookId=${bookId}'" onSuccess="displayPriceDetails(data)"></g:remoteFunction>
    }

    function displayPriceDetails(data){
        var priceDetails = JSON.parse(data.priceDetails);

        var htmlStr ="<table border='1' width='600'><tr><td>Validity (in months)</td><td>Price</td><td></td></th>";
        if(priceDetails.length==0){
            htmlStr +="<tr><td colspan='3'>No details added yet</td></tr>";
        }else {
            for (var i = 0; i < priceDetails.length; i++) {
                htmlStr += "<tr><td>"+priceDetails[i].validityMonths+"</td>"+
                        "<td>"+priceDetails[i].price+"</td>"+
                        "<td><a href=javascript:deletePrice("+priceDetails[i].id+");>Delete</a></td></tr>";

            }
        }
        htmlStr +="</table>";
        document.getElementById("priceDetails").innerHTML = htmlStr;
    }
    getPriceDetails();

    var validityMonths;
    var price;

    function addPriceDetails(){
        if(document.getElementById("validityMonths").selectedIndex==0){
            alert("Please select number of months");
        }
        else if(document.getElementById('price').value==""){
            alert("Please enter price");
        }else{
            validityMonths = document.getElementById("validityMonths")[document.getElementById("validityMonths").selectedIndex].value;
            price = document.getElementById('price').value;
            <g:remoteFunction controller="wsshop" action="addValidityDetails" params="'bookId=${bookId}&validityMonths='+validityMonths+'&price='+price" onSuccess="displayPriceDetails(data)"></g:remoteFunction>
        }

    }

    function deletePrice(priceId){
        if(confirm("Are you sure to delete this price?")) {
            <g:remoteFunction controller="wsshop" action="deleteValidityDetails" params="'priceId='+priceId" onSuccess="displayPriceDetails(data)"></g:remoteFunction>
        }

    }
</script>

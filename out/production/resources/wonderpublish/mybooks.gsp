<g:render template="/wonderpublish/loginChecker"></g:render>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>

<% if("books".equals(session["entryController"])){%>
<asset:stylesheet href="wonderslate/myLibrary.css"/>
<%}%>
<style>
.new_way {
    position: relative;
    background: #88b7d5;
    width: 100px;
    text-align: center;
    margin: 0 auto;
    transform: rotate(180deg);
    box-shadow: rgba(0, 0, 0, 0.3) 0 1px 4px -1px;
}

.new_way::after {
    content: '';
    position: absolute;
    top: -5px;
    left: calc(50% - 10px);
    background: #fff;
    width: 20px;
    height: 20px;
    box-shadow: rgba(0, 0, 0, 0.3) 0 1px 4px -1px;
    /* The points are: (left top: x y, right top: x y, center bottom: x y) */
    clip-path: polygon(0 0, 100% 0, 50% 100%);
}
</style>
<%if('24'.equals(''+session.getAttribute('siteId'))){%>
<link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700;800&display=swap" rel="stylesheet">
<style>

.hilight{
        background: #DCDDDE;
    }
.library #content-data-books .card {
    margin-bottom: 30px;
    width: 250px;
    border-radius: 0;
}
.library #content-data-books .card .card-body {
    display: none !important;
}
.library #content-data-books .lib-showcase {
    min-height: 320px;
    border-radius: 0;
}
.library #content-data-books .uncover {
    height: 320px;
    padding: 10px;
    border-radius: 0;
}
.library #content-data-books .card img {
    width: 100%;
    height: 320px;
    border-radius: 0;
    box-shadow: none;
}
.library #total-books-of-user {
    font-family: 'Open Sans', sans-serif;
    font-weight: bold;
}
.library #subjectFilter .dropdown,
.library #subjectFilter #sortBy,
.library #content-data-books .uncover,
#ebpagination .pagination .page-link {
    font-family: 'Open Sans', sans-serif;
}

@media (max-width:350px){
    #libraryExpiredModal .modal-body p {
        font-size: 14px !important;
    }
    #libraryExpiredModal .modal-body p br {
        /*display: none;*/
    }
}
</style>
<%}%>

<sec:ifNotLoggedIn>
    <script>
        loggedIn=false;
    </script>
</sec:ifNotLoggedIn>

<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<style>
.submit_icon span {
    font-size: 70px;
}
.submit_icon p {
    color: #777;
}
.fetch_icon span, .invalid_icon span {
    opacity: .7;
}
.package_book_collapse {
    position: relative;
    margin: 0;
    /*width: 450px;*/
    /*max-width: 450px;*/
}
.package_books {
    background: #FFFFFF;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
    border-radius: 10px;
    position: relative;
    left: 0;
    right: 0;
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    z-index: 99;
}
.package_books .package_book_list {
    padding-bottom: 0rem;
}
.package_books .package_book_list:last-child {
    padding-bottom: 0;
}
.package_books .package_book_list a {
    color: #000000;
    width: 85px;
    font-size: 11px;
    margin: 0 10px;
}
.package_books .package_book_list a span {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}
.package_books .package_book_list a:hover {
    color: #F79420;
}
@media (max-width: 767px) {
    .library .tab-content .card .lib-showcase img {
        height: 192px;
    }
}
</style>

<section class="library my_books" style="background:none;">
    <div class="container">
        <% if("books".equals(session["entryController"])){%>
        <div class="row page_title d-flex pt-3 pb-3 pb-lg-4 px-0 mb-0 col-12 col-md-10 mx-auto align-items-center">
            <i class="material-icons back-arrow" onclick="javascript:window.history.back();">keyboard_backspace</i>
            <h3 class="text-radial-gradient d-block"><strong>My Library</strong></h3>
        </div>
        <%}%>
        <ul class="nav nav-tabs col-12 col-md-10 mx-auto" role="tablist" >
            <li class="nav-item">
                <a class="nav-link active book" data-toggle="tab" href="#books"><%= "1".equals(""+session.getAttribute("siteId"))?"eBooks & Courses":"" %></a>
            </li>
            <% if("books".equals(session["entryController"])){%>
%{--            <li class="divider" style="display: none;">--}%
%{--                <img src="${assetPath(src: 'ws/chapter-line.png')}">--}%
%{--            </li>--}%
            <%}%>
            <%if("1".equals(""+session.getAttribute("siteId"))){%>
%{--                <li class="nav-item">--}%
%{--                    <a class="nav-link vidClass" data-toggle="tab" href="#classroom">Classroom</a>--}%
%{--                </li>--}%
            <%}%>
        </ul>

        <div class="tab-content col-12 col-md-10 px-0 mx-auto">
            <div id="books" class="tab-pane active">
                <div class="row justify-content-between align-items-center m-0" id="sin">
                    <div class="username">
                        <%if('24'.equals(''+session.getAttribute('siteId'))){%>
                            <p style="color: #000000; font-size: 20px;font-family:'Open Sans', sans-serif;text-transform: capitalize;">Welcome ${session['userdetails'].name.split(" ")[0]}, <br>Happy Reading!</p>
                        <%}else if(!"books".equals(session["entryController"])){%>
                            <p>Hey, <span class="greeting-user-name">${session['userdetails'].name.split(" ")[0]}!</span></p>
                        <%}%>
                        <p class="total-books mb-0" id="total-books-of-user"></p>
                    </div>
                    <div class="d-flex align-items-end mt-2 mt-md-0">
                        <div id="subjectFilter" class="d-flex justify-content-between align-items-left mr-3">
                            <div class="dropdown form-inline">
                                Sort By &nbsp;&nbsp;<select id="sortBy" name="sortBy" onchange="sortDisplay();" class="form-control form-control-sm">
                                <option value="lastRead">Last Read</option>
                                <option value="dateAdded">Date Added</option>
                                <option value="titleAsc">Title(A-Z)</option>
                                <option value="titleDesc">Title(Z-A)</option>
                            </select>
                            </div>
                        </div>
                       <%if("1".equals(""+session.getAttribute("siteId"))||"22".equals(""+session.getAttribute("siteId"))){%> <a href="/test-generator" class="btn btn-default btn-primary generate"><img src='${assetPath(src: 'landingpageImages/generate-btn.svg')}'> Generate a Test</a><%}%>
                        </div>
                </div>
                <%if(siteMst!=null&&"true".equals(siteMst.exercises)){%>
                <hr>
                <div class="access_code my-4">
                    <div class="col-12 col-lg-7 col-sm-10 mb-5 p-0">
                        <form id="bookAccessForm" novalidate>
                            <h4 for="accessCode" class="p-0">Book Access Code</h4>
                            <div class="input-group">

                                <input type="text" id="accessCode" value="" class="form-control form-control-lg rounded-0 shadow" placeholder="New Book? Enter the access code here" aria-describedby="accessCodeHelpBlock" required>
                                <div class="input-group-append">
                                    <button id="codeSubmit" class="btn btn-danger shadow" type="button" onclick="checkAccessCode();">
                                        SUBMIT
                                    </button>
                                </div>
                                    <div class="invalid-feedback">
                                    Invalid access code! Please check and re-enter.
                                </div>
                            </div>
                        </form>
                    </div>

                    <div class="fetching_book text-center col-10 col-lg-6 col-sm-8" style="display: none" id="invalidCode">
                        <div class="submit_icon invalid_icon">
                            <span class="material-icons text-danger">sync</span>
                            <p>Invalid access code!<br>Please re-enter.</p>
                        </div>
                    </div>

                </div>
<%}%>

                <div class="books-content-wrapper" id="content-data-books">

                </div>
                <div id="ebpagination" class="mt-3"></div>
            </div>

            <script>
                $(document).ready(function() {
                    $("#codeSubmit, #codeSubmitIcon").click(function() {

                        //Fetch form to apply custom Bootstrap validation
                        var form = $("#bookAccessForm");

                        if (form[0].checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.addClass('was-validated');

                        //Make ajax call here

                    });
                });
            </script>


        </div>
</section>
<div class="modal" id="deleteBook">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header" style="min-height: 50px;">
                <h4 class="modal-title"></h4>

            </div>

            <!-- Modal body -->
            <div class="modal-body">
                <p class="text-center" style="font-size: 18px;" id="remove-msg">Are you sure you want to Remove eBook from your library?.</p>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-primary btn-primary1" onclick="javascript:bookDelete();">Yes</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
            </div>

        </div>
    </div>
</div>

<div class="modal" id="libraryExpiredModal">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">

            <!-- Modal body -->
            <div class="modal-body">
            <p class="text-center mb-0 py-3" style="font-size: 16px;font-family: 'Open Sans',sans-serif;">Oops, your subscription has expired. <br><a href="mailto:<EMAIL>" style="font-size: 16px;font-family: 'Open Sans',sans-serif;font-weight: 600;text-decoration: underline;">Contact</a> the system admin.</p>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-sm btn-primary col-4 col-md-3" data-dismiss="modal" style="font-family: 'Open Sans',sans-serif;">Okay</button>
            </div>

        </div>
    </div>
</div>

<%if("1".equals(""+session["siteId"])){%>
<g:render template="/resources/relatedBooks"></g:render>
<%}%>


<g:render template="/${session['entryController']}/footer_new"></g:render>

<asset:javascript src="moment.min.js"/>
<!--</div>-->

<asset:javascript src="searchContents.js"/>


<asset:javascript src="jquery.tablesorter.min.js"/>

<asset:javascript src="clock.js"/>
<g:render template="/funlearn/topicscripts"></g:render>
<script>
    document.title = "My Library";

    var booksData;
    var pg_start=0
    var pg_end=6
    var pg_no=1
    var paginationIndex = 0
    function getBooksList(){
        var Showlibrary="${showLibrary}";
        if("25"!="${session['siteId']}") {
            if("24"=="${session['siteId']}" && Showlibrary=="false"){
                $("#libraryExpiredModal").modal("show");
            }else{
                <g:remoteFunction controller="wonderpublish" action="getBooksListForUser"  onSuccess='mainDisplayBooks(data);' params="'apiMode=optimized'"/>
            }
        }else{
            <g:remoteFunction controller="institute" action="getLibraryBooks"  onSuccess='mainDisplayBooksForLibwonder(data);'/>
        }
    }

   function checkAccessCode(){

        $("#invalidCode").hide();
        var bookCode  = document.getElementById("accessCode").value;
        if(bookCode&&/^\d+$/.test(bookCode)){
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="wsshop" action="bookCdValidate"  onSuccess='accessModeChecked(data);' params="'bookCode='+bookCode"/>
        }

   }

   function accessModeChecked(data) {
       var status = data.status;
       $('.loading-icon').addClass('hidden');
       if(status=="allocated"){
           alert("eBook successfully added to your library. We will now load it.");
           $('.loading-icon').removeClass('hidden');
           getBooksList();
       }else{
           alert('Invalid code');
       }
   }

    function mainDisplayBooks(data){
        booksData = data;
        displayBooks(data);
    }

    function mainDisplayBooksForLibwonder(data){
        booksData = data;
    if(data.status!="Nothing present"){
        displayBooks(data);
    }else{
        document.getElementById("content-data-books").innerHTML = "<div class='no-books-available'>" +
            "<div class='no-book-wrapper'>" + "<img class='book-image img-responsive' src='${assetPath(src: 'wonderslate/img_desert.png')}' style='margin:0 auto;border:none;'>" + "</div>" + "<p class='text-center mt-2'>It's like a desert in here! No eBooks added yet,<br>Please contact your institute.</p>" +
            "</div>";
    }
    }

    function displayBooksViaPagination(starti, endi, pageNo){
        pg_start=starti
        pg_end=endi
        pg_no=pageNo
        displayBooks(booksData)
    }


    function sortDisplay(){
        pg_start=0
        pg_end=6
        pg_no=1
        paginationIndex = 0
        displayBooks(booksData);
    }
    function displayBooks(data) {
        var colors=['#2EBAC6','#0D5FCE','#6FCF97','#F2C94C','#C20232','#FC7753','#E40039','#1abc9c','#FD7272','#55E6C1','#17c0eb'];
        var books = JSON.parse(data.books);
        //now remove dublicate 
        var newObj = {};
        var newArr = [];
        for(var i=0; i< books.length; i++){
            newObj[books[i]['id']] = books[i];
        }
        var keys = Object.keys(newObj);
        for(var i=keys.length-1; i>=0; i--){
            newArr.push(newObj[keys[i]])
        }
        books=newArr;
        var lastReadBooks = JSON.parse(data.lastReadBooks);
        for (var i = 0; i < books.length; ++i) {
            //this is for the book ids which may not have been viewed.
         books[i].sortNo=99999;
        }

        //now add the sort number to the books
        for(var j = 0; j < lastReadBooks.length;j++){
            for (var i = 0; i < books.length; ++i) {
                //this is for the book ids which may not have been viewed.
                if(lastReadBooks[j].bookId==books[i].id){
                    books[i].sortNo=j;
                    break;
                }

            }
        }
        if(document.getElementById("sortBy").selectedIndex==0) {
            books.sort(SortByLastRead);
        }else if(document.getElementById("sortBy").selectedIndex==1) {
           //dont do anything as books are by default sorted by date added.
        } else if(document.getElementById("sortBy").selectedIndex==2) {
            books.sort(SortByTitle);
        }else if(document.getElementById("sortBy").selectedIndex==3) {
            books.sort(SortByTitle);
            books.reverse(SortByTitle);
        }

        var htmlStr = "";
         var imgSrc = "";
        var ratingCount = data.books.rating;
        <% if("books".equals(session["entryController"])){%>
            htmlStr +="<h4>eBooks</h4>"+"<div class='row my-collapse'>";
        <%} else {%>
            if(siteId!=24){
                htmlStr +="<h4>All eBooks</h4>"+"<div class='row'>";
            }else{
                htmlStr+="<div class='row'>"
            }
        <%}%>
         var noOfBooks=0;
        var dataState="registered";

        var urlTag = "";
        var i = 0
        var end = books.length;
        var myArrs=[]; //Store book with packagebook Id
        var array;
        if(siteId==24){
            i=pg_start
            end=pg_end
            if(books.length<pg_end){
                pg_end=books.length
                end = books.length
            }
        }
         for (i; i < end; ++i) {
            dataState="registered";
                imgSrc = "/funlearn/showProfileImage?id="+books[i].id+"&fileName="+books[i].coverImage+"&type=books&imgType=passport";
            if (books[i].rating ==0 || books[i].rating ==0.0 || books[i].rating==null){
                ratingCount="";
            } else {
                ratingCount="(" + books[i].rating + ")";
            }
            var totalUserBooks = "";

            if(books[i].bookType!='test') {

                if (books[i].level != "") {
                    if ("School" == books[i].level) urlTag = replaceAll(books[i].syllabus?books[i].syllabus:'',' ', '-').toLowerCase();
                    else urlTag = replaceAll(books[i].grade?books[i].grade:'',' ', '-').toLowerCase();
                }
                else urlTag = "book";
                noOfBooks +=1;
                <%if('24'.equals(''+session.getAttribute('siteId'))){%>
                htmlStr += "<div class='col-6 col-md-4 col-lg-4' id='packbooks"+ books[i].id +"'>";
                <%} else {%>
                htmlStr += "<div class='col-6 col-md-4 col-lg-3' id='packbooks"+ books[i].id +"'>";
                <%}%>
                <%if(("1".equals(""+session["siteId"]))||("25".equals(""+session["siteId"]))){%>
                if((books[i].packageBookIds != "" && books[i].packageBookIds != null && books[i].packageBookIds != "null")&&books[i].packageBookId == undefined) {
                    myArrs.push(books[i].packageBookIds);  //push package bookids to array
                    for(var j=0;j<myArrs.length;j++){
                        array = JSON.parse("[" + myArrs[j] + "]");   //parse to package bookid string to array
                    }
                    htmlStr += "<a class='lib-showcase' onclick='packageToggle("+books[i].id+")'  data-toggle='collapse' aria-expanded='false' aria-controls='packageBooksLists"+ books[i].id +"'>";

                    htmlStr +="<div class='card'>" ;
                    if (books[i].coverImage === null || books[i].coverImage == "null" || books[i].coverImage == "") {
                        htmlStr +="<div class='uncover'>"+
                            "<p>"+books[i].title+"</p>"+
                            "</div>";
                    }
                    else {
                        htmlStr += "<div class='d-flex'><img src='" + imgSrc + "' alt='' class='img-hero'>" ;
                        for (var l = 0; l < array.length; l++) {
                            //check packageid present in bookid and display package books
                            var item = books.find(item =>
                                item.id === array[0]
                            );
                            if ( item != undefined && item.coverImage != '') {
                                var subImg = "/funlearn/showProfileImage?id=" + item.id + "&fileName=" + item.coverImage + "&type=books&imgType=passport";
                            }
                            else{
                                var subImg='';
                            }
                        }
                        htmlStr += "<img src='" + subImg + "' alt='' class='img-child'/></div>" ;
                        //htmlStr += "</div>" ;
                    }
                    htmlStr +="</a>"+
                        "<div class='card-body d-flex justify-content-between'>";
                    htmlStr +="<a class='card-text' href='/"+urlTag+"/" + encodeURIComponent(books[i].title).replace(/'/g,"&#39;").toLowerCase() + "/book?bookId=" + books[i].id + "&siteName=${session['entryController']}'>"
                            + books[i].title + "</a>" ;
                    <%if("1".equals(""+session["siteId"])){%>
                    if((books[i].price==0||books[i].price==0.0||books[i].price==undefined)&&books[i].packageBookId==undefined&&(''+books[i].batchName=='')) {
                        htmlStr += "<div class='dropup d-flex'>" +
                            "<button class='btn dropdown-toggle' data-toggle='dropdown'><i class='material-icons'>more_vert</i></button>" +
                            "<div class='dropdown-menu'>" +
                            "<a class='dropdown-item delete' href='javascript:removeFromLibrary(" + books[i].id + ")'>Delete</a>" +
                            "</div>" +
                            "</div>";
                    }
                    <%}%>
                    htmlStr +=   "</div>" +
                        "</div>" ;

                    //accordian
                    htmlStr +="<div class='collapse row package_book_collapse' id='packageBooksLists"+ books[i].id +"'>\n" +
                        // "<div class=\"new_way\">\n" +
                        // "</div>"+
                        "  <div class='package_books p-3 my-1 mb-4'>\n" ;
                    var subImg = "/funlearn/showProfileImage?id=" + books[i].id + "&fileName=" + books[i].coverImage + "&type=books&imgType=passport";
                    htmlStr +=  "<div class='package_book_list'>"+
                        "<a href='/"+urlTag+"/" + encodeURIComponent(books[i].title).replace(/'/g,"&#39;").toLowerCase() + "/book?bookId=" + books[i].id + "&siteName=${session['entryController']}' class='d-block align-items-center'>\n" ;
                    if(books[i].coverImage !='') {
                        htmlStr +="<img style='width:80px;height:100px;margin-bottom:10px;' src='" + subImg + "'><span>" + books[i].title + "</span></a>\n";
                    }
                    else{
                        htmlStr +="<div class='uncover p-1' style='min-width:80px;height:100px;margin-bottom:10px;'>"+
                            "<p style='font-size: 9px;'>"+books[i].title+"</p>"+
                            "</div><span>"+ books[i].title +"</span></a>";
                    }
                    htmlStr +="</div>";
                    for (var l = 0; l < array.length; l++) {
                        //check packageid present in bookid and display package books
                        var item = books.find(item =>
                            item.id === array[l]
                        );
                        if(item != undefined){
                            if(item.coverImage !='') {
                            var subImg = "/funlearn/showProfileImage?id=" + item.id + "&fileName=" + item.coverImage + "&type=books&imgType=passport";
                        }

                        htmlStr +=  "<div class='package_book_list'>"+
                            "<a href='/"+urlTag+"/" + encodeURIComponent(item.title).replace(/'/g,"&#39;").toLowerCase() + "/book?bookId=" + item.id + "&siteName=${session['entryController']}' class='d-block align-items-center'>\n" ;
                        if(item.coverImage !='') {
                            htmlStr +="<img style='width:80px;height:100px;margin-bottom:10px;' src='" + subImg + "'><span>" + item.title + "</span></a>\n";
                        }
                        else{
                            htmlStr +="<div class='uncover p-1' style='min-width:80px;height:100px;margin-bottom:10px;'>"+
                                "<p style='font-size: 9px;'>"+item.title+"</p>"+
                                "</div><span>"+ item.title +"</span></a>";
                        }
                            htmlStr +="</div>";
                        }


                    }
                    htmlStr +="  </div>\n" +
                        "</div>"+

                    "</div>";

                }
                else {
                    htmlStr += "<a class='lib-showcase' href='/"+urlTag+"/" + encodeURIComponent(books[i].title).replace(/'/g,"&#39;").toLowerCase() + "/book?bookId=" + books[i].id + "&siteName=${session['entryController']}'>";
                htmlStr +="<div class='card'>" ;
                if (books[i].coverImage === null || books[i].coverImage == "null" || books[i].coverImage == "") {
                    htmlStr +="<div class='uncover'>"+
                    "<p>"+books[i].title+"</p>"+
                    "</div>";
                }
                else {
                    htmlStr += "<img src='" + imgSrc + "' alt=''/>" ;
                }
                htmlStr +="</a>"+
                    "<div class='card-body d-flex justify-content-between'>";
                if((books[i].packageBookIds != "" && books[i].packageBookIds != null && books[i].packageBookIds != "null")&&books[i].packageBookId == undefined) {
                    htmlStr +="<a class='card-text' href='#packageBooksLists"+ books[i].id +"' data-toggle='collapse' aria-expanded='false' aria-controls='packageBooksLists"+ books[i].id +"'>"
                        + books[i].title + "</a>" ;
                } else {
                    htmlStr +="<a class='card-text' href='/"+urlTag+"/" + encodeURIComponent(books[i].title).replace(/'/g,"&#39;").toLowerCase() + "/book?bookId=" + books[i].id + "&siteName=${session['entryController']}'>"
                        + books[i].title + "</a>" ;
                }
                <%if(("1".equals(""+session["siteId"]))||("25".equals(""+session["siteId"]))){%>
                if((books[i].price==0||books[i].price==0.0||books[i].price==undefined)&&books[i].packageBookId==undefined&&(''+books[i].batchName=='')) {
                    htmlStr += "<div class='dropup d-flex'>" +
                        "<button class='btn dropdown-toggle' data-toggle='dropdown'><i class='material-icons'>more_vert</i></button>" +
                        "<div class='dropdown-menu'>" +
                        "<a class='dropdown-item delete' href='javascript:removeFromLibrary(" + books[i].id + ")'>Delete</a>" +
                        "</div>" +
                        "</div>";
                }
                <%}%>
                htmlStr +=   "</div>" +
                    "</div>" +
                    "</div>";

            }
                <%}else {%>
                htmlStr += "<a class='lib-showcase' href='/"+urlTag+"/" + encodeURIComponent(books[i].title).replace(/'/g,"&#39;").toLowerCase() + "/book?bookId=" + books[i].id + "&siteName=${session['entryController']}'>";
                htmlStr +="<div class='card'>" ;
                if (books[i].coverImage === null || books[i].coverImage == "null" || books[i].coverImage == "") {
                    htmlStr +="<div class='uncover'>"+
                        "<p>"+books[i].title+"</p>"+
                        "</div>";
                }
                else {
                    htmlStr += "<img src='" + imgSrc + "' alt=''/>" ;
                }
                htmlStr +="</a>"+
                    "<div class='card-body d-flex justify-content-between'>";
                if((books[i].packageBookIds != "" && books[i].packageBookIds != null && books[i].packageBookIds != "null")&&books[i].packageBookId == undefined) {
                    htmlStr +="<a class='card-text' href='#packageBooksLists"+ books[i].id +"' data-toggle='collapse' aria-expanded='false' aria-controls='packageBooksLists"+ books[i].id +"'>"
                        + books[i].title + "</a>" ;
                } else {
                    htmlStr +="<a class='card-text' href='/"+urlTag+"/" + encodeURIComponent(books[i].title).replace(/'/g,"&#39;").toLowerCase() + "/book?bookId=" + books[i].id + "&siteName=${session['entryController']}'>"
                        + books[i].title + "</a>" ;
                }
                <%if("1".equals(""+session["siteId"])||("25".equals(""+session["siteId"]))){%>
                if((books[i].price==0||books[i].price==0.0||books[i].price==undefined)&&books[i].packageBookId==undefined&&(''+books[i].batchName=='')) {
                    htmlStr += "<div class='dropup d-flex'>" +
                        "<button class='btn dropdown-toggle' data-toggle='dropdown'><i class='material-icons'>more_vert</i></button>" +
                        "<div class='dropdown-menu'>" +
                        "<a class='dropdown-item delete' href='javascript:removeFromLibrary(" + books[i].id + ")'>Delete</a>" +
                        "</div>" +
                        "</div>";
                }
                <%}%>
                htmlStr +=   "</div>" +
                    "</div>" +
                    "</div>";
                <%}%>

        }

         }

        htmlStr +="</div>";
        if(noOfBooks==1) {
            totalUserBooks = "Book";
        } else {
            totalUserBooks = "Books";
        }
        if(siteId==24){
            //for ebouquet
            document.getElementById('total-books-of-user').innerHTML = (pg_start+1) + "-" +pg_end+" of "+books.length+" "+ totalUserBooks;
            if(siteId==24){
                var htmlStr1 = "";
                htmlStr1 += "<nav class='row justify-content-center'>"+
                    "  <ul class='pagination'>"
                //total no of pages to be created with 6 books in each page
                var noOfPages = Math.ceil(books.length/6)

                //showing 10 pages at a time
                var pageNoStart=paginationIndex*10
                var pageNoEnd=((paginationIndex+1)*10)

                if(pageNoEnd>noOfPages){
                    pageNoEnd=noOfPages
                }
                //if page moved once showing back button
                if(paginationIndex>0){
                    htmlStr1+="    <li class='page-item'><a class='page-link' href='javascript:updatePaginationIndex(-1)'><<</a></li>"
                }

                //displaying page numbers
                for(var i=pageNoStart; i<pageNoEnd;i++){
                    var startindex = (i*6)
                    var endindex = ((i+1)*6)
                    if(endindex>books.length){
                        endindex=books.length
                    }
                    htmlStr1+="    <li class='page-item'><a id='nav"+(i+1)+"' class='page-link' href='javascript:displayBooksViaPagination("+startindex+", "+endindex+","+(i+1)+");'>"+(i+1)+"</a></li>"
                }

                //showing next button
                if(noOfPages>pageNoEnd){
                    htmlStr1+="    <li class='page-item'><a class='page-link' href='javascript:updatePaginationIndex(1)'>>></a></li>"
                }
                //all books button
                htmlStr1+="<li class='page-item'><a id='allbooksbtn' class='page-link' href='javascript:displayBooksViaPagination(0,"+books.length+",-1);'>See all books</a></li></ul>" +
                    "</nav>"
                document.getElementById('ebpagination').innerHTML = htmlStr1
                if(pg_no>0){
                    if($('#nav'+pg_no).length>0){

                        document.getElementById('nav'+pg_no).classList.add('hilight')
                    }
                }else{
                    document.getElementById('allbooksbtn').classList.add('hilight')
                }
            }
        }else{
            document.getElementById('total-books-of-user').innerHTML = noOfBooks + " " + totalUserBooks;
        }
        if(noOfBooks > 0) {
            document.getElementById("content-data-books").innerHTML=htmlStr;
            $('.loading-icon').addClass('hidden');
        } else {
            if(siteId == 25) {
                document.getElementById("content-data-books").innerHTML = "<div class='no-books-available'>" +
                    "<div class='no-book-wrapper'>" + "<img class='book-image img-responsive' src='${assetPath(src: 'wonderslate/img_desert.png')}' style='margin:0 auto;border:none;'>" + "</div>" + "<p class='text-center mt-2'>It's like a desert in here! No eBooks added yet,<br>Please contact your institute.</p>" +
                    "</div>";
            } else if(siteId == 34){
                document.getElementById("content-data-books").innerHTML = "<div class='no-books-available'>" +
                    "<div class='no-book-wrapper'>" + "<img class='book-image img-responsive' src='${assetPath(src: 'wonderslate/img_desert.png')}' style='margin:0 auto;border:none;'>" + "</div>" + "<p class='text-center mt-2'>It's like a desert in here! No books added yet,</p>" +
                    "</div>";
            }
            else {
                document.getElementById("content-data-books").innerHTML = "<div class='no-books-available'>" +
                    "<div class='no-book-wrapper'>" + "<img class='book-image img-responsive' src='${assetPath(src: 'wonderslate/img_desert.png')}' style='margin:0 auto;border:none;'>" + "</div>" + "<p class='text-center mt-2'>It's like a desert in here! No eBooks added yet,</p>" +
                    "<a href='/${session['entryController']}/store?mode=browse' class='click-here-link d-block text-center'>Browse books!</a>" +
                    "</div>";
            }

            $('.loading-icon').addClass('hidden');
            $('#show-more').hide();
            $('body').css({
                'position' : 'relative'
            });
        }
        var fronts = document.querySelectorAll(".uncover");
        for(var i=0 ; i < fronts.length; i++) {
            fronts[i].style.background = colors[i%11];
        }

        //hide packageid book which displaying sperately instead of under book in library.
        var arrayHide;
        for(var m=0;m<myArrs.length;m++){
            arrayHide = JSON.parse("[" + myArrs[m] + "]");
           for(var n=0;n<arrayHide.length;n++){
               var item = books.find(item =>
                   item.id === arrayHide[n]
               );
               if(item != undefined) $('#packbooks'+item.id).hide();
           }
        }



    }
    function updatePaginationIndex(val) {
        paginationIndex=paginationIndex+val
        displayBooks(booksData)
    }
    getBooksList();
    var tabChanged=false

    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
        if(!tabChanged){
            <%if("true".equals(""+instructor)){%>
            getInstructorAssignments();
            <%}else{%>
            getStudentAssignments();
            <%}%>
        }
     tabChanged=true
    });
</script>
<script>


    function replaceAll(str, find, replace) {
        return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
    }
    function escapeRegExp(str) {
        return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
    }

    function SortByLastRead(x,y) {
        return ((x.sortNo == y.sortNo) ? 0 : ((x.sortNo > y.sortNo) ? 1 : -1 ));
    }
    function SortByTitle(x,y) {
        return ((x.title == y.title) ? 0 : ((x.title > y.title) ? 1 : -1 ));
    }
  var deleteBookId
    function removeFromLibrary(bookId){
        deleteBookId = bookId;
        $('#deleteBook').modal('show');

    }

    function bookDelete(){
        <g:remoteFunction controller="log" action="removeBookFromLibrary" params="'bookId='+deleteBookId" onSuccess = "bookRemoved(data);"/>
    }

    function bookRemoved(data) {
        if (data.status == "Deleted") {
            $('#deleteBook').modal('hide');
            $('.loading-icon').removeClass('hidden');
           getBooksList();
        }

    }

    $("#libraryExpiredModal").on('hidden.bs.modal', function () {
        window.location = "/ebouquet/contact";
    });


    function packageToggle(bookid){
        $('.collapse').collapse('hide');
        $("#packageBooksLists"+bookid).collapse('toggle');
    }
</script>
<asset:javascript src="mcq.js"/>
<!-- Hotjar Tracking Code for www.wonderslate.com -->
<script>
    (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:1340955,hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
</script>
</body>
</html>

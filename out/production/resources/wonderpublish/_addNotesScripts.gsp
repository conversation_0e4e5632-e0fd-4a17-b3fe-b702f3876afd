<script src="https://cdn.ckeditor.com/4.7.1/full-all/ckeditor.js"></script>
<script>

    var mode = "create";
    var resourceType = "Notes";
    var chapterId = previousChapterId;
    var bookId = "${bookId}";
    var resourceDtlId,htmlId;
    var page="notes";

    function formCancelNotes() {

            $('#addNotes').hide();
            if(notesPresent) $("#content-data-userNotes").show();
            else {
                $('#content-data-no-notes').show();
            }


    }

    function formAddNotes() {
        document.addhtml.notes.value=CKEDITOR.instances.notes.getData();

        if(validate()) {
            var batchIds="";

            document.addhtml.mode.value=mode;
            document.addhtml.resourceType.value=resourceType;
            document.addhtml.chapterId.value=previousChapterId;
            document.addhtml.bookId.value=bookId;
            document.addhtml.resourceDtlId.value=resourceDtlId;
            document.addhtml.page.value="notes";
            document.addhtml.batchIds.value=batchIds;
            document.addhtml.htmlId.value='<%="edit".equals(params.mode)?params.id:session['htmlId']%>';
            document.addhtml.submit();
        }
    }

    var flds =  new Array (
        'resourceNameUserAdd',
        'notes'
    );

    function validate(){
        var allFilled=true;
        $('.alert').hide();

        for (i=0; i<flds.length; i++) {
          console.log($("#"+flds[i]).val()+"<--"+$("#"+flds[i]).attr('name'));
            if(!$("#"+flds[i]).val()) {
                $("#"+flds[i]).addClass('has-error');
                $("#"+flds[i]).closest('.form-group').addClass('has-error');
                allFilled = false;
            } else {
                $("#"+flds[i]).removeClass('has-error');
                $("#"+flds[i]).closest('.form-group').removeClass('has-error');
            }
        }

        if(!allFilled){
            $('.alert').show();
        }

        return allFilled;
    }


    function showNotesEditor(){
        var newChapterId=previousChapterId;
         var hEd = CKEDITOR.instances['notes'];
        if (hEd) {
            hEd.destroy();
        }
        CKEDITOR.replace( 'notes', {
            height: 400,
            customConfig: '/assets/ckeditor/customConfig.js',
            extraPlugins: 'mathjax,uploadimage,image2,font,colorbutton,colordialog',
            mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML',

            // Upload images to a CKFinder connector (note that the response type is set to JSON).
            uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+newChapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,

            // Configure your file manager integration. This example uses CKFinder 3 for PHP.
            filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+newChapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
            filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+newChapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,

            // The following options are not necessary and are used here for presentation purposes only.
            // They configure the Styles drop-down list and widgets to use classes.

            stylesSet: [
                { name: 'Narrow image', type: 'widget', widget: 'image', attributes: { 'class': 'image-narrow' } },
                { name: 'Wide image', type: 'widget', widget: 'image', attributes: { 'class': 'image-wide' } }
            ],

            // Load the default contents.css file plus customizations for this sample.
            contentsCss: [ CKEDITOR.basePath + 'contents.css', 'assets/css/widgetstyles.css','http://fonts.googleapis.com/css?family=Merriweather'],
            //contentsCss: [ CKEDITOR.basePath + 'contents.css', 'assets/css/widgetstyles.css','http://fonts.googleapis.com/css?family=Merriweather','http://fonts.googleapis.com/css?family=MerriweatherSans'],


            // Configure the Enhanced Image plugin to use classes instead of styles and to disable the
            // resizer (because image size is controlled by widget styles or the image takes maximum
            // 100% of the editor width).
            image2_alignClasses: [ 'image-align-left', 'image-align-center', 'image-align-right' ],
        });

        if ( CKEDITOR.env.ie && CKEDITOR.env.version == 8 ) {
            document.getElementById( 'ie8-warning' ).className = 'tip alert';
        }
        CKEDITOR.instances.notes.setData("");
        document.addhtml.resourceName.value="";
        $('#content-data-no-notes').hide();
        $('#content-data-userNotes').hide();
        $('#addNotes').show();

    }

    function uploadNotes(){
        $("#notesUploadAlert").hide(500);
        if(document.resource3Form.resourceName.value==""){
            $("#notesUploadAlert").show(500);
        }
        else {
            document.resource3Form.bookId.value = bookId;
            document.resource3Form.chapterId.value = previousChapterId;
            $("#booksaving").show(500);
            document.resource3Form.submit();
        }
    }

</script>

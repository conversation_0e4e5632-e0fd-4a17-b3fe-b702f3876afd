<div class="modal fade book-review-modal" id="write-book-review-modal" data-backdrop="static" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel">
  <div class="modal-dialog modal-sm" role="document">
    <div class="modal-content col-md-12 col-sm-12 col-xs-12" style="min-height: 387px;">
      <div id="enter-review-div" class="col-md-12 col-sm-12 col-xs-12" style="padding: 0;">
        <p class="book-review-modal-header">Rate and review this smart ebook</p>
        <fieldset class="rating" id="book-rating">
          <input type="radio" id="star5" name="rating" value="5" /><label class = "full" for="star5"></label>
          <input type="radio" id="star4" name="rating" value="4" /><label class = "full" for="star4"></label>
          <input type="radio" id="star3" name="rating" value="3" /><label class = "full" for="star3"></label>
          <input type="radio" id="star2" name="rating" value="2" /><label class = "full" for="star2"></label>
          <input type="radio" id="star1" name="rating" value="1" /><label class = "full" for="star1"></label>
        </fieldset>
        <div class="book-review-input-fields" id="reviewinput">
          <textarea class="book-review-input" maxlength="2000" id="review" placeholder="Tell us what you think (Optional)" rows="8" cols="5"></textarea>
        </div>

        <div class="modal-footer change-password-modal-footer">
          <button type="button" class="btn pull-left cncl-btn waves-effect" data-dismiss="modal">Cancel</button>
          <button type="button" onclick="javascript:submitReview();" id="submit-review-btn" class="btn save-btn waves-effect disabled">Submit</button>
        </div>
      </div>
      <div id="rating-success" class="col-md-12 col-sm-12 col-xs-12 password-change-success" style="padding: 0; display: none;">
        Thanks for the review 👏
      </div>
    </div>
  </div>
</div>



<script>
var reviewBookId;
$('#book-rating input').click(function() {
  $('#submit-review-btn').removeClass('disabled');
});

function showReviewModal(bookId) {
  reviewBookId = bookId;
  $('#write-book-review-modal').modal('show');
}

function submitReview(){
  var reviewField =  document.getElementById("review");
  var ratingField =  $("#book-rating input:checked");
  if(!$(ratingField).is(':checked')){
    $('#submit-review-btn').addClass('disabled');
  } else {
    <g:remoteFunction controller="wonderpublish" action="updateReviewRating" onSuccess='reviewsuccess(data);' params="'bookId='+reviewBookId+'&rating='+ratingField[0].value+'&review='+reviewField.value"/>
  }
}

function reviewsuccess(data){
  $('#rating-success').show();
    $('#enter-review-div').hide();
    setTimeout(function(){
      $('#write-book-review-modal').modal('hide');
    },1000);
    $('#review').val("");
    setTimeout(function(){
      $('#enter-review-div').css({
        'display' : 'block'
      });
      $('#rating-success').css({
        'display' : 'none'
      });
      $('#review').val("");
    },1500);
    $('.write-book-review-read').hide();
    $('.read-book-chapters').css({
      "min-height" : "calc(100vh - 265px)",
      "max-height" : "calc(100vh - 115px)"
    });
  }
</script>
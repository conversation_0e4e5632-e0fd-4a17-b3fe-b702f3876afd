<g:render template="/resources/security"></g:render>
<style>
    .videoTransformAppInApp #videoModal .modal-dialog{
        transform: translate(0px,0px) !important;
    }

</style>
<script>
    function populateVideoSection(cData) {
         var cStr = "";
        $('#content-data-url').hide();

        for (var i = 0; i < cData.length; ++i) {
            var showShare = false;
            var data = cData[i];

            //assumption is the createdbyuser items will come of the items created by the instructor himself and not anybody else
            if (instructor && data.sharing == "createdbyuser") {
                showShare = true;
            }
            if (instructor && data.sharing == null && instructorControlledBook) {
                showShare = true;
            }
            if("audio"==data.quizMode) {
                cStr += "     <div class=''><a href='javascript:playAudio("+ data.id + ")'><div class='row wpboxifyvideoinsidebox'><div class='col-md-12 text-center' ><h4 class='whitetext'><i class='fa fa-music fa-x'></i>&nbsp;&nbsp;Play Audio </h4></div></div></a></div>" +
                    "   </div>" +
                    "  </div>";

            }
            else if("video"==data.quizMode) {
                cStr += "     <div class=''><a href='javascript:playS3Video("+data.id+",\""+ data.link + "\")'><div class='row wpboxifyvideoinsidebox'><div class='col-md-12 text-center' ><h4 class='whitetext'><i class='fa fa-music fa-x'></i>&nbsp;&nbsp;Watch Video </h4></div></div></a></div>" +
                    "   </div>" +
                    "  </div>";

            }
            else{
                if(siteId == 9) {
                    imgSrc = "/assets/sage-video-bg.png";
                } else {
                    imgSrc = "https://i.ytimg.com/vi/"+data.link+"/mqdefault.jpg";
                }

                var playBtn = "/assets/play-btn.png";
                videoDiv.innerHTML += "";
                // if(videoAvailableInApp) videoDiv.innerHTML +="<div class=''><b>Videos part of this book are available in app only</b></div></div><div class='row'>";
                if(showShare) {
                    videoDiv.innerHTML += "<div class='video-wrapper card mt-4'>" +
                        "<a href='javascript:playVideo(\"" + data.link + "\"," + data.id + ")' class=''>" +
                            "<div class='video-item'>" +
                                "<div class='video-img-wrapper'>" +
                                    "<img src='"+imgSrc+"' class='video-img' alt=''/>" +
                                    "<div class='play-btn-wrapper'>" +
                                         "<i class='material-icons'>play_arrow</i> "+
                                     "</div>" +
                                "</div>" +
                            "</div>"+"</a>"+
                                "<div class='video-info'>" +
                                       "<div class='d-flex justify-content-between align-items-center'>"+
                                        "<p class='video-name'>"+data.title+"</p>"+
                                    "<div class=\"dropdown\"> "+
                                        "<a class=\"dropdown-toggle\" id=\"android-menu\" data-toggle=\"dropdown\" aria-haspopup=\"true\" aria-expanded=\"true\" style=\"cursor:pointer;\">" +
                                             "<i class=\"material-icons\">more_vert</i></a>" +
                                        "<div class=\"dropdown-menu\" aria-labelledby=\"android-menu\"><a href=\"javascript:showShareOptions(" + data.id + ",false);\" class='dropdown-item'>Share</a> " +
                                        "</div> " +
                                    "</div>"+"</div>"+
                                "</div>" +
                        "</div>";
                }else{
                    videoDiv.innerHTML += "<div class='video-wrapper mt-4 card'>" +
                        "<a href='javascript:playVideo(\"" + data.link + "\"," + data.id + ")' class=''>" +
                                "<div class='video-item'>" +
                                    "<div class='video-img-wrapper'>" +
                                        "<img src='"+imgSrc+"' class='video-img' alt=''/>" +
                                        "<div class='play-btn-wrapper'>" +
                                        "<i class='material-icons'>play_arrow</i> "+
                                        "</div>" +
                                    "</div>" +
                        "</div>"+"</a>"+
                                    "<div class='video-info'>" +
                                        "<p class='video-name'>"+data.title+"</p>"+
                                    "</div>" +
                        "</div>";
                }

            }
        }


    }

    function playAudio(id){
        document.getElementById("audioid").href="/funlearn/getMP3?id="+id;
        $("#audionotes").toggle("500");
    }

    function playVideo(videoLink,id){
        if(videoLink.includes("/")){
            var videoSRC=videoLink.replace(/#/g,":");
        }else{
            var videoSRC="https://www.youtube.com/embed/"+videoLink;
        }
        if(siteId !=25) {
            $("#videoModal" + ' iframe').attr('src', videoSRC);
            $("#videoModal").modal('show');
        }
        else {
            $("#htmlreadingcontent").html('<button class="btn btn-back d-flex align-items-center mt-2 mb-2" onclick="backToMain()"><i class="material-icons" style="font-size:16px;">arrow_back</i> Back</button>' +
                '<iframe src=' + videoSRC + ' width="100%" height="650px" rel="0" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"></iframe>');
            $("#content-data-all").hide();
            $('#htmlreadingcontent').show();
            if($(window).width() <= 768) {
                $('#htmlreadingcontent > iframe').css('height','100%');
            }
        }
        $("#youtubeclose").click(function () {
            $("#videoModal" + ' iframe').attr('src', '');
        });
        initCallGoogle(id,"Videos");
        if(loggedInUser){
            if(allTabMode)
                updateUserView(id,"all","videos");
            else
                updateUserView(id,"videos","videos");
        }
        else{
            if(allTabMode)
                updateView(id,"all","videos");
            else
                updateView(id,"videos","videos");
        }


    }

    var keyString ="";
    function playSmartVideo(videoLink,id){
        keyString = videoLink;
        <g:remoteFunction controller="funlearn" action="getVideoLink" params="'resId='+id+'&secureVideo='+videoLink" onSuccess='playingVideo(data)'/>
        initCallGoogle(id,"Videos");
        if(loggedInUser){

                updateUserView(id,"videos","videos");
        }
        else{

                updateView(id,"videos","videos");
        }


    }

    function playingVideo(data){
        var encryptedData = data.securityKey;
        var key = keyString;



        var plaintext =  aesUtil.decrypt(salt, iv, key, encryptedData);
        if(plaintext.includes("/")){
           //don't do anything
        }else{
             plaintext="https://www.youtube.com/embed/"+plaintext;
        }
        $("#htmlreadingcontent").html('<button class="btn btn-back d-flex align-items-center" onclick="backToMain()"><i class="material-icons" style="font-size:16px;">arrow_back</i> Back</button>' +
            '<iframe scr=' + data.linkSrc + ' width="100%" height="650px" rel="0" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" src='+plaintext+'></iframe>');
        $("#content-data-all").hide();
        $('#htmlreadingcontent').show();
        if($(window).width() <= 768) {

            $('#htmlreadingcontent > iframe').css('height','100%');
        }

    }



    function openVideos() {
        <sec:ifNotLoggedIn>
        loginOpen();
        </sec:ifNotLoggedIn>
        <sec:ifLoggedIn>

        $('#addVideo').modal('show');
        </sec:ifLoggedIn>
    }

    function playS3Video(id,link){
        player.src("https://d3fmfmagr5dtdh.cloudfront.net/Sample.mp4");
        getVideoPolls(id);
        $("#hostedvideoModal").modal('show');
    }
    function backToMain(){
        $("#htmlreadingcontent").html("<iframe src=''></iframe>");
        $('#content-data-all').show();
    }
</script>

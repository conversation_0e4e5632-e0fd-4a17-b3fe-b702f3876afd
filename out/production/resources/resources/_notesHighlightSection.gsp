<script>
    var wonderslateSite = true;
</script>
<%if(!"books".equals(session['siteController'])){%>
<script>
wonderslateSite = false;
</script>
<%}%>
<asset:javascript src="wonderslate/annotator-full.js"/>
<asset:javascript src="wonderslate/annotator.touch.js"/>
<style>
.annotator-gl {
    background-color: rgba(195, 183, 215, 0.83);
}
#htmlContent .annotator-gl {
    background-color: rgba(195, 183, 215, 0.83);
}

#print-notes{
    padding: 0!important;
}
.mynotes h2{
    font-size: 16px !important;
}
.mynotes ul{
    list-style: none;
}
.mynotes ul li {
    padding: 0px 14px;
    margin-left: 1rem;
    border-left: 3px solid;
    margin-bottom: 11px;
}
.mynotes ul li:hover{
    background: #f3e5f5;
    border-radius: 10px;
    box-shadow: 0 2px 2px rgba(0,0,0,0.1);
}
#allnotes-content .mynotes h3{
    margin-left: 0 !important;
}
.noteTag{
    color: rgba(0,0,0,0.3);
}
.tagwrapper{
    margin-bottom: 5px;
}
.yellowBorder{
    border-left-color:  #6dd53d !important;
}
.greenBorder{
    border-left-color: #D3E6D1 !important;
}
.blueBorder{
    border-left-color:#969fe5 !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px){
    .mynotes ul {
        padding: 0 !important;
    }
}
</style>

<style>
.modal-dialog-centered {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    justify-content: center;
}
.modal.show .modal-dialog {
    -webkit-transform: none;
    transform: none;
}
.modal.fade .modal-dialog {
    transition: -webkit-transform .3s ease-out;
    transition: transform .3s ease-out;
    transition: transform .3s ease-out,-webkit-transform .3s ease-out;
    -webkit-transform: translate(0,-50px);
    transform: translate(0,-50px);
}
.modal-content{
    width: 100%;
}

@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
</style>

<script type="text/javascript">
    function googleTranslateElementInit() {
        new google.translate.TranslateElement({pageLanguage: 'en'}, 'google_translate_element');
    }
</script>

<script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
<div>
    <div class="modal" id="addToFlashCard"  data-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-md">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Add as flashcard</h4>
                </div>
                <div class="modal-body">
                    <form>
                        <label>Select Card</label>
                        <select id="flashCardsList" style="display: none" class="form-control mb-2"></select>
                        <div id="flashCardsTitleDiv">
                            <input type="text" id="modalFlashCardTitle" name="modalFlashCardTitle" class="form-control" placeholder="Flashcards Set title">
                        </div>

                        <div class="mt-1">
                            <div class="revision-question">
                                <label>Front</label>
                                 <textarea id="modalterm" class="form-control mb-2" name="modalterm" maxlength="500" placeholder="Front"></textarea>
                            </div>
                            <div class="revision-answer">
                                <label>Back</label>
                                    <textarea id="modaldefinition" class="form-control mb-2" name="modaldefinition" maxlength="500"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="alert-thin alert alert-warning col-sm-12 text-left red" style="display: none;" id="addFlashcardAlert">
                    ** Enter the folder name
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" onclick="closeAddFlashCardModal()">CANCEL</button>
                    <button type="button" class="btn btn-flashcard " onclick="addFlashCardNow();">ADD</button>
                </div>
            </div>
        </div>
    </div>
</div>


<script type="text/javascript">
    var flashCardsPresent = false;
    function SortByTitle(x,y) {
        return ((x.resName == y.resName) ? 0 : ((x.resName > y.resName) ? 1 : -1 ));
    }

    function closeAddFlashCardModal(){
        $('#addToFlashCard').modal('hide');
    }

    function addFlashCardNow(){
        var noOfItems=1;
        var studySetResId=-1;
        if(flashCardsPresent&&document.getElementById("flashCardsList").selectedIndex==0){
            document.getElementById("flashCardsList").focus();
        }
        else if(!flashCardsPresent&&document.getElementById("modalFlashCardTitle").value==""){
            document.getElementById("modalFlashCardTitle").focus();
        } else if(document.getElementById("modalterm").value=="") {
            document.getElementById("modalterm").focus();
        } else if(document.getElementById("modaldefinition").value=="") {
            document.getElementById("modaldefinition").focus();
        } else {
            if(flashCardsPresent) studySetResId = document.getElementById("flashCardsList")[document.getElementById("flashCardsList").selectedIndex].value;
            var title=document.getElementById("modalFlashCardTitle").value;
            //  $('.loading-icon').removeClass('hidden');
            var params = "noOfItems="+noOfItems+"&chapterId="+previousChapterId+"&studySetResId="+studySetResId+"&folderId=${params.folderId}&title="+title;
            for (i = 0; i < noOfItems;i++) {
                params += "&term"+i+"="+document.getElementById("modalterm").value;
                params += "&definition"+i+"="+encodeURIComponent(document.getElementById("modaldefinition").value);
            }

            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="resources" action="addKeyValues" onSuccess='keyValuesSavedFromSelection(data);' params="params"></g:remoteFunction>
        }
    }

    function keyValuesSavedFromSelection(data){
       if(!flashCardsPresent && '${genericReader}' !='true') {
           chapterDetailsData.push({'id':data.resId,'resName':data.title,'resType':'KeyValues'});
           populateAllSection(chapterDetailsData);
           flashCards.push({'resId':data.resId,'resName':data.title});
       }
        flashCardsPresent = true;
        $('#addToFlashCard').modal('hide');
        document.getElementById("modalterm").value='';
        $('.loading-icon').addClass('hidden');
        $('#content-data-all').hide();

    }
    function addToFlashCard(selectedText){
        if(flashCardsPresent){
            flashCards.sort(SortByTitle);
            var select;
                select = document.getElementById("flashCardsList");
                select.options.length = 1;
                for(var i=0;i<flashCards.length;i++) {
                    var el = document.createElement("option");
                    el.textContent = flashCards[i].resName;
                    el.value = flashCards[i].resId;
                    select.appendChild(el);
                }

            $("#flashCardsTitleDiv").hide();
           $("#flashCardsList").show();


        }
        document.getElementById("modaldefinition").value=selectedText;
        $('#addToFlashCard').modal('show');
      //  alert("Selected text is "+selectedText);

    }

    function openModal(username){
        oldUsername = username
        $('#removePhone').modal('show');

    }
    var annotation;
    var resIdVal;
    var annotationColorVal;

    Annotator.Plugin.StoreLogger = function (element) {
        return {
            pluginInit: function () {
                this.annotator
                    .subscribe("annotationCreated", function (annotation) {
                        setTimeout(function(){
                            %{--<g:remoteFunction controller="wonderpublish" action="annotateSearch"  onSuccess='renderCreatedNotes(data);' params="'uri='+resIdVal+'&bookId=${params.bookId}'" />--}%
                        }, 100);
                    })
                    .subscribe("annotationUpdated", function (annotation) {
                        setTimeout(function(){
                            %{--<g:remoteFunction controller="wonderpublish" action="annotateSearch"  onSuccess='renderCreatedNotes(data);' params="'uri='+resIdVal+'&bookId=${params.bookId}'" />--}%
                        }, 100);
                    })
                    .subscribe("annotationDeleted", function (annotation) {
                        setTimeout(function(){
                            %{--<g:remoteFunction controller="wonderpublish" action="annotateSearch"  onSuccess='renderCreatedNotes(data);' params="'uri='+resIdVal+'&bookId=${params.bookId}'" />--}%
                        }, 100);
                    });
            }
        }
    };

    function loadAnnotator(resId) {
            var bookId="";
           <% if(params.bookId == null || params.bookId == '') { %>
            bookId=urlBookId;
           <% } else {%>
            bookId="${params.bookId}";
            <%}%>

        if(annotation !== undefined) {
            annotation.annotator('destroy');
        }

        annotation = $('#htmlreadingcontent').annotator();
        resIdVal = resId;

        annotation.annotator('addPlugin', 'Store', {
            // The endpoint of the store on your server.
            prefix: serverPath+'/wonderpublish',

            // Attach the uri of the current page to all annotations to allow search.
            annotationData: {
                'uri': resId,
                'bookId' : bookId
            },

            urls: {
                // These are the default URLs.
                create:  '/annotateSave',
                update:  '/annotateUpdate/:id',
                destroy: '/annotateDestroy/:id',
                search:  '/annotateSearch'
            },

            // This will perform a "search" action when the plugin loads. Will
            // request the last 20 annotations for the current url.
            // eg. /store/endpoint/search?limit=20&uri=http://this/document/only
            loadFromSearch: {
                'limit': 100,
                'all_fields': 1,
                'uri': resId,
                'bookId':bookId
            },

            showViewPermissionsCheckbox: true,
            showEditPermissionsCheckbox: true
        });

        annotation.annotator('addPlugin', 'Tags');
        annotation.annotator('addPlugin', 'StoreLogger');
        annotation.annotator().annotator("addPlugin", "Touch");
    }

    function findPos(obj) {
        var curtop = 0;
        if (obj.offsetParent) {
            do {
                curtop += obj.offsetTop;
            } while (obj = obj.offsetParent);
            return [curtop];
        }
    }

    var notes;
    var notesToExport;

    
    function viewAllNotes(){
        var bookId="";
        <% if(params.bookId == null || params.bookId == '') { %>
        bookId=urlBookId;
        <% } else {%>
        bookId="${params.bookId}";
        <%}%>
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="wonderpublish" action="getAnnotationsForFullBook"  onSuccess='renderCreatedNotes(data);' params="'bookId='+bookId" />
    }

    function sortByKey(array, key) {
        return array.sort(function(a, b) {
            var x = a[key]; var y = b[key];
            return ((x < y) ? -1 : ((x > y) ? 1 : 0));
        });
    }
    function renderCreatedNotes(data) {
        $('.loading-icon').addClass('hidden');
        var userNotes = document.getElementById('allnotes-content');
        userNotes.innerHTML='<div id="print-all-action" class="w-100 mt-4 px-4 col-lg-9">\n' +
            '                            <div class=\'d-flex align-items-center justify-content-between\'>\n' +
            '                               <div class=\'icon material-icons\' id="backfromAllnotes" onclick="backToAll()">keyboard_backspace</div>\n' +
            "<div class=\"mdl-tooltip\" data-mdl-for=\"backfromAllnotes\">\n" +
            "Back to <strong>Main</strong>\n" +
            "</div>"+
            '                            </div>\n' +
            '                        </div>';
        var quotes=data.rows;
        //console.log(quotes);
        sortByKey(quotes,'chapterId');
         var chap,listElements,a,chapters={},list,h3,div,mainElement,comment,h2,span;
         var tag,pageNumber;


        mainElement = document.createElement('div');
        mainElement.className+='print-notes col-lg-6';
        mainElement.setAttribute('id','print-notes');
        if(quotes.length!=0) {
            for (var i = 0; i < quotes.length; i++) {
                chap = quotes[i].chapterName;
                listElements = document.createElement('li');
                var hcolor = quotes[i].annotateColor;
                if (hcolor == 'annotateColorYellow'){
                    hcolor = 'yellowBorder'
                }else if(hcolor=='annotateColorGreen'){
                    hcolor = 'greenBorder'
                }else if(hcolor == 'annotateColorBlue'){
                    hcolor = 'blueBorder'
                }
                listElements.classList.add(hcolor)
                if (quotes[i].text != null) {
                    tag = document.createElement('p');
                    tag.className = 'tagwrapper';
                    tag.innerHTML = "<span class='noteTag'>Note</span></span>";
                    listElements.appendChild(tag);
                    comment=quotes[i].text;
                     h2=document.createElement('h2');
                     span=document.createElement('span');
                    span.appendChild(document.createTextNode(comment));
                    h2.appendChild(span);
                    listElements.appendChild(h2);
                   // h2.appendChild(document.createTextNode('ssss'));
                   //  span.className += "comment-bg";
                }else{
                    tag = document.createElement('p');
                    tag.className = 'tagwrapper';
                    tag.innerHTML = "<span class='noteTag'>Highlight</span></span>";
                    listElements.appendChild(tag);
                }

                a = document.createElement('a');
                a.appendChild(document.createTextNode(quotes[i].quote));
                if (chap in chapters) {
                    list = chapters[chap];
                    list.appendChild(listElements).appendChild(a);
                } else {
                    div = document.createElement('div');
                    div.className += "mynotes";
                    h3 = document.createElement('h3');
                    list = document.createElement('ul');
                    list.className += 'notes-by-user';
                    chapters[chap] = list;
                    div.appendChild(h3);
                    h3.appendChild(document.createTextNode(chap));
                    userNotes.appendChild(mainElement).appendChild(div).appendChild(list).appendChild(listElements).appendChild(a);
                }
            }
        }
        else{
            userNotes.innerHTML="<div class='text-center col-lg-9'>" +
                '                            <div class=\'d-flex align-items-center justify-content-between mt-3\'>\n' +
                '                               <div class=\'icon material-icons\' id="backfromAllnotes" onclick="backToAll()">keyboard_backspace</div>\n' +
                "<div class=\"mdl-tooltip\" data-mdl-for=\"backfromAllnotes\">\n" +
                "Back to <strong>Main</strong>\n" +
                "</div></div>"+
                    "<img src=\"${assetPath(src: 'ws/boxempty.svg')}\"  width='125px' height='117px'>"+
                "<p>Your All Highlights is empty.<br>Please add your Highlights from chapters.</p>"+
                "</div>";
                    //$('#print-all-action .print').attr('disabled','disabled');
        }
        garabageClearAll() ;
        window.componentHandler.upgradeDom();


    }
    function garabageClearAll() {
        if (bookLang != "English" && bookLang !="") {
            $('#print-notes ul li').addClass('d-none');
            $('#print-notes ul li:has("h2")').removeClass('d-none').addClass('d-block');
            var clearGarbageAll = document.querySelectorAll("#print-notes li a");

            for (var i = 0; i < clearGarbageAll.length; i++) {
                clearGarbageAll[i].style.display = "none";
            }
        }
    }


    function closeNotes() {
        $('#overlay').addClass('d-none');
        $('.export-notes').addClass('d-none');
        $('#notesMenu').removeClass('active');
    }



</script>

<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<asset:stylesheet href="jquery.simple-dtpicker.css"/><asset:stylesheet href="jquery.simple-dtpicker.css"/>
<link href="https://cdn.datatables.net/1.10.20/css/dataTables.bootstrap4.min.css" type="text/css" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<script>
    var loggedIn=false;
</script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.8.0/jszip.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.8.0/xlsx.js"></script>
<% if(wsSite){%>
<style>
table.dataTable th, table.dataTable td {
table.dataTable th, table.dataTable td {
    font-family: 'Poppins', sans-serif !important;
    font-size: 13px;
}
table.dataTable thead th {
    font-weight: 500;
}
table.dataTable td button {
    font-size: 13px;
}
table.dataTable thead th:first-child span {
    font-size: 13px;
    font-weight: 500;
}
</style>
<%}%>
<style>
#addBooksToUserModal {
    z-index: 9992;
}
#addBooksToUserModal h5 {
    font-family: 'Poppins', sans-serif !important;
}
.modal-dialog-centered {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    justify-content: center;
}
.modal.show .modal-dialog {
    -webkit-transform: none;
    transform: none;
}
.modal.fade .modal-dialog {
    transition: -webkit-transform .3s ease-out;
    transition: transform .3s ease-out;
    transition: transform .3s ease-out,-webkit-transform .3s ease-out;
    -webkit-transform: translate(0,-50px);
    transform: translate(0,-50px);
}
.modal-content{
    width: 100%;
}
@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}

.page-item.active .page-link {
    color: #ffffff !important;
}
.page-item.disabled .page-link {
    color: #6c757d !important;
    cursor: auto !important;
}
.pagination .page-link:not(:disabled):not(.disabled) {
    cursor: pointer !important;
}
.pagination .page-link {
    padding: .5rem .75rem !important;
    margin-left: -1px !important;
    color: #007bff !important;
    /*background-color: #fff;*/
    border: 1px solid #dee2e6;
    font-size: 14px !important;
}
table.dataTable th, table.dataTable td {
    white-space: normal !important;
}
#batchUsers tbody {
    display: table-row-group;
}
table.dataTable thead th:first-child {
    width:10% !important;
}
/*table.dataTable thead th:nth-child(2) {
    width:15% !important;
}*/
table.dataTable thead th:nth-child(3) {
    width:45% !important;
}
/*table.dataTable thead th:nth-child(4) {
    width:20% !important;
}*/
.addBooks-inner {
    display:flex;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>


<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-10 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <label for="name" style="display: block;">
                        <%if(session["siteId"].intValue()==24){%>
                        Add Organization/Contact person details
                        <%}else{%>
                        Add Institute/Library
                        <%}%>
                    </label> <br>
                    <div class="flex_st d-flex ">
                        <div class="col-md-5 lib ">
                            <input type="text" class="form-control admin" name="insname" id="insname"  placeholder="Add Institute name">
                        </div>
                        <div class="col-md-5">
                            <input type="text" class="form-control admin" name="contactName" id="contactName" placeholder="Add Librarian Name" >
                        </div>
                    </div>
                    <br>
                    <div class="flex_st d-flex" >
                        <div class="col-md-5">
                            <input type="text" class="form-control admin" name="contactEmail" id="contactEmail" placeholder="Add Librarian Email" >
                        </div>
                        <% if(session["siteId"].intValue()==12||session["siteId"].intValue()==23){%>
                        <div class="col-md-5 lib2">
                            &nbsp;&nbsp;IP Restricted&nbsp;&nbsp;Yes&nbsp;<input type="radio" id="yes" name="ipRestricted" value="true" checked>&nbsp;&nbsp;No&nbsp;<input id="no" type="radio" name="ipRestricted" value="false">
                        </div>
                        <%}else{ %>
                        <div class="col-md-5 lib2">
                            &nbsp;&nbsp;IP Restricted&nbsp;&nbsp;Yes&nbsp;<input type="radio" id="yes" name="ipRestricted" value="true" >&nbsp;&nbsp;No&nbsp;<input id="no" type="radio" name="ipRestricted" value="false" checked>
                        </div>
                        <% } %>
                    </div>
                    <br>
                    <br>
                    <div class="flex_st d-flex">
                        <div class="col-md-5">
                            <input type="text" class="form-control admin" id="startDate" name="startDate" placeholder="Start Date" size="10" autocomplete="off">
                        </div>
                        <div class="col-md-5">
                            <input type="text" class="form-control admin" id="endDate" name="endDate" placeholder="Completion Date" size="10" autocomplete="off">
                        </div>
                    </div>
                    <br>
                    <br>
                    <div class="flex_st d-flex">
                        <div class="col-md-5">
                        <input type="number" min="0" class="input-field input-field-login mobile-input form-control admin" id="noofusers" name="noofusers" placeholder="No. of licenses"  autocomplete="off" onkeypress="return onlyNumberKey(event)">
                        </div>
                        <% if(wsSite){%>
                        <div class="col-md-5">
                            <div class="row m-0 p-0 align-items-center justify-content-start">
                                <input type="number" min="1" class="form-control admin" id="checkOutDays" name="checkOutDays" placeholder="Check out days"  autocomplete="off" onkeypress="return onlyNumberKey(event)">
                                <i style="font-size:24px; cursor: pointer" class="fa p-2" onclick="alert('Number of days the user can borrow the library eBook.\nIf check out days is empty, by default check out days is considered as 14 days.')">&#xf05a;</i>
                            </div>
                        </div>
                        <%}%>
                    </div>
                    <% if(wsSite){%>
                    <br><br>
                    <div class="flex_st d-flex">
                    <div class="col-md-5 lib2">
                        &nbsp;&nbsp;Show full library to users&nbsp;&nbsp;Yes&nbsp;<input type="radio" id="fullViewYes" name="fullLibraryView" value="true" checked>&nbsp;&nbsp;No&nbsp;<input  id="fullViewNo" type="radio" name="fullLibraryView" value="false">
                    </div>
                </div>
                    <br>
                    <div class="flex_st d-flex">
                        <div class="col-md-5 lib2">
                            &nbsp;&nbsp;Show paid & free eBooks &nbsp;&nbsp;Yes&nbsp;<input type="radio" id="paidFreeTabYes" name="paidFreeTab" value="true" >&nbsp;&nbsp;No&nbsp;<input  id="paidFreeTabNo" type="radio" name="paidFreeTab" value="false" checked>
                        </div>
                    </div>
                    <br>
                    <div class="flex_st d-flex">
                        <div class="col-md-5 lib2">
                            &nbsp;&nbsp;Institute Type &nbsp;&nbsp;EduWonder&nbsp;<input type="radio" id="eduWonderYes" name="eduWonder" value="true" >&nbsp;&nbsp;Digital Library&nbsp;<input  id="eduWonderNo" type="radio" name="eduWonder" value="false" checked>
                        </div>
                    </div>
                    <br>
                    <div class="flex_st d-flex">
                        <div class="col-md-5 lib2">
                            &nbsp;&nbsp;Enable Shop &nbsp;&nbsp;Yes&nbsp;<input type="radio" id="shopEnabledYes" name="shopEnabled" value="true" >&nbsp;&nbsp;No&nbsp;<input  id="shopEnabledNo" type="radio" name="shopEnabled" value="false" checked>
                        </div>
                    </div>
                    <br>
                    <div class="flex_st d-flex">
                        <div class="col-md-5 lib2">
                            &nbsp;&nbsp;Enable Leaderboard &nbsp;&nbsp;Yes&nbsp;<input type="radio" id="leaderBoardEnabledYes" name="leaderBoardEnabled" value="true" >&nbsp;&nbsp;No&nbsp;<input  id="leaderBoardEnabledNo" type="radio" name="leaderBoardEnabled" value="false" checked>
                        </div>
                    </div>
                    <br>
                    <%if(isDefaultBookSetter){%>

                    <div class="flex_st d-flex">
                        <div class="col-md-5 lib2">
                            &nbsp;&nbsp;Default template institute &nbsp;&nbsp;Yes&nbsp;<input type="radio" id="defaultBooksTemplateInstituteYes" name="defaultBooksTemplateInstitute" value="true" >&nbsp;&nbsp;No&nbsp;<input  id="defaultBooksTemplateInstituteNo" type="radio" name="defaultBooksTemplateInstitute" value="false" checked>
                        </div>
                    </div>
                    <br>
                    <%}%>
                    <div class="flex_st d-flex">
                        <div class="col-md-5 lib2">
                            &nbsp;&nbsp;Copy default books &nbsp;&nbsp;Yes&nbsp;<input type="radio" id="copyDefaultBooksYes" name="copyDefaultBooks" value="true" >&nbsp;&nbsp;No&nbsp;<input  id="copyDefaultBooksNo" type="radio" name="copyDefaultBooks" value="false" checked>
                        </div>
                    </div>
                    <br>
                    <div class="flex_st d-flex">
                        <div class="col-md-5 lib2">
                            &nbsp;&nbsp;Gone Live &nbsp;&nbsp;Yes&nbsp;<input type="radio" id="goneLiveYes" name="goneLive" value="true" >&nbsp;&nbsp;No&nbsp;<input  id="goneLiveNo" type="radio" name="goneLive" value="false" checked>
                        </div>
                    </div>
                    <br>
                    <div class="flex_st d-flex">
                        <div class="col-md-5 lib2">
                            &nbsp;&nbsp;Pre University College &nbsp;&nbsp;Yes&nbsp;<input type="radio" id="preUniversityYes" name="preUniversity" value="true" >&nbsp;&nbsp;No&nbsp;<input  id="preUniversityNo" type="radio" name="preUniversity" value="false" checked>
                        </div>
                    </div>
                    <br>

                    <br>
                    <div class="flex_st d-flex" id="categorySelection">
                        <div class="col-6 col-md-3">
                            <label>Select Level:</label>
                            <g:select id="createLevel" class="form-control admin" name="createLevel" from="${levelsMstList}" optionKey="name" optionValue="name"
                                      noSelection="['':'Select']" onchange="javascript:getCreateSyllabus(this.value)"/>
                            <div class="invalid-feedback" id="categoryTagsError">Please select category tags.</div>
                        </div>
                        <div id="categorySyllabus" class="col-6 col-md-3" style="display: none">
                            <label>Select Syllabus:</label>
                            <select id="createSyllabus" class="form-control admin selectpicker" name="createSyllabus" multiple><option>Select</option></select>
                        </div>

                    </div>
                    <%}%>
                    <div class="flex_st d-flex">
                        <div class="col-md-5 lib2" id="secretKey">
                        </div>
                    </div>
                    <br>

                        <button class="btn btn-primary ml-3" id="insEditBtn" onclick="addInstitute();">
                            Add Institute
                        </button>


                </div>
                <div id="errormsg1" class="alert alert-danger has-error" role="alert" style="display: none; background: none;"></div>
            </div>
        </div>
        <div class='col-md-10 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books1">
                <div class="form-group">
                    <label for="institutes" style="display: block;">
                        <%if(session["siteId"].intValue()==24){%>
                        Organization/Library Management
                        <%}else{%>
                        Institute/Library Management
                        <%}%>
                    </button>
                    </label>
                    <div class="flex_st d-flex">
                        <div class="col-md-5">
                            <select name="institutes" id="institutes" class="form-control admin" style=" " onchange="instituteChanged();">
                                <option value="" disabled="disabled" selected>
                                    <%if(session["siteId"].intValue()==24){%>
                                    Select Organization
                                    <%}else{%>
                                    Select Institute
                                    <%}%>
                                </option>
                                <g:each in="${institutes}" var="institute" status="i">
                                    <option value="${institute.id+'_'+institute.ipRestricted+'_'+institute.batchId}">${institute.name + (institute.endDate!=null?" - Completion date: "+(new SimpleDateFormat("dd-MM-yyyy")).format(institute.endDate):"")}</option>
                                </g:each>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select id="instituteAction" name="instituteAction" class="form-control admin" onchange="instituteAction();">
                                <option value="" disabled="disabled" selected>Select Action</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="button" id="downloadInsDtl" class="btn btn-primary " style="border-width: 0px;" >
                                <%if(session["siteId"].intValue()==24){%>
                                Download Organization  Details
                                <%}else{%>
                                Download Institute  Details
                                <%}%>
                            </button>
                        </div>
                        <%if((session["userdetails"].publisherId==null && (session['siteId'].intValue()==1))){%>
                        <div class="col-md-2">
                            <button type="button" id="manageInsDtl" class="btn btn-primary mt-2" style="border-width: 0px;" >Manage Institute Page</button>
                        </div>
                        <%}%>
                        <%if(wsSite){%>
                        <div class="col-md-2">
                            <button type="button" id="manageClasses" class="btn btn-primary mt-2" style="border-width: 0px;" >Manage Classes</button>
                        </div>
                        <div class="col-md-2">
                            <button type="button" id="manageInstructors" class="btn btn-primary mt-2" style="border-width: 0px;" >Manage Instructors</button>
                        </div>
                        <%}%>
                    </div>
                </div>
                %{--<div class="form-group">--}%

                %{--</div>--}%
                <div id="addBooks" style="display: none">
                    <div class="form-group col-md-5">
                        Select input type:&nbsp;&nbsp;&nbsp;
                        <input type="radio" value="true" name="bookIdMethod">&nbsp;BookId&nbsp;&nbsp;&nbsp;
                        <input type="radio" value="false" name="bookIdMethod">&nbsp;ISBNs
                    </div>
                <div class="addBooks-inner">
                    <div class="form-group col-md-5 ">
                        <textarea class="form-control admin" name="bookId" id="bookId"  placeholder="Comma seperated book ids, or isbns"></textarea><br>
                        <button class="btn btn-primary btadd" onclick="addBook();">Add Book/s</button>
                    </div>
                <% if(!sageSite && session["userdetails"].publisherId==null){%>
                <p style="margin-top: 1.5rem;margin-left: -35px;color: #444;">- or -</p>
                <div class="form-group col-md-3 mt-3">
                    <input type="hidden" name="mode" value="submit">
                    <input id="FileInputElementBooks" type="file" class="form-control w-100" name="FileInputElementBooks" accept=".xlsx" />
                </div>
                <div class="form-group col-md-3 mt-3">
                    <input type="hidden" name="ebookId" id="ebookId" value="ebookId">
                    <button type="button" onclick="javascript:uploadBooks()" class="btn btn-primary btn-lg">Upload Books</button>
                    </div>


                <%} %>
                </div>
                <% if(!sageSite && session["userdetails"].publisherId==null){%>
                <div class="addBooks-inner">
                <div class="col-6 col-md-3 col-lg-2 pr-2">
                    <select id="level" class="form-control admin" onchange="levelChanged(this);" ><option value="" selected="selected">Select Level</option>

                    </select>
                </div>
                <div class="col-6 col-md-3 col-lg-2 pr-2">
                    <select id="syllabus" class="form-control admin" onchange="syllabusChanged(this)"  style="display: none"><option value="" selected="selected">Select Board</option> </select>
                </div>
                <div class="col-6 col-md-3 col-lg-2 pr-2">
                    <select id="grade" class="form-control admin" onchange="gradeChanged(this)"  style="display: none"><option value="" selected="selected">Select Grade</option> </select>
                </div>
                <div class="col-6 col-md-3 col-lg-2 pr-2">
                    <select id="subject" class="form-control admin" style="display: none"><option value="" selected="selected">All Subjects</option> </select>
                </div>
                    <div class="col-6 col-md-3 col-lg-2 pr-2">
                        <button type="button" onclick="javascript:getFilteredBooks()" class="btn btn-primary" style="display: none" id="filteredBooks">Get Books</button>
                    </div>
                </div><br>
                <div class="col-6 col-md-3 col-lg-2 pr-2">
                    <select id="booksBatch" class="form-control admin" name="booksBatch"  style="display: none"><option value="" selected="selected">Select Class</option></select>
                </div>
                <%}%>
                </div>
                <div id="addInstituteAdmin" style="display: none">

                    <div class="form-group col-md-6 ">
                        <input type="text" class="form-control admin col-md-4" name="institiuteadminname" id="adminUsername"  placeholder="Enter user email/mobile no."></input>
                    </div>
                    <div class="form-group col-md-6 ">
                        <input type="text" class="form-control admin col-md-4" name="institiuteadminactualname" id="adminActualname"  placeholder="Admin name"></input>
                    </div>
                        <div class="form-group col-md-6 ">
                        <input type="text" class="form-control admin col-md-4" name="adminActualPassword" id="adminActualPassword"  placeholder="password"></input>
                        </div>
                    <div class="form-group col-md-6 ">
                      Library admin<input type="radio" class="form-control admin col-md-2" name="libraryAdmin" value="libraryAdmin" checked>
                        User uploader&nbsp;&nbsp;  <input type="radio" class="form-control admin col-md-2" name="libraryAdmin" value="userUploader">
                    </div>
                        <button class="btn btn-primary btadd" onclick="addInstituteAdmin();">Add</button>
                    </div>

                </div>
                <div id="addUserLoginLimit" style="display: none">

                    <div class="form-group col-md-6 ">
                        <input type="number" min="1" max="999" step="1" class="form-control admin col-md-8" name="userloginlimit" id="userloginlimit"  placeholder="Enter user login limit."></input><br>
                        <button class="btn btn-primary btadd" onclick="addUserLoginLimit();">Submit</button>
                    </div>
                </div>
                <% if(accessCode){%>
                <div id="addGenerateAccessCode" style="display: none">

                    <div class="form-group col-md-6 ">
                        <input type="number" min="1" max="999" step="1" class="form-control admin col-md-8" name="accesscodegen" id="accesscodegen"  placeholder="Enter number."></input><br>
                        <button class="btn btn-primary btadd" onclick="addGenerateAccessCode();">Submit</button>
                    </div>
                </div>
                <div id="viewAccessCodes" style="display: none">
                </div>
                <%} %>
                <div id="addUsers" style="display: none" class="">
                    <div class="form-group col-md-5 ">
                        <p id="total-lic"></p>
                        <p id="used-lic"></p>
                        <p id="pending-lic"></p>
                        <% if(wsSite){%>
                        <textarea class="form-control admin" name="useremail" id="useremail"  placeholder="Add users email or mobile (separated by comma for multiple users)" style="height: 100px;"></textarea><br>
                        <input type="text" class="form-control admin col-md-6" name="institutePassword" id="institutePassword"  placeholder="Password">
                        <small style="font-size: 70%;">(<em>Password must not contain any space and special characters < > { } # / \ ? : ! & $ ^ * % ` | + "</em>)</small>
                        <%} else {%>
                        <textarea class="form-control admin" name="useremail" id="useremail"  placeholder="Add users email (separated by comma for multiple ${session['siteId'].intValue()==24? 'users or reviewers': 'users'})" style="height: 100px;"></textarea><br>
                        <input type="text" class="form-control admin col-md-6" name="institutePassword" id="institutePassword"  placeholder="Password">

                        <%} %>
                        <br>
                        <button class="btn btn-primary btadd mt-2" onclick="addUser('student');">Add Users</button>

                        <% if(session["siteId"].intValue()==24){%>
                        <button class="btn btn-primary btadd mt-2" onclick="addUser('instructor');">Add Reviewers</button>
                        <%} %>
                        <%if(wsSite){%>
                        <div class="form-group col-6 col-md-5 mt-4 pl-0">
                            <select id="userBatch" class="form-control admin w-100" name="userBatch"  style="display: none"><option value="" selected="selected">Select Class</option></select>
                        </div>
                        <%}%>



                    </div>

                    <p style="margin-top: 1.5rem;margin-left: -35px;color: #444;">- or -</p>
                    <div class="form-group col-md-3 mt-3">
                        <input type="hidden" name="mode" value="submit">
                        <input id="FileInputElement" type="file" class="form-control w-100" name="FileInputElement" accept=".xlsx" />
                        <small><em>(Max 200 users at once)</em></small>
                    </div>
                    <div class="form-group col-md-3 mt-3">
                        <input type="hidden" name="batchId" id="batchId" value="batchId">
                        <button type="button" onclick="javascript:uploadNotes()" class="btn btn-primary btn-lg">Upload Users</button>
                        <button class="btn btn-primary btadd" onclick="javascript:downloadUploadFileSample()">Download Sample</button>
                    </div>

                </div>
                <div id="addIPs" style="display: none">
                    <div class="form-group col-md-12">
                        <input type="text" class="form-control admin col-md-4" name="locationName" id="locationName"  placeholder="Add IP/Location name"></input><br>
                        <textarea class="form-control" name="ipAddress" id="ipAddress"  placeholder="Add IP Address(separated by comma for multiple addresses)"></textarea><br>

                        <button class="btn btn-primary btadd" onclick="addIPAddress();">Add IP</button>
                    </div>
                </div>
                <div id="addIPsRange" style="display: none">
                    <div class="form-group col-md-12">
                        <input type="text" class="form-control col-md-4" name="locationName" id="locationNameRange"  placeholder="Add IP/Location name"></input><br>
                        <input type="text" class="form-control" id="ipAddressFrom" name="ipAddressFrom" placeholder=" From IP Address">&nbsp;&nbsp;
                        <input type="text" class="form-control" id="ipAddressTo" name="ipAddressTo" placeholder=" To IP Address"><br>
                        <button class="btn btn-primary btadd" onclick="addIPAddressRange();">Add IP Addresses</button>
                    </div>
                </div>

                <div id="errormsg" class="alert alert-danger has-error" role="alert" style="display: none; background: none;word-wrap: break-word;"></div>
                <div id="successmsg" style="display: none"></div>
                <div id="batchUsers" style="display: none"></div>
                <div style="margin-right: 25px; display: none;" id="download">
                    <div class="form-group">
                        <button type="button" id="download-btn" class="btn btn-primary " style="border-width: 0px;" >Download</button>
                    </div>
                </div>
                <div style="margin-right: 25px; display: none;" id="download1">
                    <div class="form-group">
                        <button type="button" id="download-btn1" class="btn btn-primary " style="border-width: 0px;" >Download</button>
                    </div>
                </div>
                <div style="margin-right: 25px; display: none;" id="download2">
                    <div class="form-group">
                        <button type="button" id="download-btn2" class="btn btn-primary " style="border-width: 0px;" >Download</button>
                    </div>
                    <div class="form-group">
                        <button type="button" id="download-userwithclasses" class="btn btn-primary " style="border-width: 0px;" >Download with classes</button>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <div class="modal" id="removePhone">
        <div class="modal-dialog modal-dialog-centered modal-sm">
            <div class="modal-content">

                <!-- Modal Header -->
                <div class="modal-header">
                    <h4 class="modal-title">Login Limit</h4>
                    <button type="button" class="close" data-dismiss="modal" style='margin-top: -26px;'>&times;</button>
                </div>

                <!-- Modal body -->
                <div class="modal-body text-center">
                    <input type='number' name='number' id='phonenum' placeholder='Enter login limit' style='padding: 1rem;' oninput="this.value = this.value.replace(/[^0-9.]/g, ''); this.value = this.value.replace(/(\..*)\./g, '$1');" onKeyDown="if(this.value.length==10 && event.keyCode!=8) return false;">
                </div>

                <!-- Modal footer -->
                <div class="modal-footer">
                    <button type="submit" class="btn btn-danger"  onclick="javascript:submitsearch()">Submit</button>
                </div>

            </div>
        </div>
    </div>

    <div class="modal" id="addBooksToUserModal">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">

                <!-- Modal Header -->
                <div class="modal-header d-flex align-items-center">
                    <h5 class="modal-title">Add book to user</h5>
                    <button type="button" class="close" data-dismiss="modal" style='margin-top: -26px;'>&times;</button>
                </div>

                <!-- Modal body -->
                <div class="modal-body">
                    <label>Enter user's email/mobile</label><br>
                    <input type='text' id='username' class='form-control w-100 border' autocomplete="off">
                    <input type="hidden" id="addBookUserBookId" value="">
                    <input type="hidden" id="addBookUserBatchId" value="">
                </div>

                <!-- Modal footer -->
                <div class="modal-footer border-0">
                    <button type="submit" class="btn btn-primary"  onclick="javascript:submitBookToUser()">Add</button>
                </div>

            </div>
        </div>
    </div>

</div>


<g:render template="/${session['entryController']}/footer_new"></g:render>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="jquery.simple-dtpicker.js"/>
%{--<script src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>--}%
%{--<script src="https://cdn.datatables.net/1.10.20/js/dataTables.bootstrap4.min.js"></script>--}%
<script>
    var batchId=0;
    var instituteId=0;
    var institutionEdit = false;

    var publisherId="${publisherId}";
    var selectedSyllabus = "";
    const siteId1 = "${session["siteId"]}"
    function addGenerateAccessCode(){

        $('.loading-icon').addClass('hidden');
        $("#errormsg").hide();
        $("#successmsg").hide();
        var batch = document.getElementById("batches");
        var accesscode=document.getElementById("accesscodegen").value;
        if(accesscode=="" ){
            document.getElementById("errormsg").innerHTML="Please enter the count of access code.";
            $("#errormsg").show();
            $("#accesscodegen").focus();
        }else if(accesscode==0 || accesscode.includes("-")){
            document.getElementById("errormsg").innerHTML="Please enter valid access code count.";
            $("#errormsg").show();
            $("#accesscodegen").focus();
        }
        else{
            if(noOfUsers == -1 || parseInt(document.getElementById("pending-lic").innerText.toString().substring(34))>=parseInt(document.getElementById("accesscodegen").value)){
                $('.loading-icon').removeClass('hidden');
                $("#errormsg").hide();
                var accessCount = document.getElementById("accesscodegen").value;
                <g:remoteFunction controller="institute" action="generateAccessCodeForInstitute" params="'accessCount='+accessCount+'&instituteId='+instituteId" onSuccess = "accessUpdated(data);"/>
            }else{
                alert("You dont have required no. of licenses.")
                return
            }
        }

    }

    function accessUpdated(data) {
        loadUserStats()
        $('.loading-icon').addClass('hidden');
        $("#errormsg").hide();
        $("#successmsg").hide();
        if(data.status=="OK"){
            document.getElementById("successmsg").innerHTML="Added Successfully!";
            $("#successmsg").show();
            $("#accesscodegen").val('');
        }
    }

    $(function(){
        $('*[name=endDate]').appendDtpicker({
            "futureOnly": true,
            "autodateOnStart": false,
            "dateOnly":true
        });


        <% if(sageSite){%>
        $('*[name=startDate]').appendDtpicker({
            "futureOnly": true,
            "autodateOnStart": false,
            "dateOnly":true
        });
        <%}else{ %>
        $('*[name=startDate]').appendDtpicker({
            "autodateOnStart": false,
            "dateOnly":true
        });
        <% } %>



    });

    function addInstitute(){


        if(!institutionEdit){
            <%if(session["siteId"].intValue()==80){%>
            alert("You are not authorized to add institute.");
            return;
            <%}%>
        }
        var inValidStartDate = false;
        if (document.getElementById("endDate").value != "" && document.getElementById("startDate").value != ""){
            var ensDate = new Date(document.getElementById("endDate").value);
            var startDate = new Date(document.getElementById("startDate").value);
            if(ensDate.getTime() < startDate.getTime()) inValidStartDate = true;
        }
        // if(!inValidStartDate) alert(document.getElementById("endDate").value);
        var institute = document.getElementById("institutes");
        if(document.getElementById("insname").value=="" ){
            document.getElementById("errormsg1").innerHTML="Please enter the Institute name."
            $("#errormsg1").show();
        }
        else if(document.getElementById("contactEmail").value.match(/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/) == null ){
            // alert();
            document.getElementById("errormsg1").innerHTML="Please enter valid Email."
            $("#errormsg1").show();
        }else if (document.getElementById("contactName").value=="" ){
            document.getElementById("errormsg1").innerHTML="Please enter the Librarian name."
            $("#errormsg1").show();
        }else if(document.getElementById("startDate").value == ""){
            document.getElementById("errormsg1").innerHTML="Please Select Start Date."
            $("#errormsg1").show();
        }else if (inValidStartDate){
            document.getElementById("errormsg1").innerHTML="Please enter valid Start Date. Start Date cannot be greater then Completion date";
            $("#errormsg1").show();
        }
        else if(parseInt(document.getElementById("used-lic").innerText.toString().substring(31))>parseInt(document.getElementById("noofusers").value)){
            alert("No of licences cannot be less than used.")
            return
        }
        else{
            $('.loading-icon').removeClass('hidden');
            $("#errormsg1").hide();
            <% if(session["siteId"].intValue()!=24){%>
            var ipRestricted = $("input[name='ipRestricted']:checked"). val();
            <%}else{ %>
            var ipRestricted = false;
            <% } %>
            var name = document.getElementById("insname").value;
            var endDate = document.getElementById("endDate").value;
            var startDate = document.getElementById("startDate").value;
            var contactName = document.getElementById("contactName").value;
            var contactEmail = document.getElementById("contactEmail").value;
            var noofusers = document.getElementById("noofusers").value;
            var checkOutDays = (${wsSite})?document.getElementById("checkOutDays").value:null;
            if(instituteId == undefined || instituteId == null){
                instituteId = '0'
            }
            var fullLibraryView = "";
            var paidFreeTab = "";
            var eduWonder = "";
            var level = "";
            var syllabus = "";
            var shopEnabled = "";
            var leaderBoardEnabled = "";
            var copyDefaultBooks = "";
            var defaultBooksTemplateInstitute = "";
            var goneLive="";
            var preUniversity="";
            <% if(wsSite){%>
            fullLibraryView = $("input[name='fullLibraryView']:checked"). val();
            paidFreeTab = $("input[name='paidFreeTab']:checked"). val();
            eduWonder = $("input[name='eduWonder']:checked"). val();
            shopEnabled = $("input[name='shopEnabled']:checked"). val();
            leaderBoardEnabled = $("input[name='leaderBoardEnabled']:checked"). val();
            copyDefaultBooks = $("input[name='copyDefaultBooks']:checked"). val();
            goneLive = $("input[name='goneLive']:checked"). val();
            preUniversity = $("input[name='preUniversity']:checked"). val();
            <%if(isDefaultBookSetter){%>
            defaultBooksTemplateInstitute = $("input[name='defaultBooksTemplateInstitute']:checked"). val();
            <%}%>
            level = encodeURIComponent(document.getElementById("createLevel")[document.getElementById("createLevel").selectedIndex].value);
            if(document.getElementById("createSyllabus").selectedIndex>0) {
                var selMulti = $.map($("#createSyllabus option:selected"), function (el, i) {
                    return $(el).text();
                });
                syllabus = encodeURIComponent(selMulti.join(","));

            }

            <%}%>

            <g:remoteFunction controller="institute" action="addInstitute"
            params="'ipRestricted='+ipRestricted+'&endDate='+endDate+'&noOfusers='+noofusers+'&name='+name+'&contactName='+contactName+'&contactEmail='+contactEmail+'&institutionEdit='+institutionEdit+'&institutionId='+instituteId+'&startDate='+startDate+'&publisherId='+publisherId+'&checkOutDays='+checkOutDays+'&fullLibraryView='+fullLibraryView+'&paidFreeTab='+paidFreeTab+'&eduWonder='+eduWonder+'&level='+level+'&syllabus='+syllabus+'&shopEnabled='+shopEnabled+'&leaderBoardEnabled='+leaderBoardEnabled+'&copyDefaultBooks='+copyDefaultBooks+'&defaultBooksTemplateInstitute='+defaultBooksTemplateInstitute+'&goneLive='+goneLive+'&preUniversity='+preUniversity" onSuccess = "instituteAdded(data);"/>
        }
    }

    function instituteAdded(data){
        if(institutionEdit){
            if(siteId1==='24'){
                alert("Organization updated successfully");
            }else{
                alert("Institute updated successfully");
            }
        }else{
            if(siteId1==='24'){
                alert("Organization added successfully");
            }else{
                alert("Institute added successfully");
            }
        }
        window.location.href = "/institute/libAdmin";
    }
    function instituteChanged(){
        noOfUsers = -1
        // var ipRestricted = $("input[name='ipRestricted']:checked"). val();
        document.getElementById("insname").value="";
        document.getElementById("endDate").value="";
        document.getElementById("startDate").value="";
        document.getElementById("contactName").value="";
        document.getElementById("contactEmail").value="";
        $("#errormsg").hide();
        $("#successmsg").hide();
        $('#download2').hide();
        $('#download1').hide();
        $('#download').hide();
        var institutesList = document.getElementById("institutes");
        var selectedValue=institutesList[institutesList.selectedIndex].value;
        var ipRestricted=false;
        if(institutesList.selectedIndex>0){

            instituteId = selectedValue.substring(0,selectedValue.indexOf('_'));
            ipRestricted = selectedValue.substring((selectedValue.indexOf('_')+1),(selectedValue.lastIndexOf('_')));
            batchId = selectedValue.substring(selectedValue.lastIndexOf('_')+1);


            var instituteActionElement = document.getElementById("instituteAction");
            var listLength=instituteActionElement.length;
            for(var i=1;i<=listLength;i++) {
                instituteActionElement.remove(1);
            }

            var option = document.createElement("option");
            option.value = "editInstitute";
            option.text = siteId1==="24"?"Edit Organization":"Edit Institute";
            instituteActionElement.appendChild(option);

            option = document.createElement("option");
            option.value = "addBooks";
            option.text = "Add Books";
            instituteActionElement.appendChild(option);
            if(ipRestricted=="true"){
                option = document.createElement("option");
                option.value = "addIPs";
                option.text = "Add IP addresses";
                instituteActionElement.appendChild(option);
                option = document.createElement("option");
                option.value = "addIPsRange";
                option.text = "Add IP addresses range";
                instituteActionElement.appendChild(option);
            }
               option = document.createElement("option");
                option.value = "addUsers";
                option.text = "Add Users";
                instituteActionElement.appendChild(option);


            option = document.createElement("option");
            option.value = "addInstituteAdmin";
            option.text = siteId1==="24"?"Add Organization Admin":"Add Institute Admin";
            instituteActionElement.appendChild(option);

            <% if(sageSite){%>
            option = document.createElement("option");
            option.value = "addUserLoginLimit";
            option.text = "Add User Login Limit";
            instituteActionElement.appendChild(option);
            if(document.querySelector('.sageInstUpdate')){
                document.querySelector('.sageInstUpdate').innerHTML = "";
            }
            <%} %>

            option = document.createElement("option");
            option.value = "viewBooks";
            option.text = "View Books";
            instituteActionElement.appendChild(option);
            if(ipRestricted=="true"){
                option = document.createElement("option");
                option.value = "viewIPs";
                option.text = "View IP addresses";
                instituteActionElement.appendChild(option);
            }
                option = document.createElement("option");
                option.value = "viewUsers";
                option.text = "View Users";
                instituteActionElement.appendChild(option);

            option = document.createElement("option");
            option.value = "viewInstituteAdmin";
            option.text =siteId1==="24"?"View Organization Admin":"View Institute Admin";
            instituteActionElement.appendChild(option);

            <% if(accessCode){%>
            option = document.createElement("option");
            option.value = "generateAccessCode";
            option.text = "Generate Access Code";
            instituteActionElement.appendChild(option);

            option = document.createElement("option");
            option.value = "viewAccessCode";
            option.text = "View Access Code";
            instituteActionElement.appendChild(option);
            <%} %>

            <% if(wsSite){%>
            option = document.createElement("option");
            option.value = "viewWaitingList";
            option.text = "View Waiting List";
            instituteActionElement.appendChild(option);
            <%} %>

            instituteActionElement.selectedIndex=0;

        }
    }

    function instituteAction(){
        $("#successmsg").hide();
        $("#errormsg").hide();
        $("#addBooks").hide();
        $("#addUsers").hide();
        $("#batchUsers").hide();
        $("#bookUsers").hide();
        $("#addIPs").hide();
        $("#addIPsRange").hide();
        $("#addInstituteAdmin").hide();
        $("#addUserLoginLimit").hide();
        $("#addGenerateAccessCode").hide();
        $("#viewAccessCodes").hide();
        var action = document.getElementById("instituteAction")[document.getElementById("instituteAction").selectedIndex].value;
        if(action == "editInstitute") {
            $("#addInstituteAdmin").hide();
            $('#download2').hide();
            $('#download').hide();
            $('#download1').hide();
            getInstituteDetails();
            <% if(sageSite){%>
            document.querySelector('.sageInstUpdate').innerHTML = "<button class='btn btn-primary ml-3' id='insEditBtn' onclick='addInstitute();'>"+
                " Update Institute </button>";
            <%}%>
        }
        else if(action=="viewBooks"){
            $("#addInstituteAdmin").hide();
            getBooksForBatch();
        }
        else if(action=="viewInstituteAdmin"){
            $("#addInstituteAdmin").hide();
            $('#download2').hide();
            $('#download').hide();
            $('#download1').hide();
            getInstituteAdmin();
        }else if(action=="addBooks"){
            $("#addInstituteAdmin").hide();
            $('#download2').hide();
            $('#download').hide();
            $('#download1').hide();
            document.getElementById("bookId").value="";
            $("#addBooks").show();
            <% if(wsSite){%>
            getAllBooks();
            <%} %>
        }else if(action=="addInstituteAdmin"){
            $('#download2').hide();
            $('#download').hide();
            $('#download1').hide();
            document.getElementById("bookId").value="";
            $("#addInstituteAdmin").show();
            loadUserStats()
        }
        else if(action=="addUserLoginLimit"){
            $('#download2').hide();
            $('#download').hide();
            $('#download1').hide();
            $("#addUserLoginLimit").show();
            getInstituteUsersLoginlimitDetails();
        }

        else if(action=="generateAccessCode"){
            loadUserStats()
            $('#download2').hide();
            $('#download').hide();
            $('#download1').hide();
            $("#addGenerateAccessCode").show();
        }
        else if(action=="viewAccessCode"){
            $('#download2').hide();
            $('#download').hide();
            $('#download1').hide();
            $("#viewAccessCodes").show();
            viewAccessCodes()
        }
        else if(action=="addUsers"){
            $("#addInstituteAdmin").hide();
            $('#download2').hide();
            $('#download').hide();
            $('#download1').hide();
            $("#addUsers").css("display", "flex");
            loadUserStats()
        }
        else if(action=="viewUsers"){
            $("#addInstituteAdmin").hide();
            showAllUsers();
        }
        else if(action=="addIPs"){
            $("#addInstituteAdmin").hide();
            $('#download2').hide();
            $('#download').hide();
            $('#download1').hide();
            document.getElementById("ipAddress").value="";
            document.getElementById("locationName").value="";
            $("#addIPs").show();

        }else if(action=="viewIPs"){
            $("#addInstituteAdmin").hide();
            getIPAddressesForInstitute();
        }
        else if(action=="addIPsRange"){
            $("#addInstituteAdmin").hide();
            $('#download2').hide();
            $('#download').hide();
            $('#download1').hide();
            document.getElementById("locationNameRange").value="";
            document.getElementById("ipAddressFrom").value="";
            document.getElementById("ipAddressTo").value="";
            $("#addIPsRange").show();
        } else if(action=="viewWaitingList") {
            $("#addInstituteAdmin").hide();
            $('#download2').hide();
            $('#download').hide();
            $('#download1').hide();
            getWaitingList();
        }
    }

    function addUser(userType){
        var validate=""
        $('.loading-icon').addClass('hidden');
        $("#errormsg").hide();
        $("#successmsg").hide();
        var batch = document.getElementById("batches");

        var password=document.getElementById("institutePassword").value;
        password=password.replace(/&/g,'~');

        if(document.getElementById("useremail").value==="" ){
            document.getElementById("errormsg").innerHTML="Please enter the email id."
            $("#errormsg").show();
        }
        else{
            $("#errormsg").hide();
            var email = document.getElementById("useremail").value;
            <%if(wsSite){ %>
            validate=true;
                <% }else {%>
             validate= validateMultipleEmailsCommaSeparated(email);
                <% }%>
            if(validate==true){
                var classBatchId = null;
                <%if(wsSite){%>
                if(document.getElementById("userBatch").selectedIndex>0) classBatchId =document.getElementById("userBatch")[document.getElementById("userBatch").selectedIndex].value;
                <%}%>
                if(noOfUsers>-1){
                    if(parseInt(document.getElementById("pending-lic").innerText.toString().substring(34))>=email.split(",").length){
                        $('.loading-icon').removeClass('hidden');
                        <% if(sageSite){%>
                        <g:remoteFunction controller="institute" action="genarateUsertoBatch" params="'batchId='+batchId+'&useremail='+email+'&instituteId='+instituteId+'&userType='+userType" onSuccess = "generateUserAdded(data);"/>
                        <%}else{ %>
                        <g:remoteFunction controller="institute" action="genarateUsertoBatchWS" params="'password='+password+'&batchId='+batchId+'&useremail='+email+'&classBatchId='+classBatchId" onSuccess = "userAdded(data);"/>
                        <%}%>
                    }else{
                        alert("No licenses left.")
                    }
                }else{
                    $('.loading-icon').removeClass('hidden');
                    <% if(sageSite){%>
                    <g:remoteFunction controller="institute" action="genarateUsertoBatch" params="'batchId='+batchId+'&useremail='+email+'&instituteId='+instituteId+'&userType='+userType" onSuccess = "generateUserAdded(data);"/>
                    <%}else{%>
                    <g:remoteFunction controller="institute" action="genarateUsertoBatchWS" params="'password='+password+'&batchId='+batchId+'&useremail='+email+'&classBatchId='+classBatchId" onSuccess = "userAdded(data);"/>
                    <%}%>
                }


            }

        }
    }

    function  getInstituteUsersLoginlimitDetails() {
        <g:remoteFunction controller="institute" action="getInstituteUsersLoginlimitDetails" params="'instituteId='+instituteId" onSuccess = "displayLimit(data);"/>

    }
    function displayLimit(data) {
        if(data.limit != null){
            document.getElementById("userloginlimit").value=data.limit;
        }else{
            document.getElementById("userloginlimit").value="";
        }


    }


    function validateEmail(field) {
        var regex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,5}$/;
        return (regex.test(field)) ? true : false;
    }
    function validateMultipleEmailsCommaSeparated(emailcntl) {
        $('.loading-icon').addClass('hidden');
        var value = emailcntl;
        if (value != '') {
            var result = value.split(",");
            for (var i = 0; i < result.length; i++) {
                if (result[i] != '') {
                    if (!validateEmail(result[i])) {
                        // emailcntl.focus();
                        alert('Please check, `' + result[i] + '` email addresses not valid!');
                        return false;
                    }
                }
            }
        }
        return true;
    }

    function  addInstituteAdmin() {

        $('.loading-icon').addClass('hidden');
        $("#errormsg").hide();
        $("#successmsg").hide();
        var batch = document.getElementById("batches");
        if(document.getElementById("adminUsername").value==="" ){
            document.getElementById("errormsg").innerHTML="Please enter the user email/mobile no."
            $("#errormsg").show();
        }
        else if(parseInt(document.getElementById("pending-lic").innerText.toString().substring(34))<1){
            alert("No Licenses Left.")
        }
        else{
            $('.loading-icon').removeClass('hidden');
            $("#errormsg").hide();
            var email = document.getElementById("adminUsername").value;
            var name = document.getElementById("adminActualname").value;
            var password = document.getElementById("adminActualPassword").value;
            var libAdminOption =  $("input[name='libraryAdmin']:checked"). val();
            <g:remoteFunction controller="institute" action="addInstitiuteAdmin" params="'batchId='+batchId+'&useremail='+email+'&name='+name+'&password='+password+'&libAdminOption='+libAdminOption" onSuccess = "adminAdded(data);"/>
        }
    }

    function getInstituteAdmin() {
        $('.loading-icon').addClass('hidden');
        $("#errormsg").hide();
        $("#successmsg").hide();
        <g:remoteFunction controller="institute" action="viewInstituteAdmin" params="'instituteId='+instituteId" onSuccess = "viewAdmin(data);"/>

    }

    function viewAdmin(data){
        var htmlStr='';
        $("#batchUsers").show();
        if(data.status=="OK"){
            var admin=data.adminData;
            htmlStr+=                          "<table class='table table-striped table-bordered'>"+
                    "                            <th>Name</th>\n" +
                    "                            <th>Email</th>\n" +
                    "                            <th>Mobile</th>\n";
                for (var i = 0; i < admin.length; i++)
                    htmlStr += "<tr><td>" + admin[i].name + "</td>" + "<td>" + admin[i].email + "</td>" + "<td>" + admin[i].mobile + "</td>" +
                "</tr>";
            htmlStr+="</table>";
                document.getElementById("batchUsers").innerHTML = htmlStr;
        }
        else {
            if(siteId1==="24"){
                htmlStr+="<p>No admin present for this organization<p>"
            }
            else {
            htmlStr+="<p>"+data.status+"</p>";
            }
            document.getElementById("batchUsers").innerHTML =htmlStr;

        }
    }

    function  addUserLoginLimit() {
        $('.loading-icon').addClass('hidden');
        $("#errormsg").hide();
        $("#successmsg").hide();
        var batch = document.getElementById("batches");
        if(document.getElementById("userloginlimit").value==="" ){
            document.getElementById("errormsg").innerHTML="Please enter the user login limit."
            $("#errormsg").show();
        }
        else{
            $('.loading-icon').removeClass('hidden');
            $("#errormsg").hide();
            var userloginlimit = document.getElementById("userloginlimit").value;
            <g:remoteFunction controller="institute" action="updateUserloginlimit" params="'batchId='+batchId+'&userloginlimit='+userloginlimit+'&instituteId='+instituteId" onSuccess = "loginlmitUpdated(data);"/>
        }
    }

    function loginlmitUpdated(data) {
        $('.loading-icon').addClass('hidden');
        $("#errormsg").hide();
        $("#successmsg").hide();
        if(data.status=="OK"){
            document.getElementById("successmsg").innerHTML="Updated Successfully!";
            $("#successmsg").show();
        }


    }




    function addBook(){
        $('.loading-icon').addClass('hidden');
        $("#errormsg").hide();
        $("#successmsg").hide();

        if($("input[name='bookIdMethod']:checked"). val()==undefined){
            document.getElementById("errormsg").innerHTML="Please book input type";
            $("#errormsg").show();

        }else if(document.getElementById("bookId").value==="" ){
            document.getElementById("errormsg").innerHTML="Please enter the book ids/isbns."
            $("#errormsg").show();
        }
        else{
            $('.loading-icon').removeClass('hidden');
            var bookId = document.getElementById("bookId").value;
            var bookIdMethod=$("input[name='bookIdMethod']:checked"). val();

            <g:remoteFunction controller="institute" action="addBooks" params="'instituteId='+instituteId+'&bookIds='+bookId+'&bookIdMethod='+bookIdMethod+'&publisherId='+publisherId" onSuccess = "bookAdded(data);"/>
        }
    }

    function addIPAddress(){
        $('.loading-icon').addClass('hidden');
        $("#errormsg").hide();
        $("#successmsg").hide();

        if(document.getElementById("locationName").value==="" ){
            document.getElementById("errormsg").innerHTML="Please enter the address/location name."
            $("#errormsg").show();
        }else if(document.getElementById("ipAddress").value==="" ){
            document.getElementById("errormsg").innerHTML="Please enter ip address."
            $("#errormsg").show();
        }
        else{
            var locationName = document.getElementById("locationName").value;
            var ipAddress=document.getElementById("ipAddress").value;

            <g:remoteFunction controller="institute" action="addIP" params="'instituteId='+instituteId+'&ipAddress='+ipAddress+'&locationName='+locationName" onSuccess = "ipAddressAdded(data);"/>
        }
    }

    function addIPAddressRange(){
        $('.loading-icon').addClass('hidden');
        $("#errormsg").hide();
        $("#successmsg").hide();

        if(document.getElementById("locationNameRange").value==="" ){
            document.getElementById("errormsg").innerHTML="Please enter the address/location name."
            $("#errormsg").show();
        }else if(document.getElementById("ipAddressFrom").value==="" ){
            document.getElementById("errormsg").innerHTML="Please enter from ip address."
            $("#errormsg").show();
        }
        else if(document.getElementById("ipAddressTo").value==="" ){
            document.getElementById("errormsg").innerHTML="Please enter to ip address."
            $("#errormsg").show();
        }
        else{
            var locationName = document.getElementById("locationNameRange").value;
            var ipAddressFrom=document.getElementById("ipAddressFrom").value;
            var ipAddressTo=document.getElementById("ipAddressTo").value;
            <g:remoteFunction controller="institute" action="addIPAddressRange" params="'instituteId='+instituteId+'&ipAddressFrom='+ipAddressFrom+'&ipAddressTo='+ipAddressTo+'&locationName='+locationName" onSuccess = "ipAddressAdded(data);"/>
        }
    }

    function userAdded(data){
        loadUserStats()
        $("#errormsg").hide();
        $("#successmsg").hide();
        $('.loading-icon').addClass('hidden');
        if(data.status=="error"){
            document.getElementById("errormsg").innerHTML="This user is not registered. Kindly ask the user to register first.<br>"
            if(data.userNotAdded){
                document.getElementById("errormsg").innerHTML+=data.userNotAdded+" is not registered."
            }
            $("#errormsg").show();
        }
        else{
            <%if(wsSite){ %>
            document.getElementById("successmsg").innerHTML ="Added successfully"
            <% }else {%>
            document.getElementById("successmsg").innerHTML = data.status + '<br>'
            <% }%>
            if(data.userNotAdded){
                document.getElementById("successmsg").innerHTML+=data.userNotAdded+" is not registered."
            }
            document.getElementById("useremail").value="";
            $("#successmsg").show();
        }
    }

    function generateUserAdded(data){
        loadUserStats()
        $("#errormsg").hide();
        $("#successmsg").hide();
        $('.loading-icon').addClass('hidden');

        document.getElementById("successmsg").innerHTML=data.status + data.alreadyPresent ;
        document.getElementById("useremail").value="";
        $("#successmsg").show();
    }
    function adminAdded(data){
        $("#errormsg").hide();
        $("#successmsg").hide();
        $('.loading-icon').addClass('hidden');
        if(data.status=="no user"){
            document.getElementById("errormsg").innerHTML="This user is not registered. Kindly ask the user to register first."
            $("#errormsg").show();
        }else if(data.status=="admin present"){
            document.getElementById("errormsg").innerHTML="This institute admin  is already present.Please check."
            $("#errormsg").show();
        }
        else{
            document.getElementById("successmsg").innerHTML=data.status;
            document.getElementById("useremail").value="";
            $("#successmsg").show();
        }
    }

    function ipAddressAdded(data){
        $("#errormsg").hide();
        $("#successmsg").hide();
        $('.loading-icon').addClass('hidden');
        if(data.status=="OK"){
            document.getElementById("successmsg").innerHTML="IP address added";
            $("#successmsg").show();
        }
        else{
            document.getElementById("errormsg").innerHTML="IP addressed not added due to error"
            $("#errormsg").show();
        }
    }

    function batchBooksAdded(){
        $('.loading-icon').addClass('hidden');
        document.getElementById("successmsg").innerHTML="Book added";
        document.getElementById("bookId").value="";
        $("#successmsg").show();
    }

    function bookAdded(data){
        $("#errormsg").hide();
        $("#successmsg").hide();
        $('.loading-icon').addClass('hidden');
        if(data.status=="OK"){
            var booksBatchId = null;
            <%if(wsSite){%>
            if(document.getElementById("booksBatch").selectedIndex>0) {
                var defaultBatchId = data.defaultBatchId;
                booksBatchId =document.getElementById("booksBatch")[document.getElementById("booksBatch").selectedIndex].value;
                $('.loading-icon').removeClass('hidden');
                var bookIds = data.bookIds;
                <g:remoteFunction controller="institute" action="addBooksToBatch" params="'batchId='+booksBatchId+'&bookIds='+bookIds+'&defaultBatchId='+defaultBatchId" onSuccess = "batchBooksAdded(data);"/>

            }else{
                document.getElementById("successmsg").innerHTML="Book added";
                document.getElementById("bookId").value="";
                if(data.booksReAdded){
                    document.getElementById("successmsg").innerHTML+= "  <br>"+ data.booksReAdded+ " is already present"
                }
                $("#successmsg").show();
            }
            <%}else{%>

                document.getElementById("successmsg").innerHTML="Book added";
                document.getElementById("bookId").value="";
                if(data.booksReAdded){
                    document.getElementById("successmsg").innerHTML+= "  <br>"+ data.booksReAdded+ " is already present"
                }
                $("#successmsg").show();
            <%}%>

        }
        else{
            $("#successmsg").hide();
            document.getElementById("errormsg").innerHTML=data.booksNotAdded+"  was not added. Either the Book Id/ISBN is incorrect";
            if(data.booksReAdded){
                document.getElementById("errormsg").innerHTML+= "<br>"+ data.booksReAdded+ " is already present"
            }
            $("#errormsg").show();
        }
    }

    function getInstituteDetails(){
        // $('#endDate').hide();
        loadUserStats()
        <g:remoteFunction controller="institute" action="getInstituteDetails" params="'instituteId='+instituteId" onSuccess = "showInstitutionDetails(data);"/>
    }

    function getBooksForBatch(){
        $("#errormsg").hide();
        $("#successmsg").hide();
        $("#batchUsers").show();

        <g:remoteFunction controller="institute" action="getBooksForBatch" params="'batchId='+batchId" onSuccess = "showBooksForBatch(data);"/>

    }
    function showAllUsers(){
        $("#errormsg").hide();
        $("#successmsg").hide();
        $("#batchUsers").show();

        $('.loading-icon').removeClass('hidden');
        $("#errormsg").hide();

        <g:remoteFunction controller="institute" action="showAllUsersForBatch" params="'instituteId='+instituteId+'&batchId='+batchId+'&userType=student'" onSuccess = "showUsersForBatch(data);"/>

    }

    function batchCompleted(){
        $("#errormsg").hide();
        $("#successmsg").hide();
        $("#batchUsers").show();
        var batch = document.getElementById("batches");
        if(batch.selectedIndex==0){
            document.getElementById("errormsg").innerHTML="Please select a batch"
            $("#errormsg").show();
        }
        else{
            $('.loading-icon').removeClass('hidden');
            $("#errormsg").hide();
            var batchId = batch.value;
            if(confirm("Do you want to go ahead and mark this batch as completed?")) {
                <g:remoteFunction controller="institute" action="batchCompleted" params="'batchId='+batchId" onSuccess = "batchCompletionUpdated(data);"/>
            }
        }
    }

    function batchCompletionUpdated(data){
        window.location.href = "/institute/admin";
    }

    function showUsersForBatch(data){
        $('.loading-icon').addClass('hidden');
        var htmlStr="<h3>"+(data.userType)+"s of this "+(siteId1==="24"?"organization": "institute")+"</h3><br>\n" +
            "<table class='table table-striped table-bordered'>\n" +
            "<tr>\n" +
            "<th>Name</th>\n" +
            "<th>Email</th>\n"
        htmlStr+="<th>Mobile</th>\n"
        <% if(session["siteId"].intValue()==24){%>htmlStr+="<th>User Type</th>\n"<%}%>
        htmlStr+=" <th>Delete</th>\n"
        <% if(sageSite){%>htmlStr+="<th>Login Limit</th><th>Send Email</th>\n"<%}%>
           htmlStr+= " </tr>\n" ;

        if(data.status=="OK"){
            var users = data.users;
            for(i=0;i<users.length;i++){
                var username =users[i].username;
                htmlStr +="<tr><td style='text-transform:capitalize;'>"+users[i].name+"</td>"+
                    "<td>"+(users[i].email?users[i].email: '')+"</td>"
                htmlStr+="<td>"+(users[i].mobile?users[i].mobile: '')+"</td>"
                <% if(session["siteId"].intValue()==24){%>htmlStr+="<td>"+(users[i].userType=='instructor'?'Reviewer':' ')+"</td>"<%}%>
                htmlStr+=    "<td><a href='javascript:deleteUser("+users[i].userId+","+data.batchId+");'><img  src='${assetPath(src: 'baseline-delete-24px.svg')}' alt='wonderslate'></a></td>"
                <% if(sageSite){%>
                htmlStr+="<td class='text-left'>"+users[i].maxlogins+"<button type=\"button\" class=\"btn btn-sm btn-outline-primary\"onclick=\"javascript:openModal('"+username+"')\" style='margin-left: 15px; font-size: 12px;'>EDIT\n" +
                    "  </button></td>"
                if(users[i].sageLogin!="true") {
                    htmlStr += "<td class='text-left'><button type=\"button\" class=\"btn btn-primary\"onclick=\"javascript:generateSignupMail('" + users[i].userId + "')\" style='margin-left: 15px; font-size: 12px;'>Send\n" +
                        "  </button></td>"
                }
                else{
                    htmlStr += "<td class='text-left'>Credentials already generated</td>"
                }
                <%}%>
                htmlStr+=    "</tr>";
            }
            htmlStr +="</table>";
            document.getElementById("batchUsers").innerHTML= htmlStr;
            $('#download2').show();
            $('#download').hide();
            $('#download1').hide();
        }else{
            $('#download2').hide();
            $('#download').hide();
            $('#download1').hide();
            document.getElementById("batchUsers").innerHTML= "No students added to this institute yet";
        }
        $("#batchUsers").show();

    }

    function openModal(username){
        oldUsername = username;
        $('#removePhone').modal('show');

    }

    function generateSignupMail(userId){
        <g:remoteFunction controller="institute" action="generateSignUpEmail" params="'userId='+userId" onSuccess='EmailSent(data);'/>

    }

    function EmailSent(data){
        if(data.status=="OK")
        {
            alert("Email sent to user")
        }
        else{
            alert("Email not sent to user")
        }
    }

    function submitsearch(){
        $('#removePhone').modal('hide');
        var loginlimit = document.getElementById("phonenum").value;
        <g:remoteFunction controller="institute" action="addUserLoginLimit" params="'loginLimit='+loginlimit+'&username='+oldUsername" onSuccess='showUsersdata(data);'/>

    }

    function showUsersdata(data){
        if(data.status=="OK"){
            alert("Login Limit Updated Successfully");
            showAllUsers();
        }else{

            alert(data.condition);
        }
    }

    function showInstitutionDetails(data){
        institutionEdit = true;
        selectedSyllabus = data.syllabus;
        document.getElementById('insEditBtn').innerText=siteId1==="24"?"Update Organization":"Update Institute"
        $('#insname').val(data.instituteName);
        $('#contactName').val(data.instituteContactName);
        $('#contactEmail').val(data.instituteContactEmail);
        $('#contactEmail').val(data.instituteContactEmail);
        $('#noofusers').val(data.instituteNoOfusers);
        $('#checkOutDays').val(data.checkOutDays);
        <% if(session["siteId"].intValue()!=24){%>
        if(data.instituteIPRestricted == "true"){
            document.getElementById("yes").checked = true;
        }else{
            document.getElementById("no").checked = true;
        }
        <% } %>
        <% if(wsSite){%>
        if(data.fullLibraryView == "false"){
            document.getElementById("fullViewNo").checked = true;
        }else{
            document.getElementById("fullViewYes").checked = true;
        }
        if(data.paidFreeTab == "false" || data.paidFreeTab == null){
            document.getElementById("paidFreeTabNo").checked = true;
        }else{
            document.getElementById("paidFreeTabYes").checked = true;
        }
        if(data.eduWonder == "true"){
            document.getElementById("eduWonderYes").checked = true;
        }else{
            document.getElementById("eduWonderNo").checked = true;
        }
        if(data.shopEnabled == "true"){
            document.getElementById("shopEnabledYes").checked = true;
        }else{
            document.getElementById("shopEnabledNo").checked = true;
        }
        if(data.leaderBoardEnabled == "true"){
            document.getElementById("leaderBoardEnabledYes").checked = true;
        }else{
            document.getElementById("leaderBoardEnabledNo").checked = true;
        }
        <%if(isDefaultBookSetter){%>

        if(data.defaultBooksTemplateInstitute == "true"){
            document.getElementById("defaultBooksTemplateInstituteYes").checked = true;
        }else{
            document.getElementById("defaultBooksTemplateInstituteNo").checked = true;
        }
        <%}%>
        if(data.copyDefaultBooks == "true"){
            document.getElementById("copyDefaultBooksYes").checked = true;
        }else{
            document.getElementById("copyDefaultBooksNo").checked = true;
        }
        if(data.goneLive == "true"){
            document.getElementById("goneLiveYes").checked = true;
        }else{
            document.getElementById("goneLiveNo").checked = true;
        }
        if(data.preUniversity == "true"){
            document.getElementById("preUniversityYes").checked = true;
        }else{
            document.getElementById("preUniversityNo").checked = true;
        }

        <% } %>

        // $('#ipRestricted').val(data.instituteIPRestricted);
        $('#endDate').val(data.endDate);
        $('#startDate').val(data.startDate);
        document.getElementById("secretKey").innerHTML="<b>Secret Key : </b>"+data.secretKey;
        <%if(wsSite){%>

        var objSelect = document.getElementById("createLevel");
        for (var i = 0; i < objSelect.options.length; i++) {
            if (objSelect.options[i].value== data.level) {
                objSelect.options[i].selected = true;
                getCreateSyllabus(data.level);
                return;
            }
        }
        <%}%>

    }

    function showBooksForBatch(data){
        $('.loading-icon').addClass('hidden');
        if(data.status=="OK") {
            var htmlStr = "<div class='mb-2 pb-3'><input disabled type=\"button\" style='float: right; margin-bottom:10px' class='btn btn-danger' id=\"deleteBook\" value='Delete' onclick=\"deleteBook()\"> <h6 class=\"liad\">eBooks of this "+(siteId1==="24"?"organization":"institute")+"</h6>\n </div>" +
                "                    <div class='table-responsive'><table class='table table-striped table-bordered'>\n" +
                "           <thead class='bg-primary text-white text-center'>              <tr>\n" +
                "                           <th><div style='display: flex;align-items: center;'> <input type=\"checkbox\" class='btn btn--light' id=\"selectAll\" value='Select All ' onchange=\"selectAllBooks()\"><span style='margin: 0 .25rem;'>Select All</span></div></th >" +
                "                            <th>Book Id</th>\n" +
                "                            <th>Title</th>\n" +
                "                            <th>Isbn</th>\n"
            <% if(wsSite){%>
                htmlStr+=
                    "                            <th>Publishers</th>\n"
            <%}%>
            htmlStr+=
                    "                            <th>Status</th>\n"
            <% if(wsSite){%>
            htmlStr+=
                "                            <th>Number of copies</th>\n" +
                "                            <th>Validity<br>(in days)</th>\n" +
                "                            <th></th>\n" +
                "                            <th></th>\n" +
                "                            <th></th>\n"
            <%}%>


            htmlStr+=    "</tr> </thead>\n";
        }
        if(data.status=="OK"){
            var books = data.books;
            for(i=0;i<books.length;i++){
                htmlStr +="<tr>" +
                    "<td><input type=\"checkbox\" onchange='bookSelectChanged();' class=\"selectAll\" id=\""+i+"\" value="+books[i].bookId+"></td>" +
                    "<td style='text-transform:capitalize;'>"+books[i].bookId+"</td>"+
                    "<td>"+books[i].title+"</td>"+
                    "<td>"+books[i].Isbn+"</td>"
                <% if(wsSite){%>
                    htmlStr+="<td>"+books[i].publisher+"</td>"
                <%}%>
                    htmlStr+="<td>"+books[i].bookStatus+"</td>"

                <% if(wsSite){%>
                    htmlStr+="<td><input id='"+books[i].bookId+"lic' type='number' min='0' value='"+books[i].noOfLic+"' style='width: 75px;' onkeypress=\"return onlyNumberKey(event)\"></td>"+
                    "<td><input id='"+books[i].bookId+"validity' type='number' min='0' value='"+books[i].validity+"' style='width: 75px;' onkeypress=\"return onlyNumberKey(event)\"></td>"+
                    "<td><button class='btn btn-sm btn-primary' onclick='updateBookBatchDetailData("+books[i].bookId+")'>Update</button></td>"+
                    "<td><button class='btn btn-sm btn-primary' onclick='getBookUsageDetailsData("+books[i].bookId+","+batchId+")'>View Usage</button></td>" +
                    "<td><button class='btn btn-sm btn-primary' onclick='getBookWaitingListData("+books[i].bookId+","+batchId+")'>Waiting List</button></td>"
                <%}%>
                    htmlStr+="</tr>";
            }
            htmlStr +="</table></div>";
            document.getElementById("batchUsers").innerHTML= htmlStr;
            <% if(wsSite){%>
            $('#batchUsers table').DataTable( {
                "ordering": ('1'=="${session["siteId"]}" || '25'=="${session["siteId"]}"),
                order: [[1, 'asc']],
                "columnDefs": [
                    { "targets": [0,8,9,10], "orderable": false }
                ]
            });

            <%}else{%>
            $('#batchUsers table').DataTable( {
                "ordering": ('1'=="${session["siteId"]}" || '25'=="${session["siteId"]}"),
            });
            <%}%>
            $('#download').show();
            $('#download2').hide();
            $('#download1').hide();
        }else{
            $('#download2').hide();
            $('#download').hide();
            $('#download1').hide();
            document.getElementById("batchUsers").innerHTML= "No eBooks added to this institute yet";
        }
        $("#batchUsers").show();
    }

    function bookSelectChanged(field){

        if($(field).is(":checked") == true){
            if(bookIdArr.indexOf($(field).val()) == -1) bookIdArr.push($(field).val());
        }else if($(field).is(':checked') == false){
            if(bookIdArr.indexOf($(field).val()) > -1){
                bookIdArr.splice(bookIdArr.indexOf($(field).val()),1);
            }
        }

        var bookChkSelected = false;
        var checkArr = document.getElementsByClassName("selectAll");
        if(!checkArr.checked){
            document.getElementById("selectAll").checked = false;
        }
        for(var k = 0; k < checkArr.length; k++){
            if(checkArr[k].checked){
                document.getElementById("deleteBook").disabled = false;
                bookChkSelected = true;
                break
            }
        }

        if(!bookChkSelected){
            document.getElementById("deleteBook").disabled = true;
            document.getElementById("selectAll").checked = false;
        }
    }

    function selectAllBooks(){
        var checkArr = document.getElementsByClassName("selectAll");
        if(document.getElementById("selectAll").checked){
            for (var k = 0; k < checkArr.length; k++) {
                checkArr[k].checked = true;
                if(bookIdArr.indexOf(checkArr[k].value) > -1) continue;
                else bookIdArr.push(checkArr[k].value);

            }
            document.getElementById("deleteBook").disabled = false;
        }else{
            for (var k = 0; k < checkArr.length; k++) {
                // alert(bookIdArr.indexOf(checkArr[k].value));
                bookIdArr.splice(bookIdArr.indexOf(checkArr[k].value),1);
                checkArr[k].checked = false;
                // get the index of the id
                // slice the id using the index
            }
            document.getElementById("deleteBook").disabled = true;
        }
    }

    function deleteBook(){

        if(confirm("Do you want to go ahead and delete this book from the institute?")) {
            var ids = "";
            var checkArr = document.getElementsByClassName("selectAll");
            for(var k = 0; k < checkArr.length; k++){
                if(checkArr[k].checked){

                    ids = ids +checkArr[k].value +",";
                }
            }
            ids = ids.substring(0, ids.length-1);
            if(ids != "" && ids != undefined && ids != null){
                <g:remoteFunction controller="institute" action="removeBookFromBatchSage" params="'batchId='+batchId+'&bookId='+ids" onSuccess = "bookDeleted(data);"/>
            }
        }
        %{--if(confirm("Do you want to go ahead and delete this book from the institute?")) {--}%
        %{--<g:remoteFunction controller="institute" action="removeBookFromBatch" params="'batchId='+batchId+'&bookId='+bookId" onSuccess = "bookDeleted(data);"/>--}%
        // }
    }

    function deleteUser(userId,batchId){
        if(confirm("Do you want to go ahead and delete this user from the institute?")) {
            <g:remoteFunction controller="institute" action="removeUserFromBatch" params="'batchId='+batchId+'&userId='+userId" onSuccess = "userDeleted(data);"/>
        }
    }


    function bookDeleted(data){
        alert("Deleted Successfully");
        getBooksForBatch();
    }



    function userDeleted(data){
        alert("Deleted Successfully");
        showAllUsers();
    }


    function getIPAddressesForInstitute(){
        $("#errormsg").hide();
        $("#successmsg").hide();
        $("#batchUsers").show();
        <g:remoteFunction controller="institute" action="getIPAddresses" params="'instituteId='+instituteId" onSuccess = "showIPAddressesForInstitute(data);"/>

    }

    function showIPAddressesForInstitute(data){
        $('.loading-icon').addClass('hidden');
        var htmlStr="<h5>IPs of this Institute</h5>\n <input disabled type=\"button\" style='float: right;margin-bottom:10px' class='btn btn-danger' id=\"deleteIp\" value='Delete' onclick=\"deleteIPAddress()\"> " +
            "                    <table  class='table table-striped table-bordered'>\n" +
            "                        <tr>" +
            "                           <th><div style='display: flex;align-items: center;'> <input type=\"checkbox\" class='btn btn--light' id=\"selectAll\" value='Select All ' onchange=\"selectAll()\"><span style='margin: 0 1rem;white-space:nowrap;'>Select All</span></div></th >" +
            "                            <th>Location</th>\n" +
            "                            <th>IP address</th>\n" +
            // "                            <th>Delete</th>\n" +
            "                        </tr>\n" ;

        if(data.status=="OK"){
            var ips = data.ipAddresses;
            for(i=0;i<ips.length;i++){
                htmlStr +="<tr>" +
                    "<td style='width:140px;'><input type=\"checkbox\" onchange='ipSelectChanged();' class=\"selectAll\" id=\""+i+"\" value=\""+ips[i].ipAddress+"__"+ips[i].id+"\"></td>" +
                    "<td style='text-transform:capitalize;'>"+ips[i].name+"</td>"+
                    "<td>"+ips[i].ipAddress+"</td>"+
                    %{--+--}%
                    %{--"<td><a href='javascript:deleteIPAddress("+ips[i].id+");'><img  src='${assetPath(src: 'baseline-delete-24px.svg')}' ></a></td>"+--}%
                    "</tr>";
            }
            htmlStr +="                        \n" +
                "                    </table>";
            document.getElementById("batchUsers").innerHTML= htmlStr;
            $('#download1').show();
            $('#download').hide();
            $('#download2').hide();

        }else{
            $('#download2').hide();
            $('#download').hide();
            $('#download1').hide();
            document.getElementById("batchUsers").innerHTML= "No ip addresses added to this institute yet";
        }
        $("#batchUsers").show();
    }

    function selectAll(){
        var checkArr = document.getElementsByClassName("selectAll");
        if(document.getElementById("selectAll").checked){
            for (var k = 0; k < checkArr.length; k++) {
                checkArr[k].checked = true;
            }
            document.getElementById("deleteIp").disabled = false;
        }else{
            for (var k = 0; k < checkArr.length; k++) {
                checkArr[k].checked = false;
            }
            document.getElementById("deleteIp").disabled = true;
        }
    }

    function ipSelectChanged(){
        var ipChkSelected = false;
        var checkArr = document.getElementsByClassName("selectAll");
        if(!checkArr.checked){
            document.getElementById("selectAll").checked = false;
        }
        for(var k = 0; k < checkArr.length; k++){
            if(checkArr[k].checked){
                document.getElementById("deleteIp").disabled = false;
                ipChkSelected = true;
                break
            }
        }
        if(!ipChkSelected){
            document.getElementById("deleteIp").disabled = true;
            document.getElementById("selectAll").checked = false;
        }
    }

    function deleteIPAddress(){
        if(confirm("Do you want to go ahead and delete this IP address?")) {
            var ipAdds = "";
            var ids = "";
            var checkArr = document.getElementsByClassName("selectAll");
            for(var k = 0; k < checkArr.length; k++){
                if(checkArr[k].checked){
                    ipAdds = ipAdds +checkArr[k].value.split("__")[0] +",";
                    ids = ids +checkArr[k].value.split("__")[1] +",";
                }
            }
            ipAdds = ipAdds.substring(0, ipAdds.length-1);
            ids = ids.substring(0, ids.length-1);
            if(ids != "" && ids != undefined && ids != null){
                <g:remoteFunction controller="institute" action="removeIPAddress" params="'ipAdds='+ipAdds+'&ipIds='+ids" onSuccess = "ipAddressDeleted(data);"/>
            }
        }
    }

    function ipAddressDeleted(data){
        alert("Deleted Successfully");
        getIPAddressesForInstitute();

    }


    function getAllBooks(){
        <g:remoteFunction controller="wsshop" action="activeCategories" params="'publisherId='+publisherId" onSuccess = "updateLevels(data);"/>
        <g:remoteFunction controller="institute" action="getAllBatchesForInstitute" params="'instituteId='+instituteId" onSuccess = "displayBooksBatches(data);"/>

    }

    var booksLevel = "";
    var booksSyllabus = "";
    var booksGrade ="";
    var booksSubject = "";
    function levelChanged(field) {
        booksLevel = encodeURIComponent(field.value);
        $("#filteredBooks").hide();
        $("#syllabus").hide();
        $("#grade").hide();
        $("#subject").hide();
        $("#batchUsers").hide();
        $("#successmsg").hide();
            <g:remoteFunction controller="wonderpublish" action="getSyllabus"  onSuccess='updateSyllabus(data);'
             params="'level='+booksLevel"/>

    }

    function updateLevels(data){
        var levels = JSON.parse(data.activeCategories);
        var select;
        select = document.getElementById("level");
        select.options.length = 1;
        for (var i = 0; i < levels.length; i++) {
            var el = document.createElement("option");
            el.textContent = levels[i].level;
            el.value = levels[i].level;
            select.appendChild(el);
        }
    }

    function updateSyllabus(data){
        var booksSyllabusList = data.results;
        var select = document.getElementById("syllabus");
        select.options.length = 1;
        for(var i=0;i< booksSyllabusList.length; i++) {
            el = document.createElement("option");
            el.textContent = booksSyllabusList[i].syllabus;
            el.value = booksSyllabusList[i].syllabus;
            select.appendChild(el);
        }

        select.focus();
        $("#syllabus").show();
    }

    function syllabusChanged(field){
        $("#filteredBooks").hide();
        $("#grade").hide();
        $("#subject").hide();
        $("#batchUsers").hide();
        $("#successmsg").hide();
         booksSyllabus = encodeURIComponent(field.value);

        <g:remoteFunction controller="institute" action="getGradesList"  onSuccess='updateGrades(data);'
             params="'level='+booksLevel+'&syllabus='+booksSyllabus"/>
    }

    function updateGrades(data){
        $("#filteredBooks").hide();
        $("#subject").hide();
        var booksGradesList = JSON.parse(data.grades);
        var select = document.getElementById("grade");
        select.options.length = 1;
        for(var i=0;i< booksGradesList.length; i++) {
            el = document.createElement("option");
            el.textContent = booksGradesList[i].grade;
            el.value = booksGradesList[i].grade;
            select.appendChild(el);
        }

        select.focus();
        $("#grade").show();
    }

    function gradeChanged(field){
        $("#filteredBooks").hide();
        $("#batchUsers").hide();
        $("#successmsg").hide();
        booksGrade = encodeURIComponent(field.value);

        <g:remoteFunction controller="institute" action="getSubjectsList"  onSuccess='updateSubjects(data);'
             params="'level='+booksLevel+'&syllabus='+booksSyllabus+'&grade='+booksGrade"/>
    }

    function updateSubjects(data){
        var booksSubjectList = JSON.parse(data.subjects);
        var select = document.getElementById("subject");
        select.options.length = 1;
        for(var i=0;i< booksSubjectList.length; i++) {
            el = document.createElement("option");
            el.textContent = booksSubjectList[i].subject;
            el.value = booksSubjectList[i].subject;
            select.appendChild(el);
        }

        select.focus();
        $("#subject").show();
        $("#filteredBooks").show();

    }


    function getFilteredBooks(){
        $("#errormsg").hide();
        $("#successmsg").hide();
        $("#batchUsers").show();

        var booksSubject = $("#subject").val();

        <g:remoteFunction controller="institute" action="getAllBooks"
        params="'publisherId='+publisherId+'&level='+booksLevel+'&syllabus='+booksSyllabus+'&grade='+booksGrade+'&subject='+booksSubject"
       onSuccess = "showAllBooks(data);"/>

    }

    function showAllBooks(data){
        $('.loading-icon').addClass('hidden');
        if(data.status=="OK") {
            var htmlStr = "<div><input disabled type=\"button\" style='float: right' class='btn btn-danger' id=\"deleteBook\" value='Add' onclick=\"addmultipleBook(this)\"> <h5 class=\"liad my-4\">eBooks of this institute</h5>\n </div>" +
                "                    <table class='table table-striped table-bordered'>\n" +
                "           <thead class='bg-primary text-white text-center'>              <tr>\n" +
                "                           <th><div style='display: flex;align-items: center;'> <input type=\"checkbox\" class='btn btn--light' id=\"selectAll\" value='Select All ' onchange=\"selectAllBooks()\"><span style='margin: 0 1rem;'>Select All</span></div></th >" +
                "                            <th>Book Id</th>\n" +
                "                            <th>Title</th>\n" +
                "                            <th>Isbn</th>\n" +
                "                            <th>Status</th>\n" +
                "                            <th>Language</th>\n" +
                "                            <th>Publisher</th>\n" +
                "                            <th>Free / Paid</th>\n" +
                "                            <th>Subject</th>\n" +
                "                        </tr> </thead>\n";
        }
        if(data.status=="OK"){
            var books = data.books;
            for(i=0;i<books.length;i++){
                htmlStr +="<tr>" +
                    "<td><input type=\"checkbox\" onchange='bookSelectChanged(this);' class=\"selectAll\" value="+books[i].id+"></td>" +
                    "<td style='text-transform:capitalize;'>"+books[i].id+"</td>"+
                    "<td>"+books[i].title+"</td>"+
                    "<td>"+books[i].isbn+"</td>"+
                    "<td>"+books[i].status+"</td>"+
                    "<td>"+books[i].language+"</td>"+
                    "<td>"+books[i].name+"</td>"+
                    "<td>"+books[i].freePaid+"</td>"+
                    "<td>"+books[i].subject+"</td>"+
                    "</tr>";
            }
            htmlStr +="                        \n" +
                "                    </table>";
            document.getElementById("batchUsers").innerHTML= htmlStr;
            $('#batchUsers table').DataTable( {
                "ordering": true
            });
            $('#download').hide();
            $('#download2').hide();
            $('#download1').hide();
        }else{
            $('#download2').hide();
            $('#download').hide();
            $('#download1').hide();
            document.getElementById("batchUsers").innerHTML= "No eBooks added to this institute yet";
        }
        $("#batchUsers").show();
    }

    var bookIdArr = [];
    var ids = "";
    var mbookIdArr = [];
    function addmultipleBook(field) {
        var bookIdsStr ="";
        for(var i=0;i<bookIdArr.length;i++){
            bookIdsStr = bookIdsStr + bookIdArr[i] + ",";
        }
        bookIdsStr = bookIdsStr.substring(0, bookIdsStr.length-1)
                var bookId = document.getElementById("bookId").value;
                var bookIdMethod=true;
                <g:remoteFunction controller="institute" action="addBooks" params="'instituteId='+instituteId+'&bookIds='+bookIdsStr+'&bookIdMethod='+bookIdMethod+'&publisherId='+publisherId" onSuccess = "bookAdded(data);"/>
    }



</script>


<script>
    $('#download-btn').on('click', function() {
        window.location.href = "/institute/getBooksForBatch?download=true&batchId="+batchId;
    });
    $('#download-btn1').on('click', function() {
        window.location.href = "/institute/getIPAddresses?download=true&instituteId="+instituteId;
    });
    $('#download-btn2').on('click', function() {
        window.location.href = "/institute/showAllUsersForBatch?download=true&instituteId="+instituteId+"&userType=student&batchId="+batchId;
    });
    $('#download-userwithclasses').on('click', function() {
        window.location.href = "/institute/showAllUsersForInstituteWithClass?download=true&instituteId="+instituteId;
    });
    $('#downloadInsDtl').on('click', function() {
        window.location.href = "/institute/downloadInstituteDetails?publisherId="+publisherId;
    });
    $('#manageInsDtl').on('click', function() {
        if(instituteId!=0){
            window.open("/institute/manageInstitutePage?instituteId="+instituteId, '_blank') ;
        }else{
            alert("Please select an institute.")
        }
    });
    $('#manageClasses').on('click', function() {
        if(instituteId!=0){
            window.open("/institute/manageClasses?instituteId="+instituteId, '_blank') ;
        }else{
            alert("Please select an institute.")
        }
    });
    $('#manageInstructors').on('click', function() {
        if(instituteId!=0){
            window.open("/institute/userManagement?userType=Instructors&accessMode=users&instituteId="+instituteId , '_blank') ;
        }else{
            alert("Please select an institute.")
        }
    });
    $(function() {
        $('#institutes').change(function(){
            $("#addBooks,#addUsers,#batchUsers,#addIPs,#addIPsRange,#addInstituteAdmin,#addUserLoginLimit,#bookUsers").hide();
        });
    });


</script>
<script src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.20/js/dataTables.bootstrap4.min.js"></script>

<script>
    $(function () {
        $( "#userloginlimit" ).change(function() {
            var max = parseInt($(this).attr('max'));
            var min = parseInt($(this).attr('min'));
            if ($(this).val() > max)
            {
                $(this).val(max);
            } else
            if ($(this).val() < min)
            {
                $(this).val(min);
            }
        });
    });
    function loadUserStats(){
        <g:remoteFunction controller="institute" action="getUserStats" params="'instituteId='+instituteId" onSuccess = "showUserStatsData(data);"/>
        <%if(wsSite){%>
        <g:remoteFunction controller="institute" action="getAllBatchesForInstitute" params="'instituteId='+instituteId" onSuccess = "displayBatches(data);"/>
        <%}%>
    }

    function displayBatches(data){
        var batches = data.batches;
        var select = document.getElementById("userBatch");
        select.options.length = 1;
        for(var i=0;i< batches.length; i++) {
            if("Default"==batches[i].name) continue;
            el = document.createElement("option");
            el.textContent = batches[i].name;
            el.value = batches[i].batchId;

            select.appendChild(el);
        }

        select.focus();
        $('#userBatch').show();
    }

    function displayBooksBatches(data){
        var batches = data.batches;
        var select = document.getElementById("booksBatch");
        select.options.length = 1;
        for(var i=0;i< batches.length; i++) {
            if("Default"==batches[i].name) continue;
            el = document.createElement("option");
            el.textContent = batches[i].name;
            el.value = batches[i].batchId;

            select.appendChild(el);
        }

        select.focus();
        $('#booksBatch').show();
    }
    var noOfUsers = -1
    function showUserStatsData(data){
        if(data.noOfUsers>-1){
            this.noOfUsers = data.noOfUsers
            document.getElementById("total-lic").innerText="Total number of licenses: "+data.noOfUsers
            document.getElementById("used-lic").innerText="Total number of licenses used: "+data.usedlicenses
            document.getElementById("pending-lic").innerText="Total number of licenses pending: "+data.pendinglicenses
        }else{
            document.getElementById("total-lic").innerText=''
            document.getElementById("used-lic").innerText=''
            document.getElementById("pending-lic").innerText=''
        }
    }

    async function uploadNotes() {
        var password=document.getElementById("institutePassword").value;
        password=password.replace(/&/g,'~');
        document.getElementById("batchId").value = batchId;
        var qImg = document.getElementById("FileInputElement").files[0];
        var classBatchId = null;
        <%if(wsSite){%>
        if(document.getElementById("userBatch").selectedIndex>0) classBatchId =document.getElementById("userBatch")[document.getElementById("userBatch").selectedIndex].value;
        <%}%>
        if(qImg==undefined){
            alert("Please upload the file to proceed.")
        }else {
            if(noOfUsers>-1 && parseInt(document.getElementById("pending-lic").innerText.toString().substring(34)) <= (await getRowCount(qImg)-1)){
                alert("No required number of licenses left.")
                return
            }
            var formData = new FormData();
            formData.append('file', qImg);
            formData.append('batchId', batchId);
            formData.append('instituteId', instituteId);
            formData.append('mode', "submit");
            formData.append('password', password);
            formData.append('classBatchId', classBatchId);
            $.ajax({
                type: 'POST',
                url: '/institute/generateUserAddByExcel',
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    alert("Added Successfully");
                    location.reload()
                },
                error: function (data) {
                    console.log("error");
                }
            });
        }
    }

     function uploadBooks() {
        var qImg = document.getElementById("FileInputElementBooks").files[0];
        if(qImg==undefined){
            alert("Please upload the file to proceed.")
        }else {
            var formData = new FormData();
            formData.append('file', qImg);
            formData.append('batchId', batchId);
            formData.append('mode', "submit");
            formData.append('instituteId', instituteId);
            $.ajax({
                type: 'POST',
                url: '/admin/addBooksToInstituteByExcel',
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    alert("Added Successfully")
                    location.reload()
                },
                error: function (data) {
                    console.log("error");
                }
            });
        }
    }

    const getRowCount=(file)=>{
        return new Promise((resolve, reject)=>{
            var reader = new FileReader();

            reader.onload = function(e) {
                var data = e.target.result;
                var workbook = XLSX.read(data, {
                    type: 'binary'
                });

                workbook.SheetNames.forEach(function(sheetName) {
                    resolve(XLSX.utils.sheet_to_row_object_array(workbook.Sheets[sheetName]).length)
                })
            };

            reader.onerror = function(ex) {
                console.log(ex);
            };

            reader.readAsBinaryString(file);
        })
    }

    const viewAccessCodes=()=>{
        <g:remoteFunction controller="institute" action="getAllAccessCodes"
            params="'instituteId='+instituteId" onSuccess = "showAccessCodes(data)"/>
    }
    const showAccessCodes=(data)=>{
        if(data.status=='OK'){
            var htmlStr = "<a class='btn btn-primary m-3' target='_blank'  href='/institute/downloadAccessCodeDetails?instituteId="+instituteId+"' style='border-width: 0px;font-size:14px;padding:0.375rem 0.75rem;' >Download Access Codes Details</a>" +
                "<table class='table table-striped table-bordered'>" +
                "<tr class=''><th class=''>Serial No.</th><th class=''>Access Code</th><th class=''>Status</th><th class=''>Username</th><th class=''>Date Created</th><th class=''>Date Redeemed</th></tr>"
            var i=0
            data.accessCodes.forEach(ac=>{
                i++
                htmlStr +="<tr class=''><td class=''>"+i+"</td><td class=''>"+ac.code+"</td><td class=''>"+(ac.status?ac.status:"")+"</td><td class=''>"+(ac.username?ac.username.split(siteId1+'_')[1]:"")+"</td><td class=''>"+new Date(ac.dateCreated).getDate()+"/"+(new Date(ac.dateCreated).getMonth()+1)+"/"+new Date(ac.dateCreated).getFullYear()+"</td><td class=''>"+(ac.dateRedeemed ? (new Date(ac.dateRedeemed).getDate()+"/"+(new Date(ac.dateRedeemed).getMonth()+1)+"/"+new Date(ac.dateRedeemed).getFullYear()):"")+"</td></tr>"
            })
            htmlStr+="</table>"
            document.getElementById('viewAccessCodes').innerHTML = htmlStr
        }else{
            document.getElementById('viewAccessCodes').innerHTML = "<p class='mx-4'>No Access Codes Found.</p>"
        }
    }
    function updateBookBatchDetailData(bookId) {
        var validity= document.getElementById(bookId+'validity').value
        var noOfLic= document.getElementById(bookId+'lic').value
        if((validity==0 || validity==null || validity=='') && (noOfLic==null || noOfLic=='')){
            alert('Please enter validity or number of licenses.')
            return
        }
        <g:remoteFunction controller="institute" action="updateBookBatchDetailData"
                          params="'batchId='+batchId+'&bookId='+bookId+'&validity='+validity+'&noOfLic='+noOfLic"
                            onSuccess="updateBookBatchDetailDataResponse(data)"/>
    }
    function updateBookBatchDetailDataResponse(data) {
        if (data.status=='OK'){
            alert('Updated Successfully.')
        }else {
            alert('Failed to update.')
        }
    }
    function getBookUsageDetailsData(bookId, batchId) {
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="wsLibrary" action="bookUsageDetailsData"
                          params="'batchId='+batchId+'&bookId='+bookId" onSuccess="bookDetailsDataRecieved(data)"/>
    }
    function bookDetailsDataRecieved(data) {
        var institutesList = document.getElementById("institutes");
        var selectedValue=institutesList[institutesList.selectedIndex].value;
        var ipRestricted = selectedValue.substring((selectedValue.indexOf('_')+1),(selectedValue.lastIndexOf('_')));
        if(data.status==='OK'){
            $('.loading-icon').addClass('hidden');
            var htmlStr="<button type='button' class='btn btn-sm btn-primary my-3' onclick='javascript:instituteAction();'>Go Back</button>";
            if(ipRestricted!="true") {
                htmlStr+="<button class='btn btn-sm btn-primary m-2' onclick='addBookToUser(" + data.bookId + "," + batchId + ")'>Add book to user</button>";
            }
            htmlStr+="<table class='table table-striped table-bordered'><tr><th>Book Id</th><th>Username</th><th>Expiry Date</th><th>Date Added</th>";
            if(ipRestricted!="true") {
                htmlStr+="<th></th>";
            }
            htmlStr+="</tr>"
            data.users.forEach(user=>{
                htmlStr+="<tr><td>"+user.bookId+"</td><td>"+(user.username.substring(user.username.indexOf('_')+1))+"</td><td>"+(user.expiryDate?new Date(user.expiryDate).toDateString():'')+"</td><td>"+(user.dateCreated?new Date(user.dateCreated).toDateString():'')+"</td>";
                if(ipRestricted!="true") {
                htmlStr+="<td><input type='hidden' id='getUsageBookId' value='"+data.bookId+"'><button class='btn btn-sm btn-primary' onclick='deleteBookFromUser("+user.permissionId+")'>Remove Book</button></td>";
                }
                htmlStr+="</tr>"
            })
            htmlStr+="</table>"
            document.getElementById('batchUsers').innerHTML=htmlStr
            $("#batchUsers").show()
            $('#download').hide();
        }else{
            $('.loading-icon').addClass('hidden');
            var htmlStr = "<button type='button' class='btn btn-sm btn-primary my-3' onclick='javascript:instituteAction();'>Go Back</button>";
            if(ipRestricted!="true") {
                htmlStr+="<button class='btn btn-sm btn-primary m-2' onclick='addBookToUser(" + data.bookId + "," + batchId + ")'>Add User</button>";
            }
            htmlStr+="<br/><p>No users found for selected book.</p>"
            document.getElementById('batchUsers').innerHTML=htmlStr
            $("#batchUsers").show()
            $('#download').hide();
        }
    }
    function deleteBookFromUser(permissionId) {
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="wsLibrary" action="deleteBookFromUser" params="'permissionId='+permissionId" onSuccess="bookPermissionDeleted(data)"/>
    }
    function bookPermissionDeleted(data) {
        var bookId = document.getElementById('getUsageBookId').value;
        if(data.status==='OK'){
        	$('.loading-icon').addClass('hidden');
            alert("Book successfully removed from user's library");
            getBookUsageDetailsData(bookId, batchId);
        }
    }
    function addBookToUser( bookId, batchId) {
        $("#addBooksToUserModal").modal('show');
        $("#addBookUserBookId").val(bookId);
        $("#addBookUserBatchId").val(batchId);
    }
    function submitBookToUser() {
        var username = document.getElementById('username').value;
        var bookId = document.getElementById('addBookUserBookId').value;
        var batchId = document.getElementById('addBookUserBatchId').value;
        if(username == '') {
            alert("Please enter user email or mobile");
        } else {
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="wsLibrary" action="addBookToUserByAdmin" params="'bookId='+bookId+'&batchId='+batchId+'&username='+username" onSuccess="userAddedForBook(data)"/>
        }
    }
    function userAddedForBook(data) {
        var bookId = document.getElementById('addBookUserBookId').value;
        var batchId = document.getElementById('addBookUserBatchId').value;
        if(data.status==='OK'){
        	$('.loading-icon').addClass('hidden');
            alert("Book added successfully to user\'s library");
            $("#addBooksToUserModal").modal('hide');
            getBookUsageDetailsData(bookId, batchId);
        }else if(data.status=='Failed'){
        	$('.loading-icon').addClass('hidden');
            alert(data.message);
            $("#addBooksToUserModal").modal('hide');
            getBookUsageDetailsData(bookId, batchId);
        }
    }
    window.onload = function () {
        if("${session["siteId"]}"==='24'){
            document.getElementById('insname').placeholder = "Add Organization name"
            document.getElementById('contactName').placeholder = "Contact person name"
            document.getElementById('contactEmail').placeholder = "Contact person email id"
        }
    }

    function onlyNumberKey(evt) {
        // Only ASCII charactar in that range allowed
        var ASCIICode = (evt.which) ? evt.which : evt.keyCode
        if (ASCIICode > 31 && (ASCIICode < 48 || ASCIICode > 57))
            return false;
        return true;
    }

    function getWaitingList() {
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="wsLibrary" action="getQueueListByBatchId" params="'batchId='+batchId" onSuccess="showAllWaitingList(data)"/>
    }
    function showAllWaitingList(data) {
        $('.loading-icon').addClass('hidden');
        var books = data.QueueBooksByBookId;
        var htmlStr="<table class='table table-striped table-bordered'>" +
            "<tr><th width='30%'>Book Title</th><th width='20%'>Name</th><th>Username</th><th width='20%'>Email</th><th>Mobile</th></tr>";
            if(books.length>0) {
                for(var i=0;i<books.length;i++){
                    htmlStr+="<tr>" +
                        "<td>"+books[i].title+"</td>" +
                        "<td>"+books[i].name+"</td>" +
                        "<td>"+(books[i].username.substring(books[i].username.indexOf('_')+1))+"</td>" +
                        "<td>"+books[i].email+"</td>" +
                        "<td>"+books[i].mobile+"</td>" +
                        "</tr>";
                }
            } else {
                htmlStr+="<tr>" +
                    "<td colspan='5' class='text-center'>No users found in waiting list.</td>" +
                    "</tr>";
            }
        htmlStr+="</table>";
        document.getElementById('batchUsers').innerHTML=htmlStr;
        $("#batchUsers").show();
    }

    function getBookWaitingListData(bookId, batchId) {
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="wsLibrary" action="getQueueListByBookIdAndBatchId"
                          params="'batchId='+batchId+'&bookId='+bookId" onSuccess="bookWaitingListDataRecieved(data)"/>
    }
    function bookWaitingListDataRecieved(data) {
        $('.loading-icon').addClass('hidden');
        var books = data.QueueBooksByBookId;
        var htmlStr="<button type='button' class='btn btn-sm btn-primary my-3' onclick='javascript:instituteAction();'>Go Back</button><table class='table table-striped table-bordered'>" +
            "<tr><th>Name</th><th>Username</th><th>Email</th><th>Mobile</th></tr>";
        if(books.length>0) {
            for(var i=0;i<books.length;i++){
                htmlStr+="<tr>" +
                    "<td>"+books[i].name+"</td>" +
                    "<td>"+(books[i].username.substring(books[i].username.indexOf('_')+1))+"</td>" +
                    "<td>"+books[i].email+"</td>" +
                    "<td>"+books[i].mobile+"</td>" +
                    "</tr>";
            }
        } else {
            htmlStr+="<tr>" +
                "<td colspan='4' class='text-center'>No users found for selected book.</td>" +
                "</tr>";
        }
        htmlStr+="</table>";
        document.getElementById('batchUsers').innerHTML=htmlStr;
        $("#batchUsers").show();
        $('#download').hide();
    }

    function getCreateSyllabus(level){

            $('#categorySyllabus').hide();

            level = encodeURIComponent(level);

            <g:remoteFunction controller="wonderpublish" action="getSyllabus"  onSuccess='initializeCreateSyllabus(data);'
             params="'level='+level"/>

    }

    function initializeCreateSyllabus(data){

        syllabusList = data.results;
        var select = document.getElementById("createSyllabus");
        select.options.length = 1;
        for(var i=0;i< syllabusList.length; i++) {
            el = document.createElement("option");
            el.textContent = syllabusList[i].syllabus;
            el.value = syllabusList[i].syllabus;
            if(selectedSyllabus!=null&&selectedSyllabus.indexOf(syllabusList[i].syllabus)>-1) {
                el.selected = "true";
            }
            select.appendChild(el);
        }

        select.focus();
        $('#categorySyllabus').show();

    }
    const downloadUploadFileSample=()=>{
        window.location.href = "/institute/downloadUserUploadSample"
    }

    $("#addBooksToUserModal").on('shown.bs.modal', function () {
        $('#username').trigger('focus');
    });

    $("#addBooksToUserModal").on('hidden.bs.modal', function () {
        $('#username').val('');
    });

    $("#accesscodegen").keyup(function () {
        $("#errormsg").hide();
        this.value = this.value.replace(/[^0-9]/g, '');
    }).on('keypress', function () {
        if(this.value.length==3) return false;
    });
</script>

</body>
</html>

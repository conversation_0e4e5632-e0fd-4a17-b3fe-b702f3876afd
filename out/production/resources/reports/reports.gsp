<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<script>
    var loggedIn=false;
</script>
<style>

.page-item.active .page-link {
    background-color: #007bff !important;
    color: #ffffff;
}
table td a {
    color: #007bff;
}
.datepicker table {
    border-collapse: unset;
}
.datepicker .datepicker-days td, .datepicker .datepicker-days th {
    width: 25px !important;
    height: 25px !important;
}
table.dataTable.dtr-inline.collapsed>tbody>tr[role="row"]>td:first-child:before, table.dataTable.dtr-inline.collapsed>tbody>tr[role="row"]>th:first-child:before {
    top: 16px !important;
}
table.dataTable>tbody>tr.child ul.dtr-details>li {
    border-bottom: none !important;
    padding: 0.2em 0 !important;
}
table.dataTable>tbody>tr.child span.dtr-title {
    min-width: 180px !important;
}
@media screen and (max-width: 767px){
    div.dataTables_wrapper div.dataTables_paginate ul.pagination {
        margin: 10px 0 30px !important;
        justify-content: center !important;
    }
    table.dataTable.nowrap th, table.dataTable.nowrap td {
        white-space: normal !important;
    }
    table.dataTable>tbody>tr.child span.dtr-title {
        min-width: 100% !important;
    }
    .form-group.col-md-6.mt-2 {
        display:flex;
    }
    .form-group.col-md-6.mt-2 button{
        max-width:100%;
    }
}

table.dataTable {
    width: 100% !important;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<asset:stylesheet href="imageoverlay.css"/>
<link href="https://cdn.datatables.net/1.10.20/css/dataTables.bootstrap4.min.css" type="text/css" rel="stylesheet">
<link href="https://cdn.datatables.net/responsive/2.2.3/css/responsive.bootstrap4.min.css" type="text/css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<div>
    <div class="container-fluid my-5 px-5 publishing_sales">
        <div  class='px-0' id="bookdtl" style="min-height: calc(100vh - 156px);">
            <div class='col-md-12 main mx-auto p-4'>
                <h3 class="text-center mb-4"> Report</h3><br>
                <div id="content-books" class="sale">
                    <div class="form-inline align-items-end">
                        <div class="form-group col-md-3">
                            <label for="poStartDate"><strong>From date <span class="text-danger">*</span></strong></label>
                            <input type="text" class="form-control" id="poStartDate" placeholder="Any" value="<%=poStartDate!=null?poStartDate:""%>" autocomplete="off">
                        </div>
                        <div class="form-group col-md-3">
                            <label for="poEndDate"><strong>To date <span class="text-danger">*</span></strong></label>
                            <input type="text" class="form-control" id="poEndDate" placeholder="Any" value="<%=poEndDate!=null?poEndDate:""%>" autocomplete="off">
                        </div>
                        <div class="row align-items-center">
                            <div class="form-group col-8">
                                <label for="reportType"><strong>Type <span class="text-danger">*</span></strong></label>
                                <select name="reportType" class="form-control" id="reportType"><option>Select one</option>
                                    <option value="purchasedBooks">Purchased Books</option>
                                    <option value="shopingCart" >Shopping Cart</option>
                                    <option value="storeViewed" >Store Viewed Books</option>
                                    <option value="loginStoreBooks" >Store Viewed Login Books</option>
                                    <option value="libraryBooks">Purchased Library Books</option>
                                    <option value="instituteBooks">institute Book Views</option>
                                </select>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="row mt-3 ml-1">
                    <div class="form-group col-md-3">
                        <label for="siteId"><strong>Sites <span class="text-danger">*</span></strong></label>
                        <g:select id="siteId" class="form-control w-100" optionKey="id" optionValue="clientName"
                                  value="" name="siteId" from="${sitesList}" noSelection="['':'All']"/>
                    </div>
                    <div class="form-group col-md-3">
                        <label for="institute"><strong id="instLabel">Institutes</strong></label>
                        <select name="institute" class="form-control" id="institute"><option>Select one</option>
                        </select>
                    </div>
                </div>

                <div class="ml-4">
                    <p class="text-danger d-none" id="errorText" style="transition: all 0.3s ease-in">Please enter all the mandatory fields</p>
                </div>
                <div class="form-group col-md-6 mt-2">
                    <button type="button" id="search-btn" onclick="reportSearch()" class="btn btn-lg btn-primary col-3">Search</button>
                    <button type="button" id="download-btn" onclick="reportDownload()" class="btn btn-lg btn-outline-secondary col-3">Download</button>
                </div>
                <div class="col-md-12 mt-5">
                    <table id="salesData" class="table table-striped table-bordered dt-reponsive nowrap" style="display: none;">
                        <thead>
                        <tr class="bg-primary text-white">
                            <th>Username </th>
                            <th>Name </th>
                            <th>Book Title</th>
                            <th>Book Id</th>
                            <th>Publisher</th>
                            <th>Price</th>
                            <th>Count</th>
                        </tr>
                        </thead>
                    </table>
                </div>

            </div>
        </div>
    </div>
</div>
</div>
<div class="push"></div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="bootstrap.min.js"/>
<sec:ifLoggedIn>
    <script>

        $('#saleSelect').change(function() {
            localStorage.removeItem('SalesReport');
            var salesVal = $(this).val();
            localStorage.setItem("SalesReport", salesVal);
        });

        window.onload = function() {
            var SalesReport = localStorage.getItem("SalesReport");
            if(SalesReport==null){
                $('#saleSelect').val('paymentId');
            }
            else {
                $('#saleSelect').val(SalesReport);
            }
        }


    </script>

</sec:ifLoggedIn>
<script src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.20/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.2.3/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.2.3/js/responsive.bootstrap4.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<script>
    var isResponsive = false;

    var mq = window.matchMedia( "(max-width: 570px)" );
    if (mq.matches) {
        isResponsive = true;
    }
    else isResponsive = false;


    $('#salesData').hide();
    function reportSearch() {

        var isValid = reportsInputValidation();
        if (isValid){
            var reportType =$('#reportType').val();
            var url =""
            if(reportType=="purchasedBooks"){
                url="/reports/purchasedBookDetails";
            }else if(reportType=="shopingCart"){
                url="/reports/sendCartActiveDetails";
            }else if(reportType=="storeViewed"){
                url="/reports/storeBooksView";
            }else if(reportType=="loginStoreBooks"){
                url="/reports/storeBooksUsersView";
            }else if(reportType=="libraryBooks"){
                url="/reports/instituteLibraryBookDetails";
            }else if(reportType=="instituteBooks"){
                url="/reports/instituteBooksview";
            }

            if ($.fn.dataTable.isDataTable('#salesData')) {
                $('#salesData').DataTable().destroy();
            }
            $('#salesData').show();
            $('#salesData').DataTable({
                'destroy': true,
                //'processing': true,
                'searching': true,
                'ordering': false,
                'retrieve': true,
                'ajax': {
                    'url': url,
                    'type': 'GET',
                    'data': function (outData) {
                        outData.poStartDate = $('#poStartDate').val();
                        outData.poEndDate = $('#poEndDate').val();
                        outData.select = $('#saleSelect').val();
                        outData.mode="submit";
                        outData.siteId=$('#siteId').val();
                        outData.instituteId=instituteId;
                        return outData;
                    },
                    dataFilter: function (inData) {
                        return inData;
                    },
                    error: function (err, status) {
                        console.log(err);
                    },
                },
                'columns': [
                    {
                        'data': 'username',
                    },
                    {
                        'data': 'uName',
                    },
                    {
                        'data': 'bookTitle'
                    },
                    {
                        'data':'bookId'
                    },
                    {
                        'data': 'publisherName'
                    },
                    {
                        'data': 'price'
                    },
                    {
                        'data': 'viewCount'
                    },
                ],

            });
        }else{
            document.getElementById('errorText').classList.remove('d-none');
            document.getElementById('errorText').classList.add('d-flex');
            setTimeout(function (){
                document.getElementById('errorText').classList.add('d-none');
                document.getElementById('errorText').classList.remove('d-flex');
            },1600)
        }
    }

    function reportDownload(){
        var reportType =$('#reportType').val();
        var poStartDate = $('#poStartDate').val();
        var poEndDate = $('#poEndDate').val();
        var siteId=$('#siteId').val();
        var isValid = reportsInputValidation();
        var url =""
        if(reportType=="purchasedBooks"){
            url="/reports/purchasedBookDetails";
        }else if(reportType=="shopingCart"){
            url="/reports/sendCartActiveDetails";
        }else if(reportType=="storeViewed"){
            url="/reports/storeBooksView";
        }else if(reportType=="loginStoreBooks"){
            url="/reports/storeBooksUsersView";
        }else if(reportType=="libraryBooks"){
            url="/reports/instituteLibraryBookDetails";
        }else if(reportType=="instituteBooks"){
            url="/reports/instituteBooksview";
        }
        if (isValid){
            window.location.href = url+"?download=true&poStartDate="+poStartDate+"&poEndDate="+poEndDate+"&siteId="+siteId+"&instituteId="+instituteId;
        }else{
            document.getElementById('errorText').classList.remove('d-none');
            document.getElementById('errorText').classList.add('d-flex');
            setTimeout(function (){
                document.getElementById('errorText').classList.add('d-none');
                document.getElementById('errorText').classList.remove('d-flex');
            },1600)
        }
    }

    $('#poStartDate, #poEndDate').datepicker({
        format: 'dd-mm-yyyy',
        startView: 1,
        todayBtn: "linked",
        //clearBtn: true,
        autoclose: true,
        todayHighlight: true,
        orientation: "bottom auto",
        endDate: '+0d'
    });

    var sitesDropdown = document.getElementById('siteId');

    sitesDropdown.addEventListener('change',function (e){
        <g:remoteFunction controller="reports" action="institutesList" params="'siteId='+e.target.value" onSuccess="showInstitutelist(data)" />
    })


    document.getElementById('reportType').addEventListener('change',function (e){
        if (e.target.value=="instituteBooks"){
            document.getElementById('instLabel').innerHTML = 'Institutes <span class="text-danger">*</span>';
        }else{
            document.getElementById('instLabel').innerHTML = 'Institutes';
        }
    })

    var instituteId;
    function showInstitutelist(data){
        var list = data.institutes;
        var instituteHtml="";
        instituteHtml +="<option>Select one</option>";
        for (var l=0;l<list.length;l++){
            instituteId=list[l].id;
            instituteHtml +="<option value='"+list[l].id+"'>"+list[l].name+"</option>"
        }

        document.getElementById('institute').innerHTML = instituteHtml;
    }

    document.getElementById('institute').addEventListener('change',function (e){
        instituteId = e.target.value;
    })

    function reportsInputValidation(){
        var startDateVal = document.getElementById('poStartDate');
        var endDateVal = document.getElementById('poEndDate');
        var reportType = document.getElementById('reportType');
        var siteName = document.getElementById('siteId');
        var institute = document.getElementById('institute');

        if (startDateVal.value!="" && endDateVal.value!="" && (reportType.value!="" || reportType.value!="Select one") && siteName.value!=""){
            if (reportType.value=="instituteBooks" && institute.value =="Select one"){
                return false;
            }else{
                return true;
            }
        }else{
            return false;
        }
    }

</script>
</body>
</html>

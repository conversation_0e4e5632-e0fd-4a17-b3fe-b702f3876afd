---
grails:
    profile: web
    codegen:
        defaultPackage: wonderslate329
    spring:
        transactionManagement:
            proxies: false
    gorm:
        # Whether to autowire entities. 
        # Disabled by default for performance reasons.
        autowire: true
        reactor:
            # Whether to translate GORM events into Reactor events
            # Disabled by default for performance reasons
            events: false
info:
    app:
        name: '@info.app.name@'
        version: '@info.app.version@'
        grailsVersion: '@info.app.grailsVersion@'
spring:
    main:
        banner-mode: "off"
    groovy:
        template:
            check-template-location: false

# Spring Actuator Endpoints are Disabled by Default
endpoints:
    enabled: false
    jmx:
        enabled: true

---
grails:
    cors:
        enabled: false
    mime:
        disable:
            accept:
                header:
                    userAgents:
                        - Gecko
                        - WebKit
                        - Presto
                        - Trident
        types:
            all: '*/*'
            atom: application/atom+xml
            css: text/css
            csv: text/csv
            form: application/x-www-form-urlencoded
            html:
                - text/html
                - application/xhtml+xml
            js: text/javascript
            json:
                - application/json
                - text/json
            multipartForm: multipart/form-data
            pdf: application/pdf
            rss: application/rss+xml
            text: text/plain
            hal:
                - application/hal+json
                - application/hal+xml
            xml:
                - text/xml
                - application/xml
    urlmapping:
        cache:
            maxsize: 1000
    controllers:
        defaultScope: singleton
        upload:
            maxFileSize: 80214400
            maxRequestSize: 80214400
    converters:
        encoding: UTF-8
    views:
        default:
            codec: html
        gsp:
            encoding: UTF-8
            htmlcodec: xml
            codecs:
                expression: html
                scriptlets: html
                taglib: none
                staticparts: none
endpoints:
    jmx:
        unique-names: true

---
hibernate:
    cache:
        queries: false
        use_second_level_cache: true
        use_query_cache: false
        region.factory_class: org.hibernate.cache.ehcache.SingletonEhCacheRegionFactory

dataSources:
    dataSource:
        pooled: true
        jmxExport: true
        driverClassName: com.mysql.cj.jdbc.Driver
        dialect: org.hibernate.dialect.MySQL8Dialect
        username: wscontent
        password: wscontent
    wslog:
        pooled: true
        jmxExport: true
        driverClassName: com.mysql.cj.jdbc.Driver
        dialect: org.hibernate.dialect.MySQL8Dialect
        username: wslog
        password: wslog
    wscomm:
        pooled: true
        jmxExport: true
        driverClassName: com.mysql.cj.jdbc.Driver
        dialect: org.hibernate.dialect.MySQL8Dialect
        username: wscomm
        password: wscomm
    wsuser:
        pooled: true
        jmxExport: true
        driverClassName: com.mysql.cj.jdbc.Driver
        dialect: org.hibernate.dialect.MySQL8Dialect
        username: wsuser
        password: wsuser
    wsshop:
        pooled: true
        jmxExport: true
        driverClassName: com.mysql.cj.jdbc.Driver
        dialect: org.hibernate.dialect.MySQL8Dialect
        username: wsshop
        password: wsshop
    wsoutsideproducts:
        pooled: true
        jmxExport: true
        driverClassName: com.mysql.cj.jdbc.Driver
        dialect: org.hibernate.dialect.MySQL8Dialect
        username: wsoutsideproducts
        password: wsoutsideproducts

environments:
    development:
        dataSources:
            dataSource:
                dbCreate: update
                url: ****************************************************************************
            wslog:
                dbCreate: update
                url: ************************************************************************
            wscomm:
                dbCreate: update
                url: *************************************************************************
            wsuser:
                dbCreate: update
                url: *************************************************************************
            wsshop:
                dbCreate: update
                url: *************************************************************************
            wsoutsideproducts:
                dbCreate: update
                url: ************************************************************************************
        grails:
            serverURL: http://localhost:8080
    test:
        dataSource:
            dbCreate: update
            url: jdbc:h2:mem:testDb;MVCC=TRUE;LOCK_TIMEOUT=10000;DB_CLOSE_ON_EXIT=FALSE
    production:
        dataSource:
            //dbCreate: none
            //url: jdbc:h2:./prodDb;MVCC=TRUE;LOCK_TIMEOUT=10000;DB_CLOSE_ON_EXIT=FALSE
            dbCreate: update
            url: ****************************************************************
            properties:
                jmxEnabled: true
                initialSize: 5
                maxActive: 50
                minIdle: 5
                maxIdle: 25
                maxWait: 10000
                maxAge: 600000
                timeBetweenEvictionRunsMillis: 5000
                minEvictableIdleTimeMillis: 60000
                validationQuery: SELECT 1
                validationQueryTimeout: 3
                validationInterval: 15000
                testOnBorrow: true
                testWhileIdle: true
                testOnReturn: false
                jdbcInterceptors: ConnectionState
                defaultTransactionIsolation: 2 # TRANSACTION_READ_COMMITTED
        grails:
            serverURL: https://www.wonderslate.com
// Added by the Spring Security Core plugin:
grails:
    plugin:springsecurity:userLookup.:userDomainClassName: com.wonderslate.usermanagement.User
//8 hours:
server:
    session:
        timeout: 28800

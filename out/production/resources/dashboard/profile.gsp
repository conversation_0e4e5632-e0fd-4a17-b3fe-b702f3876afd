<!DOCTYPE html>
<html class="loading" lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <title>Edit Profile - Dashboard</title>
    <link rel="apple-touch-icon" href="${assetPath(src: 'landingpageImages/wsmetalogo.png')}">
    <link rel="android-touch-icon" href="${assetPath(src: 'landingpageImages/wsmetalogo.png')}"/>
    <link rel="windows-touch-icon" href="icon.png" />
    <link rel="shortcut icon" type="image/x-icon" href="${assetPath(src: 'landingpageImages/wsmetalogo.png')}">

    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Quicksand:300,400,500,700" rel="stylesheet">
    <link rel="stylesheet" href="https://maxst.icons8.com/vue-static/landings/line-awesome/line-awesome/1.3.0/css/line-awesome.min.css">

    %{--<asset:stylesheet href="dashboard/styles.css" async="true"/>--}%
    <asset:stylesheet href="dashboard/vendors/bootstrap.css" async="true"/>
    <asset:stylesheet href="dashboard/fonts/feather/style.css" async="true"/>
    <asset:stylesheet href="dashboard/vendors/extensions/pace.css" async="true"/>
    <asset:stylesheet href="dashboard/vendors/bootstrap-extended.css" async="true"/>
    <asset:stylesheet href="dashboard/vendors/colors.css" async="true"/>
    <asset:stylesheet href="dashboard/vendors/components.css" async="true"/>
    <asset:stylesheet href="dashboard/vendors/vertical-menu.css" async="true"/>
    <asset:stylesheet href="dashboard/vendors/colors/palette-gradient.css" async="true"/>
    <asset:stylesheet href="dashboard/vendors/charts/morris.css" async="true"/>
    <asset:stylesheet href="dashboard/vendors/charts/chartist.css" async="true"/>
    <asset:stylesheet href="dashboard/vendors/tables/datatables.min.css" async="true"/>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/simple-line-icons/2.4.1/css/simple-line-icons.min.css" rel="stylesheet">
    <asset:stylesheet href="dashboard/dashboard-home.css" async="true"/>
    <asset:stylesheet href="dashboard/dashboard.css" async="true"/>

    <style>
        #viewPassword:hover, #viewPassword:focus, #viewPassword:active {
            background: none;
            color: #0A72E8 !important;
            border-color: #cacfe7 !important;
        }
    #viewPassword i {
        font-size: 15px;
    }
    .user_avatar {
        position: relative;
        width: 200px;
        margin: 0 auto;
        background: #D5D5D5;
    }
    .image-overlay {
        position: absolute;
        right: 0;
        left: 0;
        background: rgba(153, 153, 153, 0.6);
        width: 200px;
        margin: 0 auto;
        font-size: 20px;
        color: #212121;
        opacity: 0;
        visibility: hidden;
    }
    .image-overlay:hover {
        color: #212121;
    }
    .user_avatar:hover .image-overlay {
        opacity: 1;
        visibility: visible;
    }
    </style>
</head>
<body class="vertical-layout vertical-compact-menu content-detached-right-sidebar menu-expanded fixed-navbar" data-open="click" data-menu="vertical-compact-menu" data-col="content-detached-right-sidebar">

<!-- fixed-top-->
<nav class="header-navbar navbar-expand-md navbar navbar-with-menu navbar-without-dd-arrow fixed-top navbar-light navbar-shadow navbar-brand-center">
    <div class="navbar-wrapper">
        <div class="navbar-header">
            <ul class="nav navbar-nav flex-row">
                <li class="nav-item mobile-menu d-md-none mr-auto"><a class="nav-link nav-menu-main menu-toggle hidden-xs" href="#"><i class="ft-menu font-large-1"></i></a></li>
                <li class="nav-item"><a class="navbar-brand" href="#"><asset:image src="landingpageImages/wsmetalogo.png" class="brand-logo"/>
                    <h3 class="brand-text">Wonderslate</h3></a></li>
                <li class="nav-item d-md-none"><a class="nav-link open-navbar-container" data-toggle="collapse" data-target="#navbar-mobile"><i class="la la-ellipsis-v"></i></a></li>
            </ul>
        </div>
        <div class="navbar-container content">
            <div class="collapse navbar-collapse" id="navbar-mobile">
                <ul class="nav navbar-nav mr-auto float-left">
                    <li class="nav-item d-none d-md-block"><a class="nav-link nav-menu-main menu-toggle hidden-xs" href="#"><i class="ft-menu"></i></a></li>
                </ul>
                <ul class="nav navbar-nav float-right">
                    <li class="dropdown dropdown-user nav-item"><a class="dropdown-toggle nav-link dropdown-user-link" href="#" data-toggle="dropdown"><span class="mr-1">Hello,<span class="user-name text-bold-700">Veeramani</span></span><span class="avatar avatar-online"><asset:image src="dashboard/profile-img.jpg"/> <i></i></span></a>
                        <div class="dropdown-menu dropdown-menu-right">
                            <a class="dropdown-item" href="#"><i class="ft-user"></i> Edit Profile</a>
                            <a class="dropdown-item" href="#"><i class="ft-power"></i> Logout</a>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</nav>

<div class="main-menu menu-fixed menu-light menu-accordion menu-shadow">
    <div class="main-menu-content">
        <ul class="navigation navigation-main" id="main-menu-navigation" data-menu="menu-navigation">
            <li class="nav-item"><a href="/dashboard"><i class="las la-home"></i><span class="menu-title">Dashboard</span></a></li>
            <li class="nav-item"><a href="/content"><i class="las la-file-alt"></i><span class="menu-title">Content</span></a>
                <ul class="menu-content">
                    <li><a class="menu-item" href="/publishing-desk">Publishing Desk</a></li>
                    <li><a class="menu-item" href="/wonderpublish/manageTabs">Manage Tabs</a></li>
                    <li><a class="menu-item" href="/wonderpublish/manageExams">Test Scoring Templates</a></li>
                    <li><a class="menu-item" href="/content">Last Used Books</a></li>
                </ul>
            </li>
            <li class="nav-item"><a href="/content-creators"><i class="las la-user-edit"></i><span class="menu-title">Creators</span></a></li>
            <li class="nav-item"><a href="/sales"><i class="las la-dollar-sign"></i><span class="menu-title">Sales</span></a></li>
            <li class="nav-item"><a href="/classes"><i class="las la-school"></i><span class="menu-title">Classes</span></a></li>
            <li class="nav-item"><a href="/teachers"><i class="las la-user-tie"></i><span class="menu-title">Teachers</span></a></li>
            <li class="nav-item"><a href="/students"><i class="las la-graduation-cap"></i><span class="menu-title">Students</span></a></li>
            <li class="nav-item"><a href="/users"><i class="las la-users"></i><span class="menu-title">Users</span></a></li>
            <li class="nav-item"><a href="reports"><i class="las la-file-export"></i><span class="menu-title">Reports</span></a></li>
        </ul>
    </div>
</div>

<div class="app-content content">
    <div class="content-wrapper">
        <div class="content-header row">
            <div class="content-header-left col-md-6 col-12 mb-2">
                <h3 class="content-header-title mb-0">Edit Your Profile</h3>
                <div class="row breadcrumbs-top">
                    <div class="breadcrumb-wrapper col-12">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/dashboard">Home</a></li>
                            <li class="breadcrumb-item active">Profile</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        <div class="content-detached content-left">
            <div class="content-body">
                <section class="row">
                    <div class="col-sm-12">
                        <div class="card">
                            <div class="card-content">
                                <div class="card-body">

                                    <form id="updateProfile" class="form mb-2">
                                        <div class="form-body">
                                            <h4 class="form-section border-light" style="font-weight: 500"><i class="ft-user"></i> Your Profile Information</h4>
                                            <div class="row align-items-end">
                                                <div class="form-group col-md-6 col-12">
                                                    <h5 for="fullName">Name</h5>
                                                    <input type="text" id="fullName" class="form-control" placeholder="Enter your full name" value="Veeramani">
                                                </div>
                                                <div class="form-group col-md-6 col-12">
                                                    <h5 for="phoneNumber">Phone Number</h5>
                                                    <input type="tel" id="phoneNumber" class="form-control" placeholder="Enter your phone number" value="8220162456">
                                                </div>
                                                <div class="form-group col-md-6 col-12">
                                                    <h5 for="emailAddress">Email Address</h5>
                                                    <input type="email" id="emailAddress" class="form-control" placeholder="Enter your email id" value="<EMAIL>">
                                                </div>
                                                <div class="form-group col-md-6 col-12">
                                                    <h5 for="yourPassword">Password</h5>
                                                    <div class="input-group">
                                                        <input type="text" id="yourPassword" class="form-control border-right-0" value="••••••••••••">
                                                        <div class="input-group-append">
                                                            <button class="btn btn-outline-info border-left-0 form-control" type="button" id="viewPassword"><i class="ft-eye-off"></i> <i class="ft-eye d-none"></i></button>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-group col-md-6 col-12">
                                                    <h5>State</h5>
                                                    <select class="form-control">
                                                        <option value="" selected="">Select here</option>
                                                        <option value="">Andhra Pradesh</option>
                                                        <option value="">Arunachal Pradesh</option>
                                                        <option value="">Assam</option>
                                                        <option value="">Bihar</option>
                                                    </select>
                                                </div>
                                                <div class="form-group col-md-6 col-12">
                                                    <h5>District</h5>
                                                    <select class="form-control">
                                                        <option value="" selected="">Select here</option>
                                                    </select>
                                                </div>
                                                <div class="form-group col-12">
                                                    <input class="btn btn-info white mr-1" type="submit" value="Save Changes">
                                                    <input class="btn btn-outline-secondary" type="reset" value="Cancel">
                                                </div>
                                            </div>
                                        </div>
                                    </form>

                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
        <div class="sidebar-detached sidebar-right sidebar-sticky">
            <div class="sidebar">
                <div class="sidebar-content card d-none d-lg-block">
                    <div class="text-center">
                        <div class="card-body">
                            <div class="user_avatar rounded-circle height-200 d-flex justify-content-center align-items-center mb-2">
                                <img src="/assets/dashboard/profile-img.jpg" class="user_avatar rounded-circle height-200" alt="User Profile Image">
                                <a href="#" class="image-overlay rounded-circle height-200 width-200 d-flex justify-content-center align-items-center">Change Photo</a>
                            </div>
                            <h4 class="card-title">Veeramani R</h4>
                            <h6 class="card-subtitle text-muted">Marketing Head</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<footer class="footer footer-static footer-light navbar-border navbar-shadow">
    <p class="clearfix blue-grey lighten-2 text-center mb-0 px-2">Copyright &copy; 2020 Wonderslate Technologies, All rights reserved.</p>
</footer>

<asset:javascript src="dashboard/vendors/vendors.min.js"/>

<asset:javascript src="dashboard/vendors/ui/jquery.sticky.js"/>

<asset:javascript src="dashboard/menu.js"/>
<asset:javascript src="dashboard/index.js"/>

<script>
    $(document).ready(function() {

        // Sticky sidebar
        if($(".sidebar-sticky").length){
            var headerNavbarHeight,
                footerNavbarHeight;

            if($("body").hasClass('content-right-sidebar')) {
                headerNavbarHeight = $('.header-navbar').height();
                footerNavbarHeight = $('footer.footer').height();
            }
            else{
                headerNavbarHeight = $('.header-navbar').height()+24;
                footerNavbarHeight = $('footer.footer').height()+10;
            }

            $(".sidebar-sticky").sticky({
                topSpacing: headerNavbarHeight,
                bottomSpacing: footerNavbarHeight
            });
        }
    });
</script>
</body>
</html>

<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>

<g:render template="/${session['entryController']}/navheader_new"></g:render>


<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />

<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
</style>

<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container"><br>
    <div class="d-flex justify-content-between align-items-center">
        <button onclick="window.history.back();" class="btn btn-secondary">Back</button>
        <h2 class="text-center flex-grow-1">Batch Details</h2>
    </div><br>
    <h3>Batch: ${"Default".equals(batch.name)?"Institution":batch.name}</h3>
    <h4>Course: ${batch.courseName}</h4>
    <h4>Grade: ${batch.grade}</h4>
    <hr/>

    <!-- Tabs -->

    <ul class="nav nav-tabs" role="tablist">
        <li  class="nav-item ">
            <a href="#students" class="active nav-link" aria-controls="students" role="tab" data-toggle="tab">Users</a>
        </li>
        <li  class="nav-item">
            <a href="#books" class="nav-link" aria-controls="books" role="tab" data-toggle="tab">Books</a>
        </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content">
        <!-- Students Tab -->
        <div role="tabpanel" class="tab-pane active" id="students">
            <br/>
            <div class="text-right">
                <g:link action="assignUserToBatch" params="[batchId: batch.id]" class="btn btn-primary">Add Users</g:link>
            </div>
            <table class="table table-striped">
                <thead>
                <tr>
                    <th>Name</th>
                    <th>Role</th>
                    <th>Actions</th>
                </tr>
                </thead>
                <tbody>
                <g:if test="${students?.size() > 0}">
                    <g:each in="${students}" var="student">
                        <tr>
                            <td>${student.name}</td>
                            <td>${student.userType}</td>
                            <td>
                                <g:link action="removeUserFromBatch" params="[id: student.id]" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to remove this student?');">Remove</g:link>
                            </td>
                        </tr>
                    </g:each>
                </g:if>
                <g:else>
                    <tr>
                        <td colspan="3" class="text-center">No students assigned to this batch.</td>
                    </tr>
                </g:else>
                </tbody>
            </table>
        </div>

        <!-- Instructors Tab -->


        <!-- Books Tab -->
        <div role="tabpanel" class="tab-pane" id="books">
            <br/>
    <%if(!"Default".equals(batch.name)){%>
            <div class="text-right">
                <g:link action="assignBookToBatch" params="[batchId: batch.id]" class="btn btn-primary">Assign Books</g:link>
            </div>
    <%}%>
            <table class="table table-striped">
                <thead>
                <tr>
                    <th>Book Title</th>
                    <th>Actions</th>
                </tr>
                </thead>
                <tbody>
                <g:if test="${books?.size() > 0}">
                    <g:each in="${books}" var="book">
                        <tr>
                            <td>${book.bookTitle}</td>
                            <td>
                                <g:link action="removeBookFromBatch" params="[id: book.id]" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to remove this book?');">Remove</g:link>
                            </td>
                        </tr>
                    </g:each>
                </g:if>
                <g:else>
                    <tr>
                        <td colspan="3" class="text-center">No books assigned to this batch.</td>
                    </tr>
                </g:else>
                </tbody>
            </table>
        </div>
    </div>
</div>
<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>


</body>
</html>

<style>

/* Additional styling as needed */
.table td, .table th {
    vertical-align: middle;
}
.truncate {
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.webLinkurl{
    text-overflow: ellipsis;
    width: 30ch;
    display: block;
    overflow: hidden;
    white-space: nowrap;
}
</style>


<script src="https://cdnjs.cloudflare.com/ajax/libs/marked/13.0.1/marked.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.4.0/highlight.min.js"></script>
<div class="mx-0 my-3 px-3 col-12 col-md-10 mx-auto fadein-animated" id="aira" style="display: none">
    <div class='row' >
        <div class='main'>
            <div id="content-books">
                <div class="form-group">
                    <div class="container" style="min-width: 768px">
                        <!-- Extension Functionality -->
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <!-- Chrome Extension Link -->
                                <button class="btn btn-sm btn-success" onclick="window.open('https://chromewebstore.google.com/search/iBookGPT%20RA', '_blank')">Chrome Extension</button>&nbsp;&nbsp;
                            <!-- Generate Extension Code Button -->
                                <button id="generateExtensionCode" class="btn btn-sm btn-danger">OTP</button>
                            </div>
                            <a href="javascript:openraTour()" id="toggleraLink">How to use Research Assistant?</a>
                        </div>

                        <!-- Display Extension Code -->
                        <div id="extensionCodeContainer" class="mt-3" style="display: none;">
                            <div class="alert alert-success" role="alert">
                                Your OTP is: <strong id="extensionCode"></strong>
                            </div>
                        </div>

                        <div class="howtousera" style="display:none;margin-top: 14px;">
                            <div>
                                <p><strong>Install iBookGPT RA</strong></p>
                                <ul>
                                    <li>Click the "Chrome Extension" button to install RA from the Chrome Web Store.</li>
                                    <li>After installation, pin the extension for quick and easy access.</li>
                                </ul>
                            </div>
                            <div>
                                <p><strong>Login</strong></p>
                                <p>Here's how to set it up</p>
                                <ul>
                                    <li>Click the OTP button to generate a one-time password.</li>
                                    <li>Open the extension on any website and enter the generated OTP.</li>
                                    <li>That's it-you're all set to interact with any website using RA!</li>
                                </ul>
                            </div>

                            <div>
                                <p><strong>Interactions</strong></p>
                                <p>Stay on top of your interactions</p>
                                <p>All your interactions are conveniently listed under the <a href="#" onclick="focusDiv(event, 'raInteractions')">Recent Interactions</a> section</p>
                            </div>

                        </div>

                        <!-- Summary Statistics -->
                        <h5 class="my-4">Dashboard Overview</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <div id="totalInteractionsCard" class="card text-white mb-3">
                                    <div class="card-header"  style="background-color: #0A72E8">Total Interactions</div>
                                    <div class="card-body"  style="background-color:#f0f9fa">
                                        <h5 class="card-title" id="totalInteractions">0</h5>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div id="pagesVisitedCard" class="card text-white bg-success mb-3">
                                    <div class="card-header"  style="background-color: #0A72E8">Pages Visited</div>
                                    <div class="card-body" style="background-color: #f0f9fa">
                                        <h5 class="card-title" id="pagesVisited">0</h5>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Interactions Table -->
                        <div id="raInteractions">
                            <h5 class="my-4">Recent Interactions</h5>
                            <div class="table-responsive">
                                <table class="table table-hover" id="interactionTable">
                                    <thead>
                                    <tr style="background-color: #f0f9fa">
                                        <th>Webpage</th>
                                        <th>User Query</th>
                                        <th>AI Response</th>
                                        <th>Date</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <!-- Interactions will be loaded here via AJAX -->
                                    </tbody>
                                </table>
                            </div>
                            <!-- Pagination Controls -->
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center" id="paginationControls">
                                    <!-- Pagination items will be added here -->
                                </ul>
                            </nav>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>
</div>


<script type="text/javascript">

        var currentPage = 1;
        var max = 10;
        var totalPages = 1;


        function loadDashboardData() {
            //loader
            $('.loading-icon').removeClass('hidden');
            $.ajax({
                url: '${createLink(controller: 'webInteraction', action: 'loadDashboardData')}',
                data: {
                    pageNum: currentPage,
                    max: max
                },
                success: function(data) {
                    $('.loading-icon').addClass('hidden');
                    $('#totalInteractions').text(data.totalInteractions);
                    $('#pagesVisited').text(data.pagesVisited);

                    // Clear existing interactions
                    $('#interactionTable tbody').empty();

                    // Append interactions to the table
                    if(data.totalInteractions>0) {
                        appendInteractions(data.interactions);

                        // Update total pages
                        totalPages = Math.ceil(data.totalInteractions / max);

                        // Update pagination controls
                        updatePaginationControls();
                    } else {
                        $('#interactionTable tbody').append('<tr><td colspan="4" class="text-center">No interactions found.</td></tr>');
                    }

                    $("#aira").show();

                },
                error: function() {
                    alert('An error occurred while loading dashboard data.');
                }
            });
        }


        function appendInteractions(interactions) {
            interactions.forEach(function(interaction) {
                var row = $('<tr>');

                var webpageLink = $('<a>', {
                    href: '${createLink(controller: 'webInteraction', action: 'pageInteractions')}' + '?webpageUrl=' + encodeURIComponent(interaction.webpageUrl),
                    text: interaction.webpageUrl,
                    target: '_blank',
                    class: 'webLinkurl'
                });

                var aiHtml  = marked.parse(interaction.aiResponse)

                function truncateHtml(content, length) {
                    var plainText = $(content).text()
                    return plainText.length > length ? plainText.substring(0, length) + '...' : plainText;
                }

                var truncatedContent = truncateHtml(aiHtml, 70);

                row.append($('<td>').append(webpageLink));
                row.append($('<td>').addClass('truncate').text(interaction.userQuery));
                row.append($('<td>').addClass('truncate').html(truncatedContent));
                row.append($('<td>').text(interaction.timestamp));

                $('#interactionTable tbody').append(row);
            });
        }

        function updatePaginationControls() {
            var pagination = $('#paginationControls');
            pagination.empty();

            // Previous button
            var prevItem = $('<li>', { class: 'page-item' + (currentPage === 1 ? ' disabled' : '') });
            var prevLink = $('<a>', { class: 'page-link', href: '#', text: 'Previous' });
            prevLink.on('click', function(e) {
                e.preventDefault();
                if (currentPage > 1) {
                    currentPage--;
                    loadDashboardData();
                }
            });
            prevItem.append(prevLink);
            pagination.append(prevItem);

            // Page numbers
            var startPage = Math.max(1, currentPage - 2);
            var endPage = Math.min(totalPages, currentPage + 2);
            for (var i = startPage; i <= endPage; i++) {
                var pageItem = $('<li>', { class: 'page-item' + (i === currentPage ? ' active' : '') });
                var pageLink = $('<a>', { class: 'page-link', href: '#', text: i });
                pageLink.on('click', (function(page) {
                    return function(e) {
                        e.preventDefault();
                        currentPage = page;
                        loadDashboardData();
                    };
                })(i));
                pageItem.append(pageLink);
                pagination.append(pageItem);
            }

            // Next button
            var nextItem = $('<li>', { class: 'page-item' + (currentPage === totalPages ? ' disabled' : '') });
            var nextLink = $('<a>', { class: 'page-link', href: '#', text: 'Next' });
            nextLink.on('click', function(e) {
                e.preventDefault();
                if (currentPage < totalPages) {
                    currentPage++;
                    loadDashboardData();
                }
            });
            nextItem.append(nextLink);
            pagination.append(nextItem);
        }


        // Generate Extension Code
        $('#generateExtensionCode').on('click', function(e) {
            //show loading icon
            $('.loading-icon').removeClass('hidden');
            e.preventDefault();
            $.ajax({
                url: '${createLink(controller: 'raaccess', action: 'generateCode')}',
                success: function(data) {
                    $('.loading-icon').addClass('hidden');
                    $('#extensionCode').text(data.code);
                    $('#extensionCodeContainer').show();



                    // Add copy icon
                    var copyIcon = $('<i>', {
                        class: 'fa fa-copy',
                        style: 'margin-left: 10px; cursor: pointer;'
                    });
                    copyIcon.on('click', function() {
                        var $temp = $("<input>");
                        $("body").append($temp);
                        $temp.val($('#extensionCode').text()).select();
                        document.execCommand("copy");
                        $temp.remove();
                        alert('Copied to clipboard');
                    });
                    $('#extensionCode').after(copyIcon);
                },
                error: function() {
                    alert('An error occurred while generating the extension code.');
                }
            });
        });

        function openraTour(){
            var content = $(".howtousera")
            var link =  $("#toggleraLink")
            content.slideToggle("slow", function (){
                if (content.is(":visible")) {
                    link.text("Close");
                } else {
                    link.text("How to use Research Assistant?");
                }
            })
        }
</script>



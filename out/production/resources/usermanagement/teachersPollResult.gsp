<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/books/navheader_new"></g:render>

<link rel="stylesheet" href="/assets/wonderslate/teachersForm.css">
<link rel="stylesheet" id="webmcqStyle" href="/assets/prepJoy/prepjoyWebsites/leaderBoard.css">
<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>
<asset:javascript src="sharer.min.js"/>
<asset:stylesheet href="wonderslate/shareContent.css" async="true"/>
<%
    String serverURL = request.getScheme()+"://"+request.getServerName()+
            ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                    "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                    request.getServerPort())
%>

<body>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<div class="container p-4 mt-5">
    <div class="lb-head text-center">
        <h1><strong>Poll Results</strong></h1>
    </div>
</div>
<div class="container topc" style="margin-top: 45px">
    <div class="top3">

    </div>
</div>


<div class="container mb-5" id="nextList">

</div>

<div class="container text-center pb-5">
    <a href="/usermanagement/nominations" class="btn outline-btn" target="_blank">View All Nominations</a>
</div>
<div class="modal fade teacher-profile-modal modal-modifier" id="profile" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog  modal-dialog-centered  modal-dialog-zoom">
        <div class="modal-content modal-content-modifier" >

            <button type="button" class="close close-icon d-flex justify-content-end mr-4" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <div class="modal-body modal-body-modifier text-center p-4">
                <div>
                    <img class="card-img">
                </div>
                <div class="p-3">
                    <h3 class="ttName"></h3>
                    <p class="ttSchl"></p>
                    <div class="d-flex align-items-center justify-content-center">
                        <p class="uCity"></p>
                        <p class="uState"></p>
                    </div>
                    <p class="ttNmd"></p>

                </div>
            </div>

        </div>
    </div>
</div>

<div id="favTeacherPage">

</div>

<g:render template="/books/footer_new"></g:render>

<script>
    //LEADERBOARD
    var currentDate = new Date().toISOString().split("T")[0];
    var nextLeaders = [];
    var topLeaders =[];
    var dataValues;

    function getDailRank(){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="usermanagement" action="getTeachersNomineeDetails" onSuccess="dailyRankUI(data)" />
    }


    getDailRank()
    function dailyRankUI(data){
        $('.loading-icon').addClass('hidden');
        if (data !=='no ranks'){
            dataValues = data;

            var leaderBoardData = data.teachersNomineeList;
            var lbHtml = "";
            var tHtml = "";

            nextLeaders = leaderBoardData.splice(3);
            topLeaders = leaderBoardData;

            for (var i=0;i<nextLeaders.length;i++){

                lbHtml +="<div class='d-flex align-items-center lb-wrapper mb-3' onclick='openProfile("+(i+4)+")'>\n"+
                    "<div class='d-flex align-items-center t-position'>\n"+
                    "<h3>#</h3>\n"+
                    "<h3>"+(i+4)+"</h3>\n"+
                    "</div>";
                lbHtml += "<div class='lb-content'>\n"+
                    "<div class='lb-content__img-wrapper'>";
                if(nextLeaders[i].teacherImage !=null && nextLeaders[i].teacherImage !="" && nextLeaders[i].teacherImage!=undefined){
                    lbHtml +="<img src='/usermanagement/showTeachersImage?id="+nextLeaders[i].id+"&fileName="+nextLeaders[i].teacherImage+"'>";
                }else{
                    lbHtml +="<img src='/assets/landingpageImages/img_avatar3.png'>";
                }
                lbHtml +=  "</div>";

                lbHtml += "<div class='lb-content__list'>";
                lbHtml +="<div>\n"+
                    "<h3>"+nextLeaders[i].teacherName+"</h3>";
                if (nextLeaders[i].schoolName !=undefined && nextLeaders[i].schoolName !=null){
                    lbHtml +=  "<p>"+nextLeaders[i].schoolName+"</p>";
                }

                lbHtml += "</div>";
                lbHtml += "<div class='lb-content__list-votes text-center'>\n"+
                    "<p>"+nextLeaders[i].voteCount+"</p>\n"+
                    "<p>Votes</p>\n"+
                    "</div>";
                lbHtml+="</div>";
                lbHtml+=" </div>";
                lbHtml+=" </div>";

            }


            for(var t=0;t<topLeaders.length;t++){
                tHtml +="<div class='item' id='item-"+(t+1)+"' onclick='openProfile("+(t+1)+")'>\n"+
                    "<div class='pos'>"+(t+1)+"</div>";
                if(topLeaders[t].teacherImage !=null && topLeaders[t].teacherImage !="" && topLeaders[t].teacherImage!=undefined){
                    tHtml +="<div class='pic' style='background-image: url(/usermanagement/showTeachersImage?id="+topLeaders[t].id+"&fileName="+topLeaders[t].teacherImage+")'></div>";
                }else{
                    tHtml +="<div class='pic' style='background-image: url(/assets/landingpageImages/img_avatar3.png)'></div>";
                }

                tHtml +="<div class='name'>"+topLeaders[t].teacherName+"</div>";

                if (topLeaders[t].schoolName !=null &&  topLeaders[t].schoolName != undefined){
                    tHtml +="<div class='schl'>"+topLeaders[t].schoolName+"</div>";
                }
                tHtml +=   "<div class='score'>"+topLeaders[t].voteCount+"</div>";
                tHtml += "<div class=''>\n"+
                    "<img src='/assets/prepJoy/"+(t+1)+".svg' alt='' class='badgeImg'>\n"+
                    "</div>\n"+
                    "</div>";
            }

            document.querySelector('#nextList').innerHTML = lbHtml;
            document.querySelector('.top3').innerHTML = tHtml;
            $('.top3').css('margin-bottom','20px');
        }else{
            var tHtml = "";
            tHtml +="<div class='d-flex flex-column justify-content-center align-items-center'><h2>Not many leaders?</h2>\n"+
                "<p><a href='/usermanagement/teachersNomineeForm'>Nominate your favourite teacher now.</a></p></div>";
            document.querySelector('.list').innerHTML = "";
            document.querySelector('.top3').innerHTML = tHtml; // can be changed
            $('.top3').css('margin-bottom','75px');
        }

        $("#item-1").addClass('one');
        $("#item-2").addClass('two');
        $("#item-3").addClass('three');
    }

    $( window ).on( "load", function (){
        $('.loading-icon').removeClass('hidden');
    });
    $( document ).ready(function() {
        $('.loading-icon').addClass('hidden');
    });

    function openProfile(id){
        if (id > 3){
            var selectedData = nextLeaders[id-4];
        }else{
            var selectedData = topLeaders[id-1];
        }

        if (selectedData.teacherImage !=null && selectedData.teacherImage!="" && selectedData.teacherImage != undefined){
            $('.card-img').attr("src","/usermanagement/showTeachersImage?id="+selectedData.id+"&fileName="+selectedData.teacherImage+"");
        }else{
            $('.card-img').attr("src","/assets/landingpageImages/img_avatar3.png");
        }
        $('.ttName').text(selectedData.teacherName);
        $('.ttNmd').text("Nominated By :"+selectedData.userName);
        if (selectedData.schoolName != undefined){
            $('.ttSchl').text(selectedData.schoolName);
        }

        $('#voteBtnProfile').attr('href','/usermanagement/nomineeDetails?nomineeId='+selectedData.id);
        $('#voteBtnProfile').attr('target','_blank');
        $('.vc').text(selectedData.voteCount);
        $('.uCity').text(selectedData.userCity+",");
        $('.uState').text(" "+selectedData.userState);

        $('#profile').modal('show')
    }
</script>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/books/navheader_new"></g:render>

<link rel="stylesheet" href="/assets/wonderslate/teachersForm.css">
<link rel="stylesheet" id="webmcqStyle" href="/assets/prepJoy/prepjoyWebsites/leaderBoard.css">
<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>
<asset:javascript src="sharer.min.js"/>
<asset:stylesheet href="wonderslate/shareContent.css" async="true"/>

<%
    String serverURL = request.getScheme()+"://"+request.getServerName()+
            ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                    "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                    request.getServerPort())
%>

<body>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<section class="section about-section gray-bg row" id="about">
    <div class="col-md-8 mx-auto">
        <h1 style="text-align: left" class="headerContent"><strong style="color: #30BAC6">India's <span style="color: #F79420">Favourite</span> Teachers</strong></h1>
    </div>
    <div class="container col-md-8 mx-auto mt-3 allDetails" style="display: none">
        <div class="row mb-2">
            <div class="col-lg-7">
                <div class="about-text go-to">
                    <h3 class="dark-color" id="teachersName"></h3>
                    <h6 class="theme-color lead" id="schoolName"></h6>
                    <div class="row about-list">
                        <div class="col-md-12">
                            <div class="media">
                                <label>Nominated By</label>
                                <p id="nominatedBy"></p>
                            </div>
                            <div class="media">
                                <label>Place </label>
                                <p id="place"></p>
                            </div>
                            <div class="media media-comment">
                                <label>Student's Comment</label>
                                <p id="comment"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-5">
                <div class="about-avatar d-flex justify-content-lg-start">
                    <img src="" title="" alt="" id="teacherImg">
                </div>
            </div>
        </div>
        <div class="counter">
            <div class="row align-items-center justify-content-center">
                <div class="col-6 col-lg-4 p-0">
                    <div class="count-data text-center">
                        <h6 class="count h2" data-to="500" data-speed="500" id="voteCount"></h6>
                        <p class="m-0px font-w-600">Total Votes</p>
                    </div>
                </div>
                <div class="col-6 col-lg-4 pl-0">
                    <div class="count-data text-center">
                        <a href="/usermanagement/nominations" class="btn outline-btn" target="_blank">View All Nominations</a>
                    </div>
                </div>
            </div>
            <div class="d-flex justify-content-center align-items-center flex-column text-center mt-4">
                <div class="d-flex align-items-center">
                    <h3 class="shareText">Get your classmates and friends to vote</h3>
                    <button class="btn outline-btn shareBtn-mob d-lg-none ml-2" id="shareBtn-mob" onclick="shareLink()"><i class="fa-solid fa-share-nodes mr-2"></i></button>
                </div>

                <button class="btn outline-btn mt-3 d-lg-block d-none" id="shareBtn" onclick="shareLink()" style="width: 120px;"><i class="fa-solid fa-share-nodes mr-2"></i>Share</button>
            </div>

        </div>
    </div>
</section>

<!-- Modal -->
<div class="modal modal-modifier fade" id="shareContentModal" tabindex="-1" role="dialog" aria-labelledby="shareContentModalCenterTitle">
    <div class="modal-dialog modal-dialog-modifier modal-dialog-centered modal-dialog-share-content"  role="document">
        <div class="modal-content modal-content-modifier modal-share-content">

            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <!-- Modal body -->
            <div class="modal-body modal-body-modifier d-flex align-items-center justify-content-center flex-column">
                <i class="material-icons-round text-success p-4" style="font-size: 50px;">check_circle_outline</i>
                <p class="linkShown" id="linkGenerated"></p>
                <h5 class="text-success pb-3 text-center" id="shareMsg">Get your classmates and friends to vote</h5>
                <a href="javascript:copyLink()" style="text-decoration: none" id="cpy" class="mb-4">Copy Link </a>
                <div id="shareLinksContainer">
                    <a id="fbShare" data-sharer="facebook" target="_blank"><img class="p-2" src="${assetPath(src: 'ws/sharefb.svg')}"></a>
                    <a id="lnShare" data-sharer="linkedin" target="_blank"><img class="p-2" src="${assetPath(src: 'ws/linkedinshare.svg')}"></a>
                    <a id="twShare" data-sharer="twitter"  target="_blank"><img class="p-2" style="width:60px; height:60px;" src="${assetPath(src: 'ws/twShare.svg')}"></a>
                    <a id="waShare" data-sharer="whatsapp" target="_blank"><img class="p-2" src="${assetPath(src: 'ws/whatsappshare.svg')}"></a>
                </div>

                <div class="d-flex justify-content-end col-12 py-3 mt-3">
                    <button type="button" class="btn btn-lg btn-secondary btn-secondary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect" data-dismiss="modal" aria-label="Close">Close</button>
                </div>

            </div>
        </div>
    </div>
</div>

<div class="container p-4 mt-5">
    <div class="lb-head text-center">
        <h1><strong>Poll Results</strong></h1>
    </div>
</div>
<div class="container topc" style="margin-top: 45px;display: none">
    <div class="top3">

    </div>
</div>


<div class="container nList mb-5 pb-5" id="nextList" style="display: none">

</div>

<div class="modal fade teacher-profile-modal modal-modifier" id="profile" data-keyboard="false">
    <div class="modal-dialog  modal-dialog-centered  modal-dialog-zoom">
        <div class="modal-content modal-content-modifier" >

            <button type="button" class="close close-icon d-flex justify-content-end mr-4" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <div class="modal-body modal-body-modifier text-center p-4">
                <div>
                    <img class="card-img">
                </div>
                <div class="p-3">
                    <h3 class="ttName"></h3>
                    <p class="ttSchl"></p>
                    <div class="d-flex align-items-center justify-content-center">
                        <p class="uCity"></p>
                        <p class="uState"></p>
                    </div>
                    <p class="ttNmd"></p>
                </div>
            </div>

        </div>
    </div>
</div>

<div id="favTeacherPage">

</div>

<g:render template="/books/footer_new"></g:render>

<script>
    var nomineeId = "${params.nomineeId}";
    var URLink;

    function getNomineeDetails(){
        $(".loading-icon").removeClass("hidden");
        <g:remoteFunction controller="usermanagement" action="getNomineeDetailsById" params="'nomineeId='+nomineeId" onSuccess="showSummaryData(data)" />
    }

    getNomineeDetails()
    function  showSummaryData(data){
        if (data.status=="Fail"){
            $(".loading-icon").removeClass("hidden");
            window.location.href = '/usermanagement/teachersNomineeForm'
        }else{
            $(".loading-icon").addClass("hidden");
            $('#teachersName').text(data.teacherName);
            $('#schoolName').text(data.schoolName);
            $('#nominatedBy').text(data.studentName);
            $('#place').text(data.studentCity);
            $('#comment').text(data.description);
            $('#voteCount').text(data.voteCount);
            $('title').text("India's Favourite Teacher - "+data.teacherName);
            if (data.teacherPhoto !=null){
                $('#teacherImg').attr("src","/usermanagement/showTeachersImage?id="+data.id+"&fileName="+data.teacherPhoto+"");
            }else{
                $('.about-avatar').removeClass('d-flex').addClass('d-none');
                $('#teacherImg').hide();
            }
            $('.allDetails,.nList,.topc').show();
        }
    }

    function vote(){
        $(".loading-icon").removeClass("hidden");
        if(getCookie("favTeacherVoted_${params.nomineeId}")){
            swal({
                title: "Already Voted!",
                button: "Ok",
            });
            $(".loading-icon").addClass("hidden");
            $('#voteBtn').text('Already Voted');
        }else{
            <g:remoteFunction controller="usermanagement" action="voteByNomineeId" params="'nomineeId='+nomineeId" onSuccess="completeVoting(data)" />
            setCookie("favTeacherVoted_${params.nomineeId}", "true");
        }

    }

    function completeVoting(data){
        $('button').css('z-index','0')
        swal({
            title: "Voted successfully!",
            icon: "success",
            buttons: {
                cancel: "OK",
                catch: {
                    text: "Share",
                    value: "share",
                },
            },
        }).then((value) => {
            switch (value) {
                case "share":
                    var link = '${serverURL}'+'/favouriteteachers?nomineeId='+nomineeId
                    openShareContentModalGeneric("Get your classmates and friends to vote",link)
                    break;

                default:

            }
        });
        $(".loading-icon").addClass("hidden");
        $('#voteBtn').text('Voted');
        getNomineeDetails();

    }

    function shareLink(){
        var link = '${serverURL}'+'/favouriteteachers?nomineeId='+nomineeId
        openShareContentModalGeneric("Get your classmates and friends to vote!",link)
    }

    function copyToClipboard (str){
        var el = document.createElement('textarea');
        el.value = str;
        el.setAttribute('readonly', '');
        el.style.position = 'absolute';
        el.style.left = '-9999px';
        document.body.appendChild(el);
        el.select();
        document.execCommand('copy');
        document.body.removeChild(el);
    }

    function copyLink(){
        $('#cpy').text('Copied!')
    }
    var appType = "${session['appType']}";
    function openShareContentModalGeneric(message, url) {
        URLink = url;
        $("#shareContentModal").modal('show');
        $('#linkToCopy').attr("value", url);
        $("#studyGroupShare").hide()
        copyToClipboard(url);


        if (appType == "" || appType == undefined || appType == null) {
            $("#fbShare").attr({
                "data-url": url,
                "data-web": true,
                "data-title": message,
            });
            $("#lnShare").attr({
                "data-url": url,
                "data-web": true,
                "data-title": message,
            });
            $("#twShare").attr({
                "data-url": url,
                "data-web": true,
                "data-title": message,
            });
            $("#waShare").attr({
                "data-url": url,
                "data-web": true,
                "data-title": message,
            });
        } else if (appType == 'android') {

            $("#fbShare").attr("onclick", "callAndroidShare(\""+message+"\",\"facebook\"," + url + ")");
            $("#lnShare").attr("onclick", "callAndroidShare(\""+message+",\"linkedin\"," + url + ")");
            $("#twShare").attr("onclick", "callAndroidShare(\""+message+"\",\"twitter\"," + url + ")");
            $("#waShare").attr("onclick", "callAndroidShare(\""+message+"\",\"whatsapp\"," + url + ")");
        } else if (appType == 'ios') {
            $("#fbShare").attr("onclick", "callIosShare(\""+message+"\",\"facebook\"," + url + ")");
            $("#lnShare").attr("onclick", "callIosShare(\""+message+"\",\"linkedin\"," + url + ")");
            $("#twShare").attr("onclick", "callIosShare(\""+message+"\",\"twitter\"," + url + ")");
            $("#waShare").attr("onclick", "callIosShare(\""+message+"\",\"whatsapp\"," + url + ")");
        }


        window.Sharer.init();
    }


    //LEADERBOARD
    var currentDate = new Date().toISOString().split("T")[0];
    var nextLeaders = [];
    var topLeaders =[];
    var dataValues;

    function getDailRank(){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="usermanagement" action="getTeachersNomineeDetails" onSuccess="dailyRankUI(data)" />
    }


    getDailRank()
    function dailyRankUI(data){
        $('.loading-icon').addClass('hidden');
        if (data !=='no ranks'){
            dataValues = data;

            var leaderBoardData = data.teachersNomineeList;
            var lbHtml = "";
            var tHtml = "";

            nextLeaders = leaderBoardData.splice(3);
            topLeaders = leaderBoardData;

            for (var i=0;i<nextLeaders.length;i++){

                lbHtml +="<div class='d-flex align-items-center lb-wrapper mb-3' onclick='openProfile("+(i+4)+")'>\n"+
                    "<div class='d-flex align-items-center t-position'>\n"+
                    "<h3>#</h3>\n"+
                    "<h3>"+(i+4)+"</h3>\n"+
                    "</div>";
                lbHtml += "<div class='lb-content'>\n"+
                    "<div class='lb-content__img-wrapper'>";
                if(nextLeaders[i].teacherImage !=null && nextLeaders[i].teacherImage !="" && nextLeaders[i].teacherImage!=undefined){
                    lbHtml +="<img src='/usermanagement/showTeachersImage?id="+nextLeaders[i].id+"&fileName="+nextLeaders[i].teacherImage+"'>";
                }else{
                    lbHtml +="<img src='/assets/landingpageImages/img_avatar3.png'>";
                }
                lbHtml +=  "</div>";

                lbHtml += "<div class='lb-content__list'>";
                lbHtml +="<div>\n"+
                    "<h3>"+nextLeaders[i].teacherName+"</h3>";
                if (nextLeaders[i].schoolName !=undefined && nextLeaders[i].schoolName !=null){
                    lbHtml +=  "<p>"+nextLeaders[i].schoolName+"</p>";
                }

                lbHtml += "</div>";
                lbHtml += "<div class='lb-content__list-votes text-center'>\n"+
                    "<p>"+nextLeaders[i].voteCount+"</p>\n"+
                    "<p>Votes</p>\n"+
                    "</div>";
                lbHtml+="</div>";
                lbHtml+=" </div>";
                lbHtml+=" </div>";

            }


            for(var t=0;t<topLeaders.length;t++){
                tHtml +="<div class='item' id='item-"+(t+1)+"' onclick='openProfile("+(t+1)+")'>\n"+
                    "<div class='pos'>"+(t+1)+"</div>";
                if(topLeaders[t].teacherImage !=null && topLeaders[t].teacherImage !="" && topLeaders[t].teacherImage!=undefined){
                    tHtml +="<div class='pic' style='background-image: url(/usermanagement/showTeachersImage?id="+topLeaders[t].id+"&fileName="+topLeaders[t].teacherImage+")'></div>";
                }else{
                    tHtml +="<div class='pic' style='background-image: url(/assets/landingpageImages/img_avatar3.png)'></div>";
                }

                tHtml +="<div class='name'>"+topLeaders[t].teacherName+"</div>";
                if (topLeaders[t].schoolName !=null &&  topLeaders[t].schoolName != undefined){
                    tHtml +="<div class='schl'>"+topLeaders[t].schoolName+"</div>";
                }
                tHtml +=  "<div class='score'>"+topLeaders[t].voteCount+"</div>";
                tHtml += "<div class=''>\n"+
                    "<img src='/assets/prepJoy/"+(t+1)+".svg' alt='' class='badgeImg'>\n"+
                    "</div>\n"+
                    "</div>";
            }

            document.querySelector('#nextList').innerHTML = lbHtml;
            document.querySelector('.top3').innerHTML = tHtml;
            $('.top3').css('margin-bottom','20px');
        }else{
            var tHtml = "";
            tHtml +="<div class='d-flex flex-column justify-content-center align-items-center'><h2>Not many leaders?</h2>\n"+
                "<p><a href='/usermanagement/teachersNomineeForm'>Nominate your favourite teacher now.</a></p></div>";
            document.querySelector('.list').innerHTML = "";
            document.querySelector('.top3').innerHTML = tHtml; // can be changed
            $('.top3').css('margin-bottom','75px');
        }

        $("#item-1").addClass('one');
        $("#item-2").addClass('two');
        $("#item-3").addClass('three');
    }

    $( window ).on( "load", function (){
        $('.loading-icon').removeClass('hidden');
    });
    $( document ).ready(function() {
        $('.loading-icon').addClass('hidden');
    });

    function openProfile(id){
        if (id > 3){
            var selectedData = nextLeaders[id-4];
        }else{
            var selectedData = topLeaders[id-1];
        }

        if (selectedData.teacherImage !=null && selectedData.teacherImage!="" && selectedData.teacherImage != undefined){
            $('.card-img').attr("src","/usermanagement/showTeachersImage?id="+selectedData.id+"&fileName="+selectedData.teacherImage+"");
        }else{
            $('.card-img').attr("src","/assets/landingpageImages/img_avatar3.png");
        }
        $('.ttName').text(selectedData.teacherName);
        $('.ttNmd').text("Nominated By :"+selectedData.userName);
        if (selectedData.schoolName != undefined){
            $('.ttSchl').text(selectedData.schoolName);
        }

        $('#voteBtnProfile').attr('href','/usermanagement/nomineeDetails?nomineeId='+selectedData.id);
        $('#voteBtnProfile').attr('target','_blank');
        $('.vc').text(selectedData.voteCount);
        $('.uCity').text(selectedData.userCity+",");
        $('.uState').text(" "+selectedData.userState);

        $('#profile').modal('show')
    }
</script>
</body>
</html>
<footer class="sage-footer">
    <div class="blue-color-hr hidden-xs hidden-sm"></div>
    <div class="row footer-wrapper">
        <div class="col-md-4 col-xs-4 footer-content-wrapper">
            <p class="footer-link-label">Resources for</p>
            <a href="/sage/studentResources?isbn=${session["isbn"]}&siteName=sage" class="footer-link">Students</a>
            <a href="/sage/instructorResources?isbn=${session["isbn"]}&siteName=sage" class="footer-link">Instructors</a>
        </div>
        <div class="col-md-4 col-xs-4 footer-content-wrapper">
            <div class="footer-content information-content">
                <p class="footer-link-label">Information</p>
                <a href="/sage/aboutus" class="footer-link">About us</a>
                <a href="/sage/faq" class="footer-link">Help</a>
                <a href="https://in.sagepub.com/en-in/sas/text-books-in" class="footer-link" target="_blank">Sales Team</a>
                <a href="http://www.sagemiles.com" class="footer-link" target="_blank">SAGE MILES</a>
            </div>
        </div>
        <div class="col-md-4 col-xs-10 col-sm-5 footer-content-wrapper">
            <div class="footer-content details-contact">
                <p class="footer-link-label">Contact us</p>
                <p class="head-office-label">HEAD OFFICE</p>
                <p class="head-office-address">SAGE Publications India Pvt Ltd</p>
                <p class="head-office-address">B 1/I-1 Mohan Cooperative Industrial Area Mathura Road, New Delhi 110 044, India</p>
                <p class="head-office-address">+91-11-4053 9222</p>
                <a href="mailto:<EMAIL>" class="head-office-email"><EMAIL></a>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="min-footer">
            <div class="">
                <a href="/sage/privacy" class="footer-link">Privacy Policy</a>
                <a href="/sage/terms" class="footer-link">Terms & Conditions</a>
            </div>
            <div class="copyright">
                <a class="copyright-footer"></a>
            </div>
        </div>
    </div>
</footer>
<g:render template="/wonderpublish/commonfooter"></g:render>
<asset:javascript src="countrypicker.js"/>
<script>
//Dynamic Year in Footer
    var strDate = new Date();
    console.log(strDate);
    var shortYear = strDate.getFullYear();
    $('.copyright-footer').html('&copy; ' +shortYear +" SAGE Publications India Pvt.Ltd.");


    function showUserLogin() {
        $('#user-login').addClass('user-login-slide');
        $('#user-registration').removeClass('user-login-slide');
        $('.user-type-selection li').removeClass('active');
        $('.sage-login-form').show();
        $('body').addClass('no-scroll');
        $('.login-overlay').show();
        $('#forgotpassword').hide();

    }

    function hideUserLogin() {
        $('.sage-input').val("").removeClass('is-correct').removeClass('has-error');
        $('.hr-text').hide();
        $('.bottom-hr').hide();
        $('.social-login').hide();
        $('#user-login, #user-registration').removeClass('user-login-slide');
        $('body').removeClass('no-scroll');
        $('.login-overlay').hide();
        $('.user-type-selection li').removeClass('active');
        $('.signup-with-email').hide();
        $('.sage-login-form-hide input').val('');
        $('.sage-login-form')[0].reset();
        $('.sage-input').removeClass('is-correct');
        $('#forgotpassword').hide();
        $('#form-error').hide();
        $('.sage-input').css({
            'border' : '1px solid #cccccc'
        });
        $('#signup-password').attr('type', 'password');
        $('#password').attr('type', 'password');
        $('.show-password').css({
            'background' : 'url(../assets/eye.png)',
            'background-size' : '100% 100%',
            'bottom' : '13px',
            'height' : '13px',
            'background-repeat' : 'no-repeat'
        });
    }

    function showRegistration() {
        $('#user-registration').addClass('user-login-slide');
        $('#user-login').removeClass('user-login-slide');
        $('.sage-login-form').hide();
        $('body').addClass('no-scroll');
        $('.login-overlay').show();
        $('#forgotpassword').hide();

    }

    function showRegisterationOptions() {
        $('.social-login').fadeIn();
        $('.hr-text').show();
        $('.bottom-hr').show();
        $('.sage-login-form').hide();
        $('.sage-login-form').fadeIn();
        $('#forgotpassword').hide();
        $('.input-error-tooltip').hide();
    }

    function showForgotPassword() {
        $('#sage-user-login-form').hide();
        $('#forgotpassword').show();
    }

    $('.user-type-selection li').click(function() {
        $('.login-overlay').show();
        $('#department').val("");
        $('#institute').val("");
        $('#course-subject').val("");
        $('#discipline-student').val("");
        $(this).addClass('active');
        $(this).siblings().removeClass('active');

        if($(this).children('a').hasClass('user-type-instructor')) {
            document.adduser.userType.value="instructor";
            $('#education-level, #student-discipline').hide();
            $('#discipline-student').val(" ");
        } else {
            document.adduser.userType.value="student";
            $('#education-level, #student-discipline').show();

        }

        if($(this).children('a').hasClass('user-type-student')) {
            $('#intrst-area, #inst-dept, #is-tute, #other-interest, #inst-course-sub').hide();
            $('#department').val(" ");
            $('#institute').val(" ");
            $('#course-subject').val(" ");
        } else {
            $('#intrst-area, #inst-dept, #is-tute, #other-interest, #inst-course-sub').show();
        }
    });

    $('#inst-int-ares').on('change', function() {
        var selectValue = $('#inst-int-ares').find(":selected").text();
        if (selectValue === "Other") {
            $('#other-interest').removeClass('hidden');
        } else {
            $('#other-interest').addClass('hidden');
        }
    });

    $('#inst-int-ares').multiselect({
        includeSelectAllOption: true,
        nonSelectedText: 'Area of Interests',
        onChange : function(option, checked) {
            if($('.sage-input-select').html()==="Area of Interests") {
                $('.sage-input-select').removeClass('multiselect-active');
            } else {
                $('.sage-input-select').addClass('multiselect-active');
            }
        }
    });

    $('.open-sage-modal').click(function() {
        $('#sageModal .sage-login-form').show();
    });

    $('select.sage-input').on('change', function() {
        $(this).addClass('multiselect-active');
    });

    function showSignUpIR() {
        $('.user-type-selection li:first-child').addClass('active');

        if($('.user-type-selection li:first-child').children('a').hasClass('user-type-instructor')) {
            $('#education-level, #student-discipline').hide();
            $('#education-level, #student-discipline')
        } else {
            $('#discipline-studen').val(" ");
        }

        showRegistration();
        showRegisterationOptions();
    }

    function showSignUpSR() {
        $('.user-type-selection li:nth-child(2)').addClass('active');
        if($('.user-type-selection li:nth-child(2)').children('a').hasClass('user-type-student')) {
            $('#intrst-area, #inst-dept, #is-tute, #other-interest, #inst-course-sub').hide();
            $('#department').val(" ");
            $('#institute').val(" ");
            $('#course-subject').val(" ");
        } else {
            $('#intrst-area, #inst-dept, #is-tute, #other-interest, #inst-course-sub').show();
        }
        showRegistration();
        showRegisterationOptions();
    }

    $(document).mouseup(function(e) {
        var loginContainer = $('.user-login');
        if (!loginContainer.is(e.target) && loginContainer.has(e.target).length === 0) {
            if($('.login-overlay').is(':visible')) {
                hideUserLogin();
            }
        } else {
            $('.login-overlay').show();
        }
    });

<%  if("loginFailed".equals(request.getParameter("mode"))){ %>
    showUserLogin();
    $("#login-failed-error").show();
<%  } else { %>
    $("#login-failed-error").hide();
<%  } %>

    function showForgotPasswordFaq() {
        $('#user-login').addClass('user-login-slide');
        $('#user-registration').removeClass('user-login-slide');
        $('.user-type-selection li').removeClass('active');
        $('body').addClass('no-scroll');
        $('.login-overlay').show();
        $('#sage-user-login-form').hide();
        $('#forgotpassword').show();
    }
    $('.user-login').click(function () {
        $('.input-error-tooltip').hide();
    });
    $('.loading-icon').addClass('hidden');
</script>
